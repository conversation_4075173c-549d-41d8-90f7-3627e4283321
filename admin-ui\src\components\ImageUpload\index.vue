<template xmlns="http://www.w3.org/1999/html">
  <div class="component-upload-image">
    <el-upload
      multiple
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :data="data"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      ref="imageUpload"
      :on-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{hide: this.fileList.length >= this.limit}"
    >
      <i class="el-icon-plus"></i>
    </el-upload>

    <!-- 上传提示 -->
    <div class="el-upload__tip" slot="tip" v-if="showTip">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join('/') }}</b> </template>
      的文件
      <template v-if="enableImageCompress"> (图片将自动压缩到{{ maxImageSize }}KB以内) </template>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      title="预览"
      width="800"
      append-to-body
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
      <div v-if="compressedInfo" style="text-align: center; margin-top: 10px; color: #67c23a;">
        {{ compressedInfo }}
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth"
import { isExternal } from "@/utils/validate"
import Sortable from 'sortablejs'

// 支持的图片类型
const SUPPORTED_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'webp', 'gif']

export default {
  name: "ImageUpload",
  props: {
    value: [String, Object, Array],
    // 上传接口地址
    action: {
      type: String,
      default: "/common/upload"
    },
    // 上传携带的参数
    data: {
      type: Object
    },
    // 图片数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["png", "jpg", "jpeg"]
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 拖动排序
    drag: {
      type: Boolean,
      default: true
    },
    // 是否启用图片压缩
    enableImageCompress: {
      type: Boolean,
      default: true
    },
    // 图片最大大小（KB）
    maxImageSize: {
      type: Number,
      default: 500 // 500KB
    },
    // 图片压缩质量（0-1）
    imageQuality: {
      type: Number,
      default: 0.8
    },
    // 图片最大宽度（像素）
    maxImageWidth: {
      type: Number,
      default: 1920
    },
    // 图片最大高度（像素）
    maxImageHeight: {
      type: Number,
      default: 1080
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      hideUpload: false,
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadImgUrl: process.env.VUE_APP_BASE_API + this.action, // 上传的图片服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      isCompressing: false,
      compressedInfo: "",
      originalFileSize: 0
    }
  },
  mounted() {
    if (this.drag) {
      this.$nextTick(() => {
        const element = document.querySelector('.el-upload-list')
        if (element) {
          Sortable.create(element, {
            onEnd: (evt) => {
              const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]
              this.fileList.splice(evt.newIndex, 0, movedItem)
              this.$emit("input", this.listToString(this.fileList))
            }
          })
        }
      })
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',')
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === "string") {
              if (item.indexOf(this.baseUrl) === -1 && !isExternal(item)) {
                item = { name: this.baseUrl + item, url: this.baseUrl + item }
              } else {
                item = { name: item, url: item }
              }
            }
            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    },
    // 是否是图片类型
    isImageType() {
      return this.fileType.some(type => SUPPORTED_IMAGE_TYPES.includes(type.toLowerCase()))
    }
  },
  methods: {
    // 检查是否是图片
    isImageFile(file) {
      if (!file) return false
      const fileName = file.name.split('.')
      const fileExt = fileName[fileName.length - 1].toLowerCase()
      return SUPPORTED_IMAGE_TYPES.includes(fileExt)
    },

    // 压缩图片
    async compressImage(file) {
      if (!this.enableImageCompress || !this.isImageFile(file)) {
        return file
      }

      try {
        this.isCompressing = true
        this.originalFileSize = file.size
        const compressedFile = await this.compressImageFile(file)
        this.isCompressing = false
        return compressedFile
      } catch (error) {
        console.error('图片压缩失败:', error)
        this.isCompressing = false
        return file
      }
    },

    // 实际的图片压缩逻辑
    compressImageFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (event) => {
          const img = new Image()
          img.onload = () => {
            // 计算压缩后的尺寸
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')

            let width = img.width
            let height = img.height

            // 如果图片尺寸超过最大尺寸，则按比例缩放
            if (width > this.maxImageWidth || height > this.maxImageHeight) {
              const ratio = Math.min(
                this.maxImageWidth / width,
                this.maxImageHeight / height
              )
              width = width * ratio
              height = height * ratio
            }

            canvas.width = width
            canvas.height = height

            // 绘制图片到canvas
            ctx.drawImage(img, 0, 0, width, height)

            // 转换为Blob
            canvas.toBlob((blob) => {
              if (!blob) {
                reject(new Error('图片压缩失败'))
                return
              }

              // 检查压缩后的大小
              if (blob.size <= this.maxImageSize * 1024) {
                const compressedFile = new File([blob], file.name, {
                  type: blob.type,
                  lastModified: Date.now()
                })
                compressedFile.compressed = true
                compressedFile.originalSize = file.size
                resolve(compressedFile)
              } else {
                // 如果仍然太大，降低质量再次压缩
                let quality = this.imageQuality
                let attempts = 0
                const maxAttempts = 5

                const tryCompress = () => {
                  canvas.toBlob((smallerBlob) => {
                    if (!smallerBlob) {
                      reject(new Error('图片压缩失败'))
                      return
                    }

                    if (smallerBlob.size <= this.maxImageSize * 1024 || attempts >= maxAttempts) {
                      const compressedFile = new File([smallerBlob], file.name, {
                        type: smallerBlob.type,
                        lastModified: Date.now()
                      })
                      compressedFile.compressed = true
                      compressedFile.originalSize = file.size
                      resolve(compressedFile)
                    } else {
                      quality = Math.max(0.1, quality - 0.1)
                      attempts++
                      setTimeout(tryCompress, 0)
                    }
                  }, file.type, quality)
                }

                tryCompress()
              }
            }, file.type, this.imageQuality)
          }
          img.onerror = () => reject(new Error('图片加载失败'))
          img.src = event.target.result
        }
        reader.onerror = () => reject(new Error('文件读取失败'))
        reader.readAsDataURL(file)
      })
    },

    // 上传前loading加载
    async handleBeforeUpload(file) {
      let isImg = false
      if (this.fileType.length) {
        let fileExtension = ""
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1).toLowerCase()
        }
        isImg = this.fileType.some(type => {
          const lowerType = type.toLowerCase()
          if (file.type.indexOf(lowerType) > -1) return true
          if (fileExtension && fileExtension.indexOf(lowerType) > -1) return true
          return false
        })
      } else {
        isImg = file.type.indexOf("image") > -1
      }

      if (!isImg) {
        this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join("/")}图片格式文件!`)
        return false
      }

      if (file.name.includes(',')) {
        this.$modal.msgError('文件名不正确，不能包含英文逗号!')
        return false
      }

      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传头像图片大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }

      // 如果是图片且需要压缩
      if (this.enableImageCompress && isImg && file.size > this.maxImageSize * 1024) {
        try {
          this.$modal.loading("正在压缩图片，请稍候...")
          const compressedFile = await this.compressImage(file)
          this.$modal.closeLoading()

          if (compressedFile.compressed) {
            const originalSize = (compressedFile.originalSize / 1024).toFixed(2)
            const compressedSize = (compressedFile.size / 1024).toFixed(2)
            this.compressedInfo = `图片已从 ${originalSize}KB 压缩到 ${compressedSize}KB`
            this.$modal.msgSuccess(this.compressedInfo)
          }

          // 替换原始文件
          Object.defineProperty(compressedFile, 'name', {
            value: file.name,
            writable: false
          })

          this.$modal.loading("正在上传图片，请稍候...")
          this.number++
          return compressedFile
        } catch (error) {
          this.$modal.closeLoading()
          this.$modal.msgError("图片压缩失败，将上传原图")
          console.error(error)
        }
      }

      this.$modal.loading("正在上传图片，请稍候...")
      this.number++
      return true
    },

    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },

    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        const uploadedFile = {
          name: res.fileName,
          url: res.fileName,
          compressed: file.compressed || false,
          originalSize: file.originalSize || file.size
        }
        this.uploadList.push(uploadedFile)
        this.uploadedSuccessfully()
      } else {
        this.number--
        this.$modal.closeLoading()
        this.$modal.msgError(res.msg)
        this.$refs.imageUpload.handleRemove(file)
        this.uploadedSuccessfully()
      }
    },

    // 删除图片
    handleDelete(file) {
      const findex = this.fileList.map(f => f.name).indexOf(file.name)
      if (findex > -1) {
        this.fileList.splice(findex, 1)
        this.$emit("input", this.listToString(this.fileList))
      }
    },

    // 上传失败
    handleUploadError() {
      this.$modal.msgError("上传图片失败，请重试")
      this.$modal.closeLoading()
      this.number--
    },

    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList)
        this.uploadList = []
        this.number = 0
        this.$emit("input", this.listToString(this.fileList))
        this.$modal.closeLoading()
        this.compressedInfo = ""
      }
    },

    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      if (file.compressed) {
        const originalSize = (file.originalSize / 1024).toFixed(2)
        const currentSize = (file.size / 1024).toFixed(2)
        this.compressedInfo = `图片已从 ${originalSize}KB 压缩到 ${currentSize}KB`
      } else {
        this.compressedInfo = ""
      }
      this.dialogVisible = true
    },

    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ""
      separator = separator || ","
      for (let i in list) {
        if (list[i].url) {
          strs += list[i].url.replace(this.baseUrl, "") + separator
        }
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : ''
    }
  }
}
</script>

<style scoped lang="scss">
/* 通过深度选择器穿透 scoped */
::v-deep .el-upload-list--picture-card .el-upload-list__item {
  width: 80px !important;
  height: 80px !important;
  position: relative;

  &::after {
    content: attr(data-compressed);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(103, 194, 58, 0.7);
    color: white;
    font-size: 10px;
    text-align: center;
    padding: 2px;
  }
}

::v-deep .el-upload--picture-card {
  width: 80px !important;
  height: 80px !important;
  line-height: 88px !important;
}

// .el-upload--picture-card 控制加号部分
::v-deep.hide .el-upload--picture-card {
  display: none;
}

// 去掉动画效果
::v-deep .el-list-enter-active,
::v-deep .el-list-leave-active {
  transition: all 0s;
}

::v-deep .el-list-enter, .el-list-leave-active {
  opacity: 0;
  transform: translateY(0);
}

.compressed-badge {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: #67c23a;
  color: white;
  border-radius: 10px;
  padding: 0 5px;
  font-size: 10px;
  line-height: 16px;
}
</style>
