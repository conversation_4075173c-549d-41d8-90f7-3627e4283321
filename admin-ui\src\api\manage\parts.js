import request from '@/utils/request'

// 查询配件列表
export function listParts(query) {
  return request({
    url: '/manage/parts/list',
    method: 'get',
    params: query
  })
}


export function listPartsJoinPurchase(query) {
  return request({
    url: '/manage/parts/listPartsJoinPurchase',
    method: 'get',
    params: query
  })
}

// 查询配件详细
export function getParts(partsId) {
  return request({
    url: '/manage/parts/' + partsId,
    method: 'get'
  })
}

// 新增配件
export function addParts(data) {
  return request({
    url: '/manage/parts',
    method: 'post',
    data: data
  })
}

export function batchAddParts(data) {
  return request({
    url: '/manage/parts/batchAddParts',
    method: 'post',
    data: data
  })
}

// 修改配件
export function updateParts(data) {
  return request({
    url: '/manage/parts',
    method: 'put',
    data: data
  })
}

// 删除配件
export function delParts(partsId) {
  return request({
    url: '/manage/parts/' + partsId,
    method: 'delete'
  })
}
