import request from '@/utils/request'

// 查询工单记录列表
export function listWorkorder(query) {
  return request({
    url: '/workorder/register/list',
    method: 'get',
    params: query
  })
}

// 查询工单记录详细
export function getWorkorder(serviceId) {
  return request({
    url: '/workorder/register/' + serviceId,
    method: 'get'
  })
}

// 新增工单记录
export function addWorkorder(data) {
  return request({
    url: '/workorder/register',
    method: 'post',
    data: data
  })
}

// 修改工单记录
export function updateWorkorder(data) {
  return request({
    url: '/workorder/register',
    method: 'put',
    data: data
  })
}

// 删除工单记录
export function delWorkorder(serviceId) {
  return request({
    url: '/workorder/register/' + serviceId,
    method: 'delete'
  })
}
