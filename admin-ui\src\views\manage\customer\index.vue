<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('客户姓名')" prop="customerNickName">
        <el-input
          v-model="queryParams.customerNickName"
          :placeholder="strConvert('请输入客户姓名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('车牌号')" prop="carPlateNumber">
        <el-input
          v-model="queryParams.carPlateNumber"
          :placeholder="strConvert('请输入车牌号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户邮箱')" prop="email">
        <el-input
          v-model="queryParams.email"
          :placeholder="strConvert('请输入客户邮箱')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('电话')" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          :placeholder="strConvert('请输入电话')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{strConvert('搜索')}}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{strConvert('重置')}}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"

          icon="el-icon-plus"

          @click="handleAdd"
          v-hasPermi="['manage:customer:add']"
        >{{strConvert('新增')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"

          icon="el-icon-edit"

          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:customer:edit']"
        >{{strConvert('修改')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"

          icon="el-icon-delete"

          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:customer:remove']"
        >{{strConvert('删除')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"

          @click="handleExport"
          v-hasPermi="['manage:customer:export']"
        >{{strConvert('导出')}}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="customerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('客户编号')" align="center" prop="customerId" />
      <el-table-column :label="strConvert('客户姓名')" align="center" prop="customerNickName" />
      <el-table-column :label="strConvert('客户电话')" align="center" prop="phonenumber" />
      <el-table-column :label="strConvert('客户邮箱')" align="center" prop="email" />
      <el-table-column :label="strConvert('用户性别')" align="center" prop="sex" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.sex"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('折扣等级')" align="center" prop="discountLevel" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_customer_discount_level" :value="scope.row.discountLevel"/>
        </template>
      </el-table-column>

<!--      <el-table-column :label="strConvert('头像地址')" align="center" prop="avatar" />
      <el-table-column :label="strConvert('密码')" align="center" prop="password" />
      <el-table-column :label="strConvert('账号状态')" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_notice_status" :value="scope.row.status"/>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('备注')" align="center" prop="remark" />
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button

            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['manage:customer:edit']"
          >{{strConvert('修改')}}</el-button>
          <el-button

            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['manage:customer:remove']"
          >{{strConvert('删除')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改客户信息对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="strConvert('客户姓名')" prop="customerNickName">
          <el-input v-model="form.customerNickName" :placeholder="strConvert('请输入客户姓名')" />
        </el-form-item>
        <el-form-item :label="strConvert('客户邮箱')" prop="email">
          <el-input v-model="form.email" :placeholder="strConvert('请输入客户邮箱')" />
        </el-form-item>
        <el-form-item :label="strConvert('车牌号')" prop="email">
          <el-input v-model="form.carPlateNumber" :placeholder="strConvert('请输入车牌号')" />
        </el-form-item>
        <el-form-item :label="strConvert('品牌')" prop="brand">
          <el-input v-model="form.brand" :placeholder="strConvert('请输入品牌')" />
        </el-form-item>
        <el-form-item :label="strConvert('颜色')" prop="color">
          <el-input v-model="form.color" :placeholder="strConvert('请输入颜色')" />
        </el-form-item>
        <el-form-item :label="strConvert('客户电话')" prop="phonenumber">
          <el-input v-model="form.phonenumber" :placeholder="strConvert('请输入客户电话')" />
        </el-form-item>
<!--        <el-form-item :label="strConvert('头像地址')" prop="avatar">
          <el-input v-model="form.avatar" :placeholder="strConvert('请输入头像地址')" />
        </el-form-item>-->
        <el-form-item :label="strConvert('用户性别')" prop="sex">
          <el-radio-group v-model="form.sex">
            <el-radio
              v-for="dict in dict.type.sys_user_sex"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="strConvert('折扣等级')" prop="discountLevel">
          <el-select v-model="form.discountLevel" :placeholder="strConvert('请选择折扣等级')">
            <el-option
              v-for="dict in dict.type.t_customer_discount_level"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item :label="strConvert('账号状态')" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_notice_status"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>-->
        <el-form-item :label="strConvert('备注')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{strConvert('确 定')}}</el-button>
        <el-button @click="cancel">{{strConvert('取 消')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'
import { listCustomer, getCustomer, delCustomer, addCustomer, updateCustomer } from "@/api/manage/customer"
import { formatDate } from '@/utils'

export default {
  name: "Customer",
  dicts: ['sys_notice_status','sys_user_sex','t_customer_discount_level'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 客户信息表格数据
      customerList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        customerNickName: null,
        carPlateNumber:null,
        email: null,
        phonenumber: null,
        sex: null,
        avatar: null,
        password: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        customerNickName: [
          { required: true, message: this.strConvert('客户姓名不能为空'), trigger: "blur" }
        ],
        phonenumber: [
          { required: true, message: this.strConvert('客户电话不能为空'), trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,
    /** 查询客户信息列表 */
    getList() {
      this.loading = true
      listCustomer(this.queryParams).then(response => {
        this.customerList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        customerId: null,
        customerName: null,
        customerNickName: null,
        customerType: null,
        carPlateNumber:null,
        brand: null,//品牌
        color: null,//颜色
        email: null,
        phonenumber: null,
        sex: "0",
        discountLevel: "0",
        avatar: null,
        password: null,
        status: null,
        delFlag: null,
        loginIp: null,
        loginDate: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.customerId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.strConvert('添加客户信息')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const customerId = row.customerId || this.ids
      getCustomer(customerId).then(response => {
        this.form = response.data
        this.open = true
        this.title = this.strConvert('修改客户信息')
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.customerId != null) {
            updateCustomer(this.form).then(response => {
              this.$modal.msgSuccess(this.strConvert('修改成功'))
              this.open = false
              this.getList()
            })
          } else {
            addCustomer(this.form).then(response => {
              this.$modal.msgSuccess(this.strConvert('新增成功'))
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const customerIds = row.customerId || this.ids
      this.$modal.confirm(this.strConvert('是否确认删除客户信息编号为"') + customerIds + this.strConvert('"的数据项？')).then(function() {
        return delCustomer(customerIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.strConvert('删除成功'))
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/customer/export', {
        ...this.queryParams
      }, `customer_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
