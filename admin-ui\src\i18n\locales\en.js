export default {
  2001: "Hello, World!",
  2002: "Maintenance Acceptance Settlement Form",
  2003: 'Problem Diagnosis',
  2004: 'User Information',
  2005: 'Image Information',
  2006: 'Work Order Details',
  2007: 'European Mazda Automobile Trading Co., Ltd.',
  2008: 'No data yet',
  2009: 'Incoming Order',
  2010: 'Purchase Information',
  2011: 'Item Details',
  2012: 'Accessories',
  2013: 'Material',
  2014: 'Operator',
  2016: 'EURO Maintenance Management Platform',
  2017: 'Language',
  2018: 'Account number',
  2019: 'Password',
  2020: 'Remember password',
  2021: 'Login',
  2022: "Work Order",
  2023: "Amount",
  2024: "Customer",
  2025: "Inventory Alert",
  2026: 'Are you sure you want to delete the data item with work order record number %s ?',
  2027: 'Are you sure you want to cancel the work order with work order number %s ? The relevant financial records will be deleted after cancellation',
  "网站": "Website",
  "未读消息": "Unread Messages",
  "快速前往": "Quick Access",
  "登记中": "Registering",
  "待分派": "Pending Assignment",
  "执行中": "In Progress",
  "等待配件": "Awaiting Parts",
  "待结算": "Pending Settlement",
  "已完结": "Completed",
  "已作废": "Voided",
  "工单标题": "Work Order Title",
  "工单编号": "Work Order Number",
  "客户姓名": "Customer Name",
  "客户编号": "Customer ID",
  "车牌号": "License Plate Number",
  "创建时间": "Creation Time",
  "快速登记": "Quick Registration",
  "登记": "Registration",
  "图片": "Image",
  "创建者": "Creator",
  "问题描述": "Problem Description",
  "操作": "Action",
  "录入信息": "Enter Information",
  "发布": "Publish",
  "修改": "Edit",
  "删除": "Delete",
  "发布工单": "Publish Work Order",
  '搜索': 'Search',
  '重置': 'Reset',
  // questionForm.vue 新增翻译
  '请输入客户姓名': 'Please enter customer name',
  '请输入工单标题': 'Please enter work order title',
  '品牌': 'Brand',
  '请输入品牌': 'Please enter brand',
  '颜色': 'Color',
  '请输入颜色': 'Please enter color',
  '客户电话': 'Customer Phone',
  '请输入客户电话': 'Please enter customer phone',
  '请输入车牌号': 'Please enter license plate number',
  '进厂日期': 'Entry Date',
  '取车日期': 'Pickup Date',
  '选择日期': 'Select Date',
  '预收金额': 'Advance Payment',
  '请输入预收金额': 'Please enter advance payment',
  '欠款金额': 'Outstanding Amount',
  '请输入再付金额': 'Please enter remaining payment',
  '总额': 'Total Amount',
  '折后金额': 'Discounted Amount',
  '余额': 'Balance',
  '请输入问题描述': 'Please enter problem description',
  '图片上传': 'Image Upload',
  '主项合计金额': 'Main Total Amount',
  '手动修改折后金额': 'Manually Modify Discounted Amount',
  '是否附加税款': 'Include Tax',
  '当前税率': 'Current Tax Rate',
  '确认提交': 'Confirm Submit',
  '服务类型': 'Service Type',
  '服务项': 'Service Item',
  '合计': 'Total',
  '定损信息录入': 'Damage Assessment Entry',
  '添加': 'Add',
  '加入': 'Join',
  '编号': 'Number',
  '问题编号': 'Issue Number',
  '问题名称': 'Issue Name',
  '请输入问题名称': 'Please enter issue name',
  '费用': 'Cost',
  '描述': 'Description',
  '客户姓名不能为空': 'Customer name cannot be empty',
  '标题不能为空': 'Title cannot be empty',
  '客户电话不能为空': 'Customer phone cannot be empty',
  '预收款不能为空': 'Advance payment cannot be empty',
  '车牌号不能为空': 'License plate number cannot be empty',
  '受理': 'Accepted',
  '开启': 'Enable',
  '关闭': 'Disable',
  '确认要"': 'Confirm to "',
  '手动修改折扣金额': 'manually modify discount amount?',
  '含税算计价，如有折扣，建议开启含税前，先进行折扣优惠，否则将影响最终结算': 'The price is calculated including tax. If there is a discount, it is recommended to apply the discount before turning on the tax, otherwise it will affect the final settlement.',
  '至少应选择一项服务': 'At least one service should be selected',
  '修改成功': 'Modified successfully',
  '添加成功': 'Added successfully',
  '请先选择要删除的工单详情数据': 'Please select work order details to delete first',
  '问题信息不完整 请检查后再试': 'Issue information is incomplete, please check and try again',
  '添加的项目:': 'Added item: ',
  ' 已存在于右侧列表中 请移除后 重新再式': ' already exists in the right list, please remove and try again',
  '折扣金额': 'Discount Amount',
  '类目': 'Category',
  // receive.vue 新增翻译
  '回撤':'Retrieval',
  '回撤成功':'Retrieval successful',
  '操作后工单将退回至《受理登记》中，确认是否继续？':'After the operation, the work order will be returned to the "Acceptance Registration". Do you want to confirm whether to continue?',
  '请输入工单编号': 'Please enter work order number',
  '请输入客户编号': 'Please enter customer number',
  '开始日期': 'Start Date',
  '结束日期': 'End Date',
  '派工单登记': 'Work Order Registration',
  '派工单打印': 'Work Order Print',
  '维修服务类型': 'Repair Service Type',
  '进度': 'Progress',
  '服务': 'Service',
  '数量': 'Quantity',
  '单价': 'Unit Price',
  '操作人': 'Operator',
  '操作时间': 'Operation Time',
  '一键完成': 'One-click Complete',
  '操作完成': 'Operation Complete',
  '工单指派': 'Work Order Assignment',
  '接收车辆': 'Vehicle Reception',
  '单号为"': 'Order number "',
  '"的工单，确认后此订单将由': '" work order, after confirmation this order will be maintained by ',
  '来维护，是否确认？': ', confirm?',
  '指派成功': 'Assignment successful',
  '操作后此类目将由': 'After operation, this category will be ',
  '立即完成进度，是否确认': 'immediately completed, confirm?',
  '更新成功': 'Update successful',
  // workorder/index.vue 新增翻译
  '业务进度': 'Business Progress',
  '请选择业务进度': 'Please select business progress',
  '操作人员': 'Operator',
  '总金额': 'Total Amount',
  '结算时间': 'Settlement Time',
  '单据打印': 'Document Print',
  '受理单打印': 'Acceptance Form Print',
  '查看': 'View',
  '作废': 'Void',
  '操作成功': 'Operation successful',
  // workorder/progress.vue 新增翻译
  '总进度': 'Total Progress',
  '汇总信息': 'Summary Information',
  '序号': 'Serial Number',
  '配件编号': 'Parts Number',
  '配件名称': 'Parts Name',
  '请输入配件名称': 'Please enter parts name',
  '库存': 'Stock',
  '规格': 'Specification',
  '操作描述': 'Operation Description',

  '更新 ': 'Update ',
  '更 新': 'Update',
  '添加工单': 'Add Work Order',
  '工单：': 'Work Order: ',
  ' 配件信息添加': ' Parts Information Addition',
  '进度更新不能为0%': 'Progress update cannot be 0%',
  '新增成功': 'Added successfully',
  // customer/index.vue 新增翻译
  '客户邮箱': 'Customer Email',
  '请输入客户邮箱': 'Please enter customer email',
  '电话': 'Phone',
  '请输入电话': 'Please enter phone number',

  '导出': 'Export',
  '用户性别': 'Gender',
  '折扣等级': 'Discount Level',
  '请选择折扣等级': 'Please select discount level',
  '请输入内容': 'Please enter content',
  '添加客户信息': 'Add Customer Information',
  '修改客户信息': 'Edit Customer Information',
  '是否确认删除客户信息编号为"': 'Are you sure to delete customer information number "',
  // settlement.vue 新增翻译
  '预收款': 'Advance Payment',
  '再付金额': 'Remaining Payment',
  '结算金额': 'Settlement Amount',
  '去结算': 'Go to Settlement',
  '尾款不能为空': 'Final payment cannot be empty',
  '修改工单记录': 'Edit Work Order Record',
  // workDetailForm.vue 新增翻译
  '受理信息': 'Acceptance Information',
  '含税': 'Tax Included',
  '图像集': 'Image Gallery',
  '维修服务信息录入': 'Repair Service Information Entry',
  '服务编号': 'Service Number',
  '服务名称': 'Service Name',
  '请输入服务名称': 'Please enter service name',
  '问题描述不能为空': 'Problem description cannot be empty',
  '信息不完整，请检查后再试': 'Information incomplete, please check and try again',
  // settlementForm.vue 新增翻译
  '维修服务列表': 'Repair Service List',
  '服务总计': 'Service Total',
  '维修配件列表': 'Repair Parts List',
  '配件合计': 'Parts Total',
  '结算汇总': 'Settlement Summary',
  '受理折扣金额': 'Acceptance Discount Amount',
  '已缴纳扣金额': 'Paid Amount',
  '剩余欠缴金额': 'Remaining Outstanding Amount',
  '成本汇总': 'Cost Summary',
  '维修服务成本': 'Repair Service Cost',
  '配件花费成本': 'Parts Cost',
  '合计成本': 'Total Cost',
  '结算': 'Settlement',
  '请输入结算金额': 'Please enter settlement amount',
  '利润：折扣金额 -（ 合计成本 + 剩余欠缴金额 ）': 'Profit: Discount Amount - (Total Cost + Remaining Outstanding Amount)',
  '结 算': 'Settle',
  '补交金额不能为空': 'Additional payment amount cannot be empty',
  '"的工单，确认是结算并生成收入流水？': '" work order, confirm settlement and generate income record?',
  '结算成功': 'Settlement successful',
  // progressForm.vue 新增翻译
  '维修服务总计': 'Repair Service Total',
  // infoForm.vue 新增翻译
  '统计': 'Statistics',
  '"的工单，确定是否完结？完结后将关闭该工单相关操作': '" work order, are you sure to complete it? All related operations will be closed after completion',
  '工单成功': 'Work order successful',
  '添加配件': 'Add accessories',
  '更新进度': 'Update progress',
  '采购单号': 'Purchase Order Number',
  '报销确认，同时生成《财务流水》出帐记录': 'Reimbursement confirmation, and generate financial flow debit record',
  '结算单打印': 'Settlement Print',
  '请输入采购单号': 'Please enter purchase order number',
  '请输入标题': 'Please enter title',
  '请输入创建者': 'Please enter creator',
  '请选择物件类型': 'Please select item type',
  '请输入工单号': 'Please enter work order number',
  '请输入申请单号': 'Please enter application number',
  '采购单详情信息': 'Purchase order details',
  '标题': 'Title',
  '物件类型': 'Item Type',
  '工单号': 'Work Order Number',
  '物件申请单号': 'Item Application Number',
  '审批状态': 'Approval Status',
  '审批时间': 'Approval Time',
  '总费用': 'Total Cost',
  '同步库存': 'Sync Inventory',
  '物料名称': 'Material Name',
  '说明': 'Description',
  '确认报销': 'Confirm Reimbursement',
  '生成入库单': 'Generate Inbound Order',
  '删除采购单': 'Delete Purchase Order',

  // TreeNodeDialog.vue related translations
  '树节点对话框': 'Tree Node Dialog',
  '节点名称': 'Node Name',
  '请输入节点名称': 'Please enter node name',
  '节点类型': 'Node Type',
  '请选择节点类型': 'Please select node type',
  '文件夹': 'Folder',
  '文件': 'File',
  '请输入描述信息': 'Please enter description',

  // resetPwd.vue related translations
  '请输入旧密码': 'Please enter old password',
  '请输入新密码': 'Please enter new password',
  '请确认新密码': 'Please confirm new password',

  // 新增系统管理相关翻译
  '新增': 'Add',
  '展开/折叠': 'Expand/Collapse',
  '目录': 'Directory',
  '菜单': 'Menu',
  '按钮': 'Button',
  '路由名称': 'Route Name',
  '是否外链': 'External Link',
  '是': 'Yes',
  '否': 'No',
  '路由地址': 'Route Path',
  '组件路径': 'Component Path',

  '路由参数': 'Route Parameters',
  '是否缓存': 'Cache',
  '缓存': 'Cache',
  '不缓存': 'No Cache',
  '显示状态': 'Display Status',
  '菜单状态': 'Menu Status',
  '确 定': 'Confirm',
  '取 消': 'Cancel',
  '菜单名称不能为空': 'Menu name cannot be empty',
  '菜单顺序不能为空': 'Menu order cannot be empty',
  '路由地址不能为空': 'Route address cannot be empty',
  // 代码生成相关翻译
  '生成': 'Generate',
  '创建': 'Create',
  '导入': 'Import',
  '预览': 'Preview',
  '编辑': 'Edit',
  '同步': 'Sync',
  '生成代码': 'Generate Code',
  '复制': 'Copy',
  '成功生成到自定义路径：': 'Successfully generated to custom path: ',
  '同步成功': 'Sync successful',
  '复制成功': 'Copy successful',
  '请输入表名称': 'Please enter table name',
  '请输入表描述': 'Please enter table description',
  '请输入实体类名称': 'Please enter entity class name',
  '请输入作者': 'Please enter author',
  // 用户头像相关翻译
  '选择': 'Select',
  '提 交': 'Submit',
  // 字典管理相关翻译

  '数据标签不能为空': 'Data label cannot be empty',
  '数据键值不能为空': 'Data key value cannot be empty',
  '数据顺序不能为空': 'Data order cannot be empty',
  '字典名称不能为空': 'Dictionary name cannot be empty',
  '字典类型不能为空': 'Dictionary type cannot be empty',
  '刷新缓存': 'Refresh Cache',
  '刷新成功': 'Refresh successful',
  // 角色管理相关翻译
  '添加用户': 'Add User',
  '批量取消授权': 'Batch Cancel Authorization',
  '取消授权': 'Cancel Authorization',
  '取消授权成功': 'Authorization cancelled successfully',
  '基本信息': 'Basic Information',
  '角色信息': 'Role Information',
  '提交': 'Submit',
  '返回': 'Return',
  '授权成功': 'Authorization successful',
  // 用户管理相关翻译
  '更多': 'More',
  '重置密码': 'Reset Password',
  '分配角色': 'Assign Role',
  '将文件拖到此处，或': 'Drag files here, or ',
  '点击上传': 'click to upload',
  '是否更新已经存在的用户数据': 'Update existing user data',
  '仅允许导入xls、xlsx格式文件。': 'Only xls, xlsx format files are allowed.',
  '下载模板': 'Download Template',
  '用户名称不能为空': 'Username cannot be empty',
  '用户昵称不能为空': 'User nickname cannot be empty',
  '用户密码不能为空': 'User password cannot be empty',
  '不能包含非法字符：': 'Cannot contain illegal characters: ',
  '请输入正确的邮箱地址': 'Please enter a valid email address',
  '请输入正确的手机号码': 'Please enter a valid phone number',
  '修改成功，新密码是：': 'Modified successfully, new password is: ',
  // 密码重置相关翻译
  '保存': 'Save',
  '旧密码不能为空': 'Old password cannot be empty',
  '新密码不能为空': 'New password cannot be empty',
  '长度在 6 到 20 个字符': 'Length between 6 and 20 characters',
  '确认密码不能为空': 'Confirm password cannot be empty',
  // 系统配置相关翻译
  '参数名称不能为空': 'Parameter name cannot be empty',
  '参数键名不能为空': 'Parameter key name cannot be empty',
  '参数键值不能为空': 'Parameter key value cannot be empty',
  // 角色权限相关翻译
  '权限字符': 'Permission Character',
  '全选/全不选': 'Select All/Deselect All',
  '父子联动': 'Parent-Child Linkage',
  '数据权限': 'Data Permission',
  '分配用户': 'Assign Users',
  '角色名称不能为空': 'Role name cannot be empty',
  '权限字符不能为空': 'Permission character cannot be empty',
  '角色顺序不能为空': 'Role order cannot be empty',
  // 系统问题管理相关翻译
  '注意：分成比不修改的情况下，默认为10%': 'Note: If the commission ratio is not modified, the default is 10%',
  '货币类型不能为空': 'Currency type (usd, gel) cannot be empty',
  // 岗位管理相关翻译
  '岗位名称不能为空': 'Position name cannot be empty',
  '岗位编码不能为空': 'Position code cannot be empty',
  '岗位顺序不能为空': 'Position order cannot be empty',
  // 注册相关翻译
  '注 册': 'Register',
  '注 册 中...': 'Registering...',
  '使用已有账户登录': 'Login with existing account',
  '恭喜你，您的账号 ': 'Congratulations, your account ',
  ' 注册成功！': ' has been registered successfully!',
  '系统提示': 'System Prompt',
  // 通知公告相关翻译
  '公告标题不能为空': 'Announcement title cannot be empty',
  '公告类型不能为空': 'Announcement type cannot be empty',
  // 部门管理相关翻译
  '上级部门不能为空': 'Parent department cannot be empty',
  '部门名称不能为空': 'Department name cannot be empty',
  '显示排序不能为空': 'Display order cannot be empty',
  // 个人信息相关翻译
  '个人信息': 'Personal Information',
  '用户名称': 'Username',
  '手机号码': 'Phone Number',
  '用户邮箱': 'User Email',
  '所属部门': 'Department',
  '所属角色': 'Role',
  '创建日期': 'Creation Date',
  '基本资料': 'Basic Information',
  '男': 'Male',
  '女': 'Female',
  '邮箱地址不能为空': 'Email address cannot be empty',
  '手机号码不能为空': 'Phone number cannot be empty',
  // 代码生成详细配置翻译
  '生成模板': 'Generation Template',
  '前端类型': 'Frontend Type',
  '生成包路径': 'Generation Package Path',
  '生成模块名': 'Generation Module Name',
  '生成业务名': 'Generation Business Name',
  '生成功能名': 'Generation Function Name',
  '生成代码方式': 'Code Generation Method',
  'zip压缩包': 'ZIP Archive',
  '自定义路径': 'Custom Path',
  '上级菜单': 'Parent Menu',
  '最近路径快速选择': 'Recent Path Quick Selection',
  '恢复默认的生成基础路径': 'Restore default generation base path',
  '其他信息': 'Other Information',
  '树编码字段': 'Tree Code Field',
  '树父编码字段': 'Tree Parent Code Field',
  '树名称字段': 'Tree Name Field',
  '关联信息': 'Association Information',
  '关联子表的表名': 'Associated Sub-table Name',
  '子表关联的外键名': 'Foreign Key Name for Sub-table',
  '请选择生成模板': 'Please select generation template',
  '请输入生成包路径': 'Please enter generation package path',
  '请输入生成模块名': 'Please enter generation module name',
  '请输入生成业务名': 'Please enter generation business name',
  '请输入生成功能名': 'Please enter generation function name',
  // 技师派工单相关翻译
  '技师派工单': 'Technician Work Order',
  'მიღების დრო时间:': 'Reception Time:',
  '№/工单编号:': 'Work Order No.:',
  'მფლობელი车主:': 'Vehicle Owner:',
  'ა/მ მარკა品牌:': 'Vehicle Brand:',
  'ფერი/ 颜色:': 'Color:',
  'ტელეფონი电话:': 'Phone:',
  'სახ. №车牌号:': 'License Plate:',
  'მიღ.თარ/进厂日期:': 'Entry Date:',
  'შესრ.თარ/取车日期:': 'Pickup Date:',
  'სულ总额:': 'Total Amount:',
  '财务审核ბუღალტერი:': 'Financial Auditor:',
  '总经理დირექტორი:': 'General Manager:',
  '附件信息დანართის ინფორმაცია': 'Attachment Information',
  'დამატებითი ინფორმაცია问题说明:': 'Additional Information/Problem Description:',
  '授权播放权限': 'Authorize Playback Permission',
  '播放失败': 'Playback Failed',
  '播放未读消息提醒': 'Play Unread Message Reminder',
  '未读消息提醒': 'Unread Message Reminder',
  '需要播放时': 'When Playback Is Needed',
  '中': 'Medium',
  '中文': 'Chinese',
  '密码': 'Password',
  '底部': 'Bottom',
  '录': 'Record',
  '注册开关': 'Registration Switch',
  '登': 'Login',
  '立即注册': 'Register Now',
  '记住密码': 'Remember Password',
  '语言': 'Language',
  '语言不能为空': 'Language cannot be empty',
  '请输入您的密码': 'Please enter your password',
  '请输入您的账号': 'Please enter your account',
  '请输入验证码': 'Please enter the verification code',
  '请选择': 'Please select',
  '账号': 'Account',
  '验证码': 'Verification Code',
  '验证码开关': 'Verification Code Switch',
  '两次输入的密码不一致': 'The two passwords entered do not match',
  '之间': 'Between',
  '册': 'Register',
  '和': 'And',
  '注': 'Note',
  '用户密码长度必须介于': 'User password length must be between',
  '用户账号长度必须介于': 'User account length must be between',
  '确认密码': 'Confirm Password',
  '请再次输入您的密码': 'Please enter your password again',
  '坐标轴指示器，坐标轴触发有效': 'Axis indicator, effective when triggered by the axis',
  '默认为直线，可选为:': 'Default is a straight line, options include:',
  '不再传递参数': 'No longer pass parameters',
  '添加立即执行': 'Add immediate execution',
  '直接使用转换后的数据': 'Directly use converted data',
  '确保数据存在': 'Ensure data exists',
  '近一周帐面统计': 'Weekly account statistics',
  '保留过渡效果': 'Preserve transition effects',
  '分别设置不同图标的悬停背景色': 'Set hover background colors for different icons separately',
  '原有样式': 'Original style',
  '客户': 'Customer',
  '工单': 'Work Order',
  '库存预警': 'Inventory Alert',
  '悬停时图标颜色变白': 'Icon turns white when hovering',
  '金额': 'Amount',
  '回首页': 'Back to Home',
  '对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面': 'Sorry, you do not have access permissions. Please refrain from unauthorized operations! You may return to the main page.',
  '您没有访问权限！': 'You do not have access permissions!',
  '错误': 'Error',
  '对不起，您正在寻找的页面不存在。尝试检查': 'Sorry, the page you are looking for does not exist. Try checking',
  '找不到网页！': 'Webpage Not Found!',
  '的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。': 'error, then press the refresh button on your browser or try finding other content in our application.',
  '返回首页': 'Return to Home',
  '修改按钮操作': 'Modify Button Action',
  '删除成功': 'Successfully Deleted',
  '删除按钮操作': 'Delete Button Action',
  '取': 'Cancel',
  '取消按钮': 'Cancel Button',
  '备注': 'Remarks',
  '多选框选中数据': 'Checkbox Selected Data',
  '头像地址': 'Avatar URL',
  '定': 'Confirm',
  '客户信息表格数据': 'Customer Information Table Data',
  '导出按钮操作': 'Export Button Action',
  '弹出层标题': 'Popup Title',
  '总条数': 'Total Count',
  '提交按钮': 'Submit Button',
  '搜索按钮操作': 'Search Button Action',
  '新增按钮操作': 'Add Button Action',
  '是否显示弹出层': 'Whether to Show Popup',
  '是否确认删除客户信息编号为': 'Confirm deletion of customer information with ID',
  '显示搜索条件': 'Show Search Criteria',
  '查询参数': 'Query Parameters',
  '查询客户信息列表': 'Query Customer Information List',
  '消': 'Cancel',
  '添加或修改客户信息对话框': 'Add or Modify Customer Information Dialog',
  '的数据项？': 'data item(s)?',
  '确': 'Confirm',
  '表单参数': 'Form Parameters',
  '表单校验': 'Form Validation',
  '表单重置': 'Form Reset',
  '请输入头像地址': 'Please enter the avatar URL',
  '账号状态': 'Account Status',
  '选中数组': 'Selected Array',
  '遮罩层': 'Mask Layer',
  '重置按钮操作': 'Reset Button Operation',
  '非单个禁用': 'Non-Single Disabled',
  '非多个禁用': 'Non-Multiple Disabled',
  '修改资金支出登记': 'Modify Fund Expenditure Registration',
  '关联单号': 'Associated Order Number',
  '单据类型不能为空': 'Document Type Cannot Be Empty',
  '备注时间范围': 'Remark Time Range',
  '帐变类型': 'Account Change Type',
  '帐变类型不能为空': 'Account Change Type Cannot Be Empty',
  '支出单号': 'Expenditure Order Number',
  '支出登记': 'Expenditure Registration',
  '支出说明': 'Expenditure Description',
  '支出说明不能为空': 'Expenditure Description Cannot Be Empty',
  '是否确认删除资金支出登记编号为': 'Confirm Deletion of Fund Expenditure Registration ID',
  '查询资金支出登记列表': 'Query Fund Expenditure Registration List',
  '添加或修改资金支出登记对话框': 'Add or Modify Fund Expenditure Registration Dialog',
  '添加资金支出登记': 'Add Fund Expenditure Registration',
  '用户名': 'Username',
  '请输入关联单号': 'Please Enter Associated Order Number',
  '请输入支出单号': 'Please Enter Expenditure Order Number',
  '请输入用户名': 'Please Enter Username',
  '请输入金额': 'Please Enter Amount',
  '请选择帐变类型': 'Please Select Account Change Type',
  '资金支出登记表格数据': 'Fund Expenditure Registration Table Data',
  '金额不能为空': 'Amount Cannot Be Empty',
  '今日金额总计': "Today's Total Amount",
  '修改资金流水': 'Modify Fund Flow',
  '删除标志': 'Deletion Flag',
  '单据类型': 'Document Type',
  '变动类型': 'Change Type',
  '变动类型不能为空': 'Change Type Cannot Be Empty',
  '新增流水': 'Add New Flow',
  '是否确认删除资金流水编号为': 'Confirm Deletion of Fund Flow ID',
  '月度金额总计': 'Monthly Total Amount',
  '查询资金流水列表': 'Query Fund Flow List',
  '流水号': 'Flow Number',
  '流水号不能含有字母': 'Flow Number Cannot Contain Letters',
  '添加或修改资金流水对话框': 'Add or Modify Fund Flow Dialog',
  '添加资金流水': 'Add Fund Flow',
  '用户名不能为空': 'Username Cannot Be Empty',
  '用户编号': 'User ID',
  '用户编号不能为空': 'User ID Cannot Be Empty',
  '统计资金流水': 'Statistics Fund Flow',
  '请输入删除标志': 'Please Enter Deletion Flag',
  '请输入流水号': 'Please enter the serial number',
  '请输入用户编号': 'Please enter the user ID',
  '请选择单据类型': 'Please select the document type',
  '请选择变动类型': 'Please select the change type',
  '资金流水表格数据': 'Fund flow table data',
  '修改资金收入登记': 'Modify fund income registration',
  '收入单号': 'Income document number',
  '收入登记': 'Income registration',
  '收入说明': 'Income description',
  '收入说明不能为空': 'Income description cannot be empty',
  '是否确认删除资金收入登记编号为': 'Confirm deletion of fund income registration ID:',
  '查询资金收入登记列表': 'Query fund income registration list',
  '添加或修改资金收入登记对话框': 'Add or modify fund income registration dialog',
  '添加资金收入登记': 'Add fund income registration',
  '请输入收入单号': 'Please enter the income document number',
  '资金收入登记表格数据': 'Fund income registration table data',
  '中文不能为空': 'Chinese cannot be empty',
  '俄文': 'Russian',
  '修改多语言': 'Modify multilingual settings',
  '多语言表格数据': 'Multilingual table data',
  '是否确认删除多语言编号为': 'Confirm deletion of multilingual ID:',
  '查询多语言列表': 'Query multilingual list',
  '格文': 'Georgian',
  '添加多语言': 'Add multilingual',
  '添加或修改多语言对话框': 'Add or modify multilingual dialog',
  '英文': 'English',
  '修改网站消息': 'Modify website message',
  '客户号码': 'Customer number',
  '客户沟通': 'Customer communication',
  '无': 'None',
  '是否确认删除网站消息编号为': 'Confirm deletion of website message ID:',
  '查询网站消息列表': 'Query website message list',
  '沟': 'Communication',
  '消息': 'Message',
  '消息内容': 'Message content',
  '消息内容不能为空': 'Message content cannot be empty',
  '消息标题': 'Message title',
  '消息类型': 'Message type',
  '添加或修改网站消息对话框': 'Add or modify website message dialog',
  '添加网站消息': 'Add website message',
  '添加聊天组件显示控制': 'Add chat component display control',
  '网站消息表格数据': 'Website message table data',
  '请输入客户号码': 'Please enter the customer number',
  '请输入消息': 'Please enter the message',
  '请输入消息标题': 'Please enter the message title',
  '请选择消息类型': 'Please select the message type',
  '通': 'Communication',
  '同步库存时，会根据入库单中相应的明细对库存进行增加': 'When synchronizing inventory, the corresponding details in the receipt will increase the inventory',
  '开启开关后，同步库存时，默认会将采购单采购价同步配件管理中的相应的“参考采购价”': 'After enabling the switch, during inventory synchronization, the purchase order price will be synchronized to the corresponding part in parts management by default Reference Purchase Price',
  '修改入库单': 'Modify receipt',
  '停用': 'Disable',
  '入库': 'Stock In',
  '入库单号': 'Stock In Order Number',
  '入库单打印': 'Stock In Order Print',
  '入库单表格数据': 'Stock In Order Table Data',
  '入库单详情信息': 'Stock In Order Details',
  '入库单详情删除按钮操作': 'Delete Button Operation for Stock In Order Details',
  '入库单详情序号': 'Serial Number in Stock In Order Details',
  '入库单详情添加按钮操作': 'Add Button Operation for Stock In Order Details',
  '入库单详情表格数据': 'Stock In Order Details Table Data',
  '单号为': 'Order Number is',
  '参考采购价': 'Reference Purchase Price',
  '启用': 'Enable',
  '复选框选中数据': 'Checkbox Selected Data',
  '子表选中数据': 'Sub-table Selected Data',
  '将采购价同步至配件库中吗？': 'Synchronize the purchase price to the parts library?',
  '库存同步成功': 'Inventory Synchronization Successful',
  '库存已同步的单据不得再次修改': 'Documents with synchronized inventory cannot be modified again',
  '成功': 'Success',
  '打印单据': 'Print Document',
  '是否同步采购价': 'Whether to Synchronize Purchase Price',
  '是否确认删除入库单编号为': 'Confirm deletion of stock in order number',
  '查找工单信息': 'Search Work Order Information',
  '查找物料信息': 'Search Material Information',
  '查找配件信息': 'Search Parts Information',
  '查看入库单': 'View Stock In Order',
  '查询入库单列表': 'Query Stock In Order List',
  '添加入库单': 'Add Stock In Order',
  '添加备注': 'Add Remarks',
  '添加或修改入库单对话框': 'Add or Modify Stock In Order Dialog',
  '物件类型不能为空': 'Object Type Cannot Be Empty',
  '物料编号': 'Material Number',
  '的入库单，确认是否同步库并生成支出流水？': 'stock in order, confirm synchronization to inventory and generate expenditure record?',
  '确认要': 'Confirm to',
  '请先选择要删除的入库单详情数据': 'Please first select the stock in order detail data to delete',
  '请输入入库单号': 'Please enter the stock in order number',
  '请输入数量': 'Please enter the quantity',
  '请输入物料名称': 'Please enter the material name',
  '请输入说明': 'Please enter the description',
  '请重新检查入库清单后，确保数据完整后再试': 'Please recheck the stock in list and ensure data integrity before trying again',
  '输入为空时清空历史数据': 'Clear historical data when input is empty',
  '采购价同步开关': 'Purchase Price Synchronization Switch',
  '采购价同步至配件库': 'Synchronize Purchase Price to Parts Library',
  '修改配件类型': 'Modify Parts Type',
  '是否确认删除配件类型编号为': 'Confirm deletion of parts type number',
  '查询配件类型列表': 'Query Parts Type List',
  '添加或修改配件类型对话框': 'Add or Modify Parts Type Dialog',
  '添加配件类型': 'Add Parts Type',
  '类型名称': 'Type Name',
  '请输入配件类型名称': 'Please enter the parts type name',
  '请选择创建时间': 'Please select creation time',
  '请选择配件部位': 'Please select accessory part',
  '配件类型名称': 'Accessory type name',
  '配件类型名称不能为空': 'Accessory type name cannot be empty',
  '配件类型编号': 'Accessory type code',
  '配件类型表格数据': 'Accessory type table data',
  '配件部位': 'Accessory part',
  '配件部位不能为空': 'Accessory part cannot be empty',
  '配件部位编号': 'Accessory part code',
  '修改采购单': 'Modify purchase order',
  '出帐流水生成成功': 'Expense record generated successfully',
  '审批意见': 'Approval comments',
  '报销确认': 'Reimbursement confirmation',
  '是否出帐': 'Whether to expense',
  '是否确认删除采购单编号为': 'Confirm deletion of purchase order number:',
  '查找申请单信息': 'Search application form information',
  '查看按钮操作': 'View button operation',
  '查看详情': 'View details',
  '查看采购单': 'View purchase order',
  '查询采购单列表': 'Query purchase order list',
  '注意：报销确认，同时生成《财务流水》出帐记录': 'Note: Reimbursement confirmation will simultaneously generate an "Financial Record" expense entry',
  '添加或修改采购单对话框': 'Add or modify purchase order dialog',
  '添加采购单': 'Add purchase order',
  '的采购单，确认是否生成入库单？': 'purchase order. Confirm to generate receiving order?',
  '的采购单，确认是否确认报销？报销后将生成出帐流水。': 'purchase order. Confirm reimbursement? This will generate an expense record after confirmation.',
  '统计价格': 'Calculate total price',
  '请先选择要删除的采购单详情数据': 'Please first select purchase order detail data to delete',
  '请输入单价': 'Please enter unit price',
  '请输入物件申请单号': 'Please enter item application number',
  '请选择审批时间': 'Please select approval time',
  '请选择审批状态': 'Please select approval status',
  '请重新检查采购清单后，确保数据完整后再试': 'Please recheck the purchase list and ensure data completeness before retrying',
  '采购': 'Procurement',
  '采购单表格数据': 'Purchase order table data',
  '采购单详情删除按钮操作': 'Purchase order detail delete button operation',
  '采购单详情序号': 'Purchase order detail serial number',
  '采购单详情添加按钮操作': 'Purchase order detail add button operation',
  '采购单详情表格数据': 'Purchase order detail table data',
  '采购登记': 'Purchase registration',
  '单条数据提交按钮': 'Single data submit button',
  '取消': 'Cancel',
  '库存数量': 'Stock quantity',
  '当前编辑行索引': 'Currently edited row index',
  '批量保存': 'Batch save',
  '批量删除': 'Batch delete',
  '批量提交按钮': 'Batch submit button',
  '数量不能为空': 'Quantity cannot be empty',
  '新增未保存，直接移除': 'New unsaved entry will be directly removed',
  '新增行直接进入编辑状态': 'New row enters edit mode directly',
  '是否为新增': 'Whether it is a new addition',
  '是否确认删除物料编号为': 'Confirm deletion of material number',
  '查询物料列表': 'Query material list',
  '标记为新增': 'Mark as new',
  '校验': 'Validation',
  '没有要提交的数据': 'No data to submit',
  '物料名称不能为空': 'Material name cannot be empty',
  '物料表格数据': 'Material table data',
  '编辑中的行数据': 'Row data being edited',
  '编辑还原': 'Edit revert',
  '请输入库存数量': 'Please enter inventory quantity',
  '请输入物料编号': 'Please enter material number',
  '进入编辑状态，备份原始数据以便取消时还原': 'Enter edit mode, backup original data for cancellation restoration',
  '退出编辑状态': 'Exit edit mode',
  '中性灰色（禁用': 'Neutral gray (Disabled',
  '危险': 'Danger',
  '售价比': 'Price ratio',
  '失败行）': 'Failed rows)',
  '成功状态）': 'Success status)',
  '成功绿色（已完成': 'Success green (Completed',
  '按钮不可聚焦': 'Button not focusable',
  '排序': 'Sort',
  '是否确认删除配件编号为': 'Confirm deletion of accessory number',
  '更新行）': 'Updated rows)',
  '柔和紫色（特殊标记行）': 'Soft purple (Special marked rows)',
  '查询配件列表': 'Query accessory list',
  '次要行）': 'Secondary rows)',
  '活力橙色（高优先级行）': 'Vibrant orange (High-priority rows)',
  '活跃行）': 'Active rows)',
  '添加或修改配件对话框': 'Add or modify accessory dialog',
  '清新青色（新增': 'Fresh cyan (Newly added',
  '类型编码': 'Type code',
  '警示黄色（待处理': 'Warning yellow (Pending',
  '请输入售价百分比': 'Please enter price percentage',
  '请输入排序': 'Please enter sort order',
  '请输入配件编号': 'Please enter accessory number',
  '请选择配件类型': 'Please select accessory type',
  '输入框可聚焦': 'Input field focusable',
  '配件单位': 'Accessory unit',
  '配件名称不能为空': 'Accessory name cannot be empty',
  '配件类型': 'Accessory type',
  '配件类型不能为空': 'Accessory type cannot be empty',
  '配件类型数据': 'Accessory type data',
  '配件表格数据': 'Accessory table data',
  '配件规格': 'Accessory specification',
  '错误红色（异常': 'Error red (Abnormal',
  '需注意）': 'Requires attention)',
  '预警': 'Early warning',
  '预警库存': 'Warning inventory',
  '高亮蓝色（当前选中': 'Highlight blue (Currently selected',
  '审批': 'Approval',
  '审批意见不能为空': 'Approval comments cannot be empty',
  '审批状态不能为空': 'Approval status cannot be empty',
  '审批通过成功': 'Approval successful',
  '审批通过添加提醒': 'Add reminder after approval',
  '审批驳回成功': 'Rejection successful',
  '待审批': 'Pending approval',
  '是否确认删除物件申请编号为': 'Confirm deletion of item application number:',
  '是否确认审批通过？通过后，将同步扣减相应库存？': 'Confirm approval? Upon approval, corresponding inventory will be deducted.',
  '未提交': 'Not submitted',
  '查看并审批': 'View and approve',
  '查询物件申请列表': 'Query item application list',
  '注意：审批通过后，将会扣减申请单中相关物件对应的库存': 'Note: After approval, inventory for related items in the application will be deducted.',
  '添加或修改物件申请对话框': 'Add or modify item application dialog',
  '添加物件申请': 'Add item application',
  '物件申请审批': 'Item application approval',
  '物件申请表格数据': 'Item application table data',
  '物件申请详情': 'Item application details',
  '物件申请详情信息': 'Item application detail information',
  '物件申请详情序号': 'Item application detail serial number',
  '物件申请详情表格数据': 'Item application detail table data',
  '申请人': 'Applicant',
  '申请单号': 'Application number',
  '请输入审批意见': 'Please enter approval comments',
  '审批成功': 'Approval successful',
  '此处添加角色权限过滤': 'Add role permission filter here',
  '修改物件申请': 'Modify item application',
  '创建申请': 'Create application',
  '提交申请': 'Submit application',
  '是否确认提交编号为': 'Confirm submission of application number:',
  '父级': 'Parent',
  '物件申请详情对话框': 'Item application details dialog',
  '物料申请单详情信息': 'Material application details',
  '的申请单进行审批？': 'application for approval?',
  '编辑详情操作': 'Edit details operation',
  '详情添加按钮操作': 'Detail add button operation',
  '请重新检查申请清单后，确保数据完整后再试': 'Please recheck the application list and ensure data integrity before retrying',
  '去查看': 'Go to view',
  '尾款': 'Final payment',
  '工单作废': 'Work order voidance',
  '工单记录表格数据': 'Work order record table data',
  '打印单据类型': 'Print document type',
  '日接到奔驰引擎盖喷漆工单': 'Received Mercedes hood painting work order today',
  '日接到奔驰引擎盖喷漆工单，检车后发现需要喷漆处理，': 'Received Mercedes hood painting work order today; inspection revealed need for painting.',
  '是否确认删除工单记录编号为': 'Confirm deletion of work order record number:',
  '月': 'Month',
  '查询工单记录列表': 'Query work order record list',
  '样例': 'Example',
  '检车后发现需要喷漆处理': 'Inspection revealed need for painting treatment',
  '的工单？作废后将删除相关财务流水': 'work order? Voiding will delete related financial transactions',
  '确认是否作废工单编号为': 'Confirm whether to void work order number',
  '结算人': 'Settler',
  '全局优化：确保文字在彩色行上清晰可见': 'Global optimization: Ensure text is clearly visible on colored rows',
  '内部文本居中（可选）': 'Center internal text (optional)',
  '利润：折扣金额': 'Profit: Discount amount',
  '右侧间距': 'Right spacing',
  '在描述项标签上显示星号': 'Display asterisk on description item labels',
  '完结按钮': 'Complete button',
  '宽度自适应内容': 'Width adapts to content',
  '工单总计': 'Work order total',
  '工单详情序号': 'Work order detail serial number',
  '工单详情表格数据': 'Work order detail table data',
  '底部间距': 'Bottom spacing',
  '弹性布局': 'Flexible layout',
  '手动添加新服务项': 'Manually add new service item',
  '标签右对齐': 'Right-align labels',
  '水平居中': 'Horizontal center',
  '用户折扣': 'User discount',
  '的工单，确定是否完结？完结后将关闭该工单相关操作': 'work order, confirm to complete? Completion will close related operations for this work order',
  '禁止收缩': 'Disable shrinking',
  '第一行：工单标题和编号': 'First row: Work order title and number',
  '第七行：问题描述和图片': 'Seventh row: Problem description and images',
  '第三行：联系方式和车辆信息': 'Third row: Contact information and vehicle details',
  '第二行：客户信息': 'Second row: Customer information',
  '第五行：金额信息': 'Fifth row: Amount information',
  '第六行：支付信息': 'Sixth row: Payment information',
  '第四行：日期信息': 'Fourth row: Date information',
  '统计数据文本': 'Statistical data text',
  '过滤掉不需要显示的行': 'Filter out rows that do not need to be displayed',
  '隐藏表单自带的必填星号': 'Hide built-in required field asterisk',
  '修改进度操作': 'Modify progress operation',
  '响应式赋值': 'Responsive assignment',
  '如果有': 'If any',
  '工单详情删除按钮操作': 'Work order detail delete button operation',
  '工单详情添加按钮操作': 'Work order detail add button operation',
  '工单配件详情表格数据': 'Work order parts detail table data',
  '强制刷新当前子表格': 'Force refresh current subtable',
  '强制更新表格布局（可选）': 'Force update table layout (optional)',
  '排序，确保相同类型连续排列': 'Sort to ensure consecutive arrangement of same types',
  '新': 'New',
  '更': 'Change',
  '根据实际布局调整': 'Adjust according to actual layout',
  '派工单详情表格数据': 'Dispatch order detail table data',
  '消耗时间': 'Time consumed',
  '添加或修改工单记录对话框': 'Dialog for adding or modifying work order records',
  '生成合并策略': 'Generate merge strategy',
  '统计配件总金额': 'Total Accessories Amount',
  '进度更新不能为': 'Progress Update Cannot Be',
  '工单定损项添加按钮操作': 'Work Order Damage Assessment Item Add Button Action',
  '查找用户信息': 'Search User Information',
  '减少内边距': 'Reduce Padding',
  '减少分割线上下间距': 'Reduce Divider Top and Bottom Spacing',
  '减少右侧间距': 'Reduce Right Spacing',
  '减少容器内边距': 'Reduce Container Padding',
  '减少底部间距': 'Reduce Bottom Spacing',
  '减少按钮内边距': 'Reduce Button Padding',
  '减少文本域内边距': 'Reduce Text Area Padding',
  '减少表格单元格内边距': 'Reduce Table Cell Padding',
  '减少输入框高度': 'Reduce Input Field Height',
  '减少顶部间距': 'Reduce Top Spacing',
  '利用回调重置数据': 'Reset Data Using Callback',
  '压缩分割线间距': 'Compress Divider Spacing',
  '压缩布局：减少各组件间距': 'Compact Layout: Reduce Component Spacing',
  '压缩按钮组间距': 'Compress Button Group Spacing',
  '压缩整体容器间距': 'Compress Overall Container Spacing',
  '压缩日期选择器高度': 'Compress Date Picker Height',
  '压缩表格行高': 'Compress Table Row Height',
  '压缩输入框高度': 'Compress Input Field Height',
  '工单定损详情表格数据': 'Work Order Damage Assessment Details Table Data',
  '是否含税结算': 'Whether Tax-Inclusive Settlement',
  '税率': 'Tax Rate',
  '统一表格行高': 'Uniform Table Row Height',
  '统计计算主项金额相关信息': 'Calculate and Summarize Main Item Amount Information',
  '获取已有工单中客户相关分成比': 'Obtain Customer Split Ratio from Existing Work Orders',
  '请检查后再试': 'Please Check and Try Again',
  '问题信息不完整': 'Incomplete Problem Information',
  '附加税（': 'Additional Tax (',
  '如果选择了多行，则只保留最后选择的一行': 'If Multiple Rows Are Selected, Only the Last Selected Row Is Retained',
  '指派工单操作': 'Assign Work Order Action',
  '更新为单选后的结果': 'Result After Updating to Single Selection',
  '正常单选情况': 'Normal Single Selection Scenario',
  '派工单编辑按钮操作': 'Dispatch Work Order Edit Button Action',
  '的工单，确认后此订单将由': 'Work Order, After Confirmation This Order Will Be Handled By',
  '调整后的单选处理方法': 'Adjusted Single Selection Handling Method',
  '发布工单操作': 'Publish Work Order Action',
  '发布成功': 'Published Successfully',
  '只匹配分隔符，且前后不能是空格': 'Only match the delimiter, and there must be no spaces before or after it.',
  '快速录入': 'Quick entry',
  '快速添加工单': 'Quickly add work order',
  '快速登记方便快速添加表单数据，内容的顺序依次是：故障描述': 'Quick registration allows for fast addition of form data. The content order is as follows: fault description',
  '搜索表单': 'Search form',
  '数据缺失，请检查后再试': 'Data is missing, please check and try again.',
  '桌面端按钮组': 'Desktop button group',
  '电话号码': 'Phone number',
  '的工单，确认后将产生入帐流水，是否确认？': 'work order. Confirmation will generate an accounting entry. Confirm?',
  '移动端按钮组': 'Mobile button group',
  '表格': 'Table',
  '过滤内容格式': 'Filter content format',
  '：检车后发现需要喷漆处理': ': After inspection, it was found that painting is required.',
  '：检车后发现需要喷漆处理，': ': After inspection, it was found that painting is required,',
  '初始化': 'Initialize',
  '可汇总计算的服务金额统计': 'Service amount statistics that can be summarized and calculated.',
  '暂时不更新进度，防止提前完结。方便完成单据打印等工作': 'Temporarily do not update the progress to prevent premature completion. Facilitates completion of document printing and other tasks.',
  '此处是通过样式判断该项服务是否会被纳入到合并主项金额中去。': 'Here, it is determined by style whether this service will be included in the merged main item amount.',
  '的工单，确认是结算并生成收入流水？': 'work order. Confirm settlement and generate revenue entry?',
  '算': 'Calculate',
  '结': 'Settle',
  '结算按钮': 'Settlement button',
  '初始化存储数据的数组': 'Initialize the array for storing data.',
  '判断如果是合并计算的服务，那么只计算一次，其它项为': 'If it is a service for merged calculation, then only calculate once; other items are',
  '已存在于右侧列表中': 'Already exists in the right list.',
  '总计': 'Total',
  '计算每个服务类的金额': 'Calculate the amount for each service category.',
  '请移除后': 'Please remove it first,',
  '重新再式': 'Then try again.',
  '主项总金额': 'Total main item amount',
  '只在合计列': 'Only in the total column',
  '只在第一行显示合计': 'Only show the total in the first row.',
  '当前类目提成': 'Current category commission',
  '当前类目金额': 'Current category amount',
  '是否允许删除等操作': 'Whether deletion and other operations are allowed.',
  '显示合并单元格': 'Display merged cells.',
  '最后一列': 'Last column',
  '根据': 'According to',
  '确定最后一列的索引': 'Determine the index of the last column.',
  '跨所有行': 'Across all rows',
  '使用': 'Use',
  '使用内存': 'Use memory',
  '信息': 'Information',
  '内存信息': 'Memory information',
  '内存消耗': 'Memory consumption',
  '内存配置': 'Memory configuration',
  '出口': 'Export',
  '单机': 'Standalone',
  '命令': 'Command',
  '命令统计': 'Command Statistics',
  '天': 'Day',
  '客户端数': 'Number of Clients',
  '峰值': 'Peak',
  '打开加载层': 'Open Loading Layer',
  '是否开启': 'Enabled or Not',
  '是否成功': 'Success or Not',
  '查缓存询信息': 'Query Cache Information',
  '正在加载缓存监控数据，请稍候！': 'Loading cache monitoring data, please wait!',
  '版本': 'Version',
  '端口': 'Port',
  '统计命令信息': 'Statistical Command Information',
  '网络入口': 'Network Entry',
  '运行时间': 'Running Time',
  '运行模式': 'Running Mode',
  '集群': 'Cluster',
  '列表前缀去除': 'List Prefix Removal',
  '刷新缓存列表成功': 'Refresh Cache List Successful',
  '刷新缓存名称列表': 'Refresh Cache Name List',
  '刷新缓存键名列表': 'Refresh Cache Key List',
  '刷新键名列表成功': 'Refresh Key List Successful',
  '查询缓存内容详细': 'Query Cache Content Details',
  '查询缓存名称列表': 'Query Cache Name List',
  '查询缓存键名列表': 'Query Cache Key List',
  '清理全部': 'Clear All',
  '清理全部缓存': 'Clear All Cache',
  '清理全部缓存成功': 'Clear All Cache Successful',
  '清理指定名称缓存': 'Clear Specified Name Cache',
  '清理指定键名缓存': 'Clear Specified Key Cache',
  '清理缓存名称': 'Clear Cache Name',
  '清理缓存键名': 'Clear Cache Key',
  '缓存内容': 'Cache Content',
  '缓存名称': 'Cache Name',
  '缓存键名': 'Cache Key',
  '键名列表': 'Key List',
  '键名前缀去除': 'Key Prefix Removal',
  '下次执行时间:': 'Next Execution Time:',
  '任务分组': 'Task Group',
  '任务名称': 'Task Name',
  '任务名称不能为空': 'Task Name Cannot Be Empty',
  '任务吗？': 'Task?',
  '任务日志列表查询': 'Task Log List Query',
  '任务日志详细': 'Task Log Details',
  '任务状态': 'Task Status',
  '任务状态修改': 'Task Status Modification',
  '任务组名': 'Task Group Name',
  '任务组名字典翻译': 'Task Group Name Dictionary Translation',
  '任务编号': 'Task Number',
  '任务详细': 'Task Details',
  '任务详细信息': 'Task Detailed Information',
  '传入的表达式': 'Incoming Expression',
  '修改任务': 'Modify Task',
  '允许': 'Allow',
  '关': 'Off',
  '参数说明：支持字符串，布尔类型，长整型，浮点型，整型': 'Parameter Description: Supports string, boolean, long integer, floating-point, and integer types',
  '定时任务表格数据': 'Scheduled Task Table Data',
  '执行一次': 'Execute Once',
  '执行成功': 'Execution Successful',
  '执行策略': 'Execution Strategy',
  '执行表达式': 'Execution Expression',
  '执行表达式不能为空': 'Execution Expression Cannot Be Empty',
  '放弃执行': 'Abandon Execution',
  '日志': 'Log',
  '是否并发': 'Whether to Execute Concurrently',
  '是否显示': 'Whether to Display',
  '是否显示详细弹出层': 'Whether to Show Detailed Popup Layer',
  '是否确认删除定时任务编号为': 'Are You Sure You Want to Delete the Scheduled Task Number',
  '暂停': 'Pause',
  '更多操作触发': 'More Operations Trigger',
  '查询定时任务列表': 'Query Scheduled Task List',
  '正常': 'Normal',
  '添加任务': 'Add Task',
  '添加或修改定时任务对话框': 'Add or Modify Scheduled Task Dialog',
  '状态': 'Status',
  '生成表达式': 'Generate Expression',
  '确定后回传值': 'Return Value After Confirmation',
  '确认要立即执行一次': 'Confirm to Execute Immediately Once',
  '禁止': 'Forbid',
  '立即执行': 'Execute Immediately',
  '立即执行一次': 'Execute Immediately Once',
  '类调用示例:': 'Class Call Example:',
  '表达式': 'Expression',
  '表达式弹出层': 'Expression Popup Layer',
  '表达式按钮操作': 'Expression Button Operation',
  '表达式生成器': 'Expression Generator',
  '请输入': 'Please Enter',
  '请输入任务名称': 'Please Enter Task Name',
  '请输入调用目标字符串': 'Please Enter Call Target String',
  '请选择任务分组': 'Please select task group',
  '请选择任务状态': 'Please select task status',
  '请选择任务组名': 'Please select task group name',
  '调度日志': 'Scheduling log',
  '调用方法': 'Invocation method',
  '调用目标字符串': 'Target invocation string',
  '调用目标字符串不能为空': 'Target invocation string cannot be empty',
  '调用目标方法:': 'Target invocation method:',
  '调用示例:': 'Invocation example:',
  '闭': 'Close',
  '默认策略': 'Default policy',
  '失败': 'Failure',
  '异常信息:': 'Exception information:',
  '执行时间': 'Execution time',
  '执行状态': 'Execution status',
  '日志信息': 'Log information',
  '日志序号:': 'Log sequence number:',
  '日志编号': 'Log number',
  '日期范围': 'Date range',
  '是否确认删除调度日志编号为': 'Are you sure you want to delete scheduling log number',
  '是否确认清空所有调度日志数据项？': 'Are you sure you want to clear all scheduling log data items?',
  '查询调度日志列表': 'Query scheduling log list',
  '清空': 'Clear',
  '清空成功': 'Cleared successfully',
  '清空按钮操作': 'Clear button operation',
  '详细': 'Details',
  '详细按钮操作': 'Details button operation',
  '请选择执行状态': 'Please select execution status',
  '调度日志表格数据': 'Scheduling log table data',
  '调度日志详细': 'Scheduling log details',
  '返回按钮': 'Return button',
  '排序触发事件': 'Sort trigger event',
  '操作信息': 'Operation information',
  '操作系统': 'Operating system',
  '数据项': 'Data item',
  '是否确认删除访问编号为': 'Are you sure you want to delete access number',
  '是否确认清空所有登录日志数据项？': 'Are you sure you want to clear all login log data items?',
  '是否确认解锁用户': 'Are you sure you want to unlock user',
  '查询登录日志列表': 'Query login log list',
  '浏览器': 'Browser',
  '用户': 'User',
  '登录地址': 'Login address',
  '登录地点': 'Login location',
  '登录日期': 'Login date',
  '登录时间': 'Login time',
  '登录状态': 'Login status',
  '表格数据': 'Table Data',
  '解锁': 'Unlock',
  '解锁成功': 'Unlock Successful',
  '解锁按钮操作': 'Unlock Button Action',
  '访问编号': 'Access Number',
  '请输入用户名称': 'Please Enter Username',
  '请输入登录地址': 'Please Enter Login Address',
  '选择用户名': 'Select Username',
  '默认排序': 'Default Sorting',
  '主机': 'Host',
  '会话编号': 'Session Number',
  '强退': 'Force Logout',
  '强退成功': 'Force Logout Successful',
  '强退按钮操作': 'Force Logout Button Action',
  '是否确认强退名称为': 'Confirm Force Logout for User Named',
  '登录名称': 'Login Name',
  '的用户？': '?',
  '部门名称': 'Department Name',
  '操作地址': 'Operation Address',
  '操作地点': 'Operation Location',
  '操作方法:': 'Operation Method:',
  '操作日志类型字典翻译': 'Operation Log Type Dictionary Translation',
  '操作日志详细': 'Operation Log Details',
  '操作日期': 'Operation Date',
  '操作模块:': 'Operation Module:',
  '操作状态': 'Operation Status',
  '操作类型': 'Operation Type',
  '是否确认删除日志编号为': 'Confirm Deletion of Log Number',
  '是否确认清空所有操作日志数据项？': 'Confirm Clearing All Operation Log Data Items?',
  '查询登录日志': 'Query Login Log',
  '毫秒': 'Millisecond',
  '登录信息:': 'Login Information:',
  '类型': 'Type',
  '系统模块': 'System Module',
  '请求参数:': 'Request Parameters:',
  '请求地址:': 'Request Address:',
  '请求方式:': 'Request Method:',
  '请输入操作人员': 'Please Enter Operator',
  '请输入操作地址': 'Please Enter Operation Address',
  '请输入系统模块': 'Please Enter System Module',
  '返回参数:': 'Return Parameters:',
  '使用率': 'Usage Rate',
  '值': 'Value',
  '内存': 'Memory',
  '剩余内存': 'Remaining Memory',
  '可用大小': 'Available Size',
  '名称': 'Name',
  '启动时间': 'Startup Time',
  '安装路径': 'Installation Path',
  '属性': 'Properties',
  '已用内存': 'Used Memory',
  '已用大小': 'Used Size',
  '已用百分比': 'Usage Percentage',
  '当前空闲率': 'Current Idle Rate',
  '总内存': 'Total Memory',
  '总大小': 'Total Size',
  '文件系统': 'File System',
  '服务器': 'Server',
  '服务器信息': 'Server Information',
  '服务器名称': 'Server Name',
  '查询服务器信息': 'Query Server Information',
  '核心数': 'Core Count',
  '正在加载服务监控数据，请稍候！': 'Loading service monitoring data, please wait!',
  '用户使用率': 'User Usage Rate',
  '盘符类型': 'Drive Type',
  '盘符路径': 'Drive Path',
  '磁盘状态': 'Disk Status',
  '系统使用率': 'System Usage Rate',
  '系统架构': 'System Architecture',
  '虚拟机信息': 'Virtual Machine Information',
  '运行参数': 'Runtime Parameters',
  '运行时长': 'Runtime Duration',
  '项目路径': 'Project Path',
  '业务名称': 'Business Name',
  '总计金额:': 'Total Amount:',
  '请输入业务名称': 'Please enter the business name',
  '请输入操作人员名称': "Please enter the operator's name",
  '修改参数': 'Modify Parameters',
  '刷新缓存按钮操作': 'Refresh Cache Button Operation',
  '参数主键': 'Parameter Primary Key',
  '参数名称': 'Parameter Name',
  '参数表格数据': 'Parameter Table Data',
  '参数键值': 'Parameter Key Value',
  '参数键名': 'Parameter Key Name',
  '是否确认删除参数编号为': 'Confirm deletion of parameter ID',
  '查询参数列表': 'Query Parameter List',
  '添加参数': 'Add Parameter',
  '添加或修改参数配置对话框': 'Add or Modify Parameter Configuration Dialog',
  '系统内置': 'System Built-in',
  '请输入参数名称': 'Please enter the parameter name',
  '请输入参数键值': 'Please enter the parameter key value',
  '请输入参数键名': 'Please enter the parameter key name',
  '上级部门': 'Parent Department',
  '修改部门': 'Modify Department',
  '展开': 'Expand',
  '折叠': 'Collapse',
  '折叠操作': 'Collapse Operation',
  '是否展开，默认全部展开': 'Expand or not, expand all by default',
  '是否确认删除名称为': 'Confirm deletion of the item named',
  '显示排序': 'Display sorting',
  '查询部门列表': 'Query department list',
  '添加或修改部门对话框': 'Add or modify department dialog',
  '添加部门': 'Add department',
  '联系电话': 'Contact phone number',
  '表格树数据': 'Table tree data',
  '请输入联系电话': 'Please enter the contact phone number',
  '请输入负责人': 'Please enter the person in charge',
  '请输入邮箱': 'Please enter the email address',
  '请输入部门名称': 'Please enter the department name',
  '负责人': 'Person in charge',
  '转换部门数据结构': 'Convert department data structure',
  '选择上级部门': 'Select parent department',
  '邮箱': 'Email',
  '部门树选项': 'Department tree options',
  '部门状态': 'Department status',
  '重新渲染表格状态': 'Re-render table status',
  '主要': 'Primary',
  '修改字典数据': 'Modify dictionary data',
  '回显样式': 'Echo style',
  '字典名称': 'Dictionary name',
  '字典排序': 'Dictionary sorting',
  '字典标签': 'Dictionary label',
  '字典类型': 'Dictionary type',
  '字典编码': 'Dictionary code',
  '字典表格数据': 'Dictionary table data',
  '字典键值': 'Dictionary key value',
  '数据标签': 'Data label',
  '数据标签回显样式': 'Data label echo style',
  '数据状态': 'Data status',
  '数据键值': 'Data key value',
  '是否确认删除字典编码为': 'Confirm deletion of dictionary code',
  '查询字典数据列表': 'Query dictionary data list',
  '查询字典类型列表': 'Query dictionary type list',
  '查询字典类型详细': 'Query dictionary type details',
  '样式属性': 'Style attributes',
  '添加字典数据': 'Add dictionary data',
  '类型数据字典': 'Type data dictionary',
  '警告': 'Warning',
  '请输入字典标签': 'Please enter the dictionary label',
  '请输入数据标签': 'Please enter the data label',
  '请输入数据键值': 'Please enter the data key value',
  '请输入样式属性': 'Please enter the style attributes',
  '返回按钮操作': 'Return button operation',
  '默认': 'Default',
  '默认字典类型': 'Default dictionary type',
  '修改字典类型': 'Modify dictionary type',
  '字典状态': 'Dictionary status',
  '字典编号': 'Dictionary ID',
  '是否确认删除字典编号为': 'Confirm deletion of dictionary ID',
  '添加字典类型': 'Add Dictionary Type',
  '请输入字典名称': 'Please enter the dictionary name',
  '请输入字典类型': 'Please enter the dictionary type',
  '主类目': 'Main Category',
  '修改菜单': 'Edit Menu',
  '和地址保持一致': 'Consistent with the address',
  '图标': 'Icon',
  '开头': 'Beginning',
  '控制器中定义的权限字符，如:': 'Permission character defined in the controller, e.g.:',
  '是否展开，默认全部折叠': 'Expand or not, collapsed by default',
  '权限标识': 'Permission Identifier',
  '查询菜单下拉树结构': 'Query menu dropdown tree structure',
  '查询菜单列表': 'Query menu list',
  '添加或修改菜单对话框': 'Add or edit menu dialog',
  '添加菜单': 'Add Menu',
  '点击选择图标': 'Click to select an icon',
  '目录下': 'Under directory',
  '缓存，需要匹配组件的': "Cache, needs to match the component's",
  '菜单名称': 'Menu Name',
  '菜单图标': 'Menu Icon',
  '菜单树选项': 'Menu Tree Options',
  '菜单类型': 'Menu Type',
  '菜单表格树数据': 'Menu table tree data',
  '访问的组件路径，如:': 'Component path to access, e.g.:',
  '访问的路由地址，如:': 'Route address to access, e.g.:',
  '访问路由的默认传递参数，如:': 'Default parameters passed to the route, e.g.:',
  '请输入权限标识': 'Please enter the permission identifier',
  '请输入组件路径': 'Please enter the component path',
  '请输入菜单名称': 'Please enter the menu name',
  '请输入路由参数': 'Please enter the route parameters',
  '请输入路由名称': 'Please enter the route name',
  '请输入路由地址': 'Please enter the route address',
  '转换菜单数据结构': 'Convert menu data structure',
  '选择上级菜单': 'Select parent menu',
  '选择停用则路由将不会出现在侧边栏，也不能被访问': 'If disabled is selected, the route will not appear in the sidebar and cannot be accessed',
  '选择图标': 'Select Icon',
  '选择是则会被': 'If yes is selected, it will be',
  '选择是外链则路由地址需要以': 'If external link is selected, the route address must start with',
  '选择隐藏则路由将不会出现在侧边栏，但仍然可以访问': 'If hidden is selected, the route will not appear in the sidebar but can still be accessed',
  '默认不填则和路由地址相同：如地址为:': 'If left blank by default, it will be the same as the route address: e.g., if the address is:',
  '（注意：为避免名字的冲突，特殊情况下请自定义，保证唯一性）': '(Note: To avoid naming conflicts, please customize in special cases to ensure uniqueness)',
  '，则名称为': ', then the name is',
  '，如外网地址需内链访问则以': ', if an external address needs to be accessed as an internal link, then',
  '，默认在': ', default is in',
  '修改公告': 'Edit Notice',
  '公告标题': 'Notice Title',
  '公告类型': 'Notice Type',
  '公告表格数据': 'Notice table data',
  '内容': 'Content',
  '是否确认删除公告编号为': 'Are you sure you want to delete the announcement with ID',
  '查询公告列表': 'Query announcement list',
  '添加公告': 'Add announcement',
  '添加或修改公告对话框': 'Add or modify announcement dialog',
  '请输入公告标题': 'Please enter the announcement title',
  '请选择公告类型': 'Please select the announcement type',
  '修改岗位': 'Modify position',
  '岗位名称': 'Position name',
  '岗位排序': 'Position sorting',
  '岗位状态': 'Position status',
  '岗位编号': 'Position ID',
  '岗位编码': 'Position code',
  '岗位表格数据': 'Position table data',
  '岗位顺序': 'Position order',
  '是否确认删除岗位编号为': 'Are you sure you want to delete the position with ID',
  '查询岗位列表': 'Query position list',
  '添加岗位': 'Add position',
  '添加或修改岗位对话框': 'Add or modify position dialog',
  '请输入岗位名称': 'Please enter the position name',
  '请输入岗位编码': 'Please enter the position code',
  '请输入编码名称': 'Please enter the code name',
  '不能为空': 'Cannot be empty',
  '修改定损问题信息': 'Modify loss assessment question information',
  '分成比': 'Split ratio',
  '定损': 'Loss assessment',
  '定损服务类型': 'Loss assessment service type',
  '定损问题信息表格数据': 'Loss assessment question information table data',
  '是否确认删除定损问题信息编号为': 'Are you sure you want to delete the loss assessment question information with ID',
  '条目类型': 'Entry type',
  '查询定损问题信息列表': 'Query loss assessment question information list',
  '注意：分成比不修改的情况下，默认为': 'Note: If the split ratio is not modified, the default is',
  '添加定损问题信息': 'Add loss assessment question information',
  '添加或修改定损问题信息对话框': 'Add or modify loss assessment question information dialog',
  '维修名称': 'Repair name',
  '请输入分成比': 'Please enter the split ratio',
  '请输入费用': 'Please enter the cost',
  '请选择服务类型': 'Please select the service type',
  '请选择条目类型': 'Please select the entry type',
  '请选择问题类型': 'Please select the question type',
  '货币类型': 'Currency type',
  '取消授权按钮操作': 'Cancel authorization button action',
  '手机': 'Mobile phone',
  '打开授权用户表弹窗': 'Open authorized user table popup',
  '批量取消授权按钮操作': 'Batch cancel authorization button action',
  '是否取消选中用户授权数据项？': 'Are you sure you want to cancel the authorization for the selected user data items?',
  '查询授权用户列表': 'Query authorized user list',
  '用户昵称': 'User nickname',
  '用户表格数据': 'User table data',
  '确认要取消该用户': 'Confirm to cancel this user',
  '角色吗？': 'Role?',
  '请输入手机号码': 'Please enter mobile number',
  '选中用户组': 'Selected user group',
  '仅本人数据权限': 'Only own data permission',
  '修改角色': 'Modify role',
  '全不选': 'Deselect all',
  '全不选）': 'Deselect all)',
  '全选': 'Select all',
  '全部数据权限': 'All data permissions',
  '分配数据权限': 'Assign data permissions',
  '分配数据权限操作': 'Assign data permissions operation',
  '分配用户操作': 'Assign user operation',
  '分配角色数据权限对话框': 'Assign role data permissions dialog',
  '加载中，请稍候': 'Loading, please wait',
  '半选中的菜单节点': 'Half-selected menu node',
  '半选中的部门节点': 'Half-selected department node',
  '取消按钮（数据权限）': 'Cancel button (data permissions)',
  '所有菜单节点数据': 'All menu node data',
  '所有部门节点数据': 'All department node data',
  '折叠）': 'Collapse)',
  '提交按钮（数据权限）': 'Submit button (data permissions)',
  '数据范围选项': 'Data scope options',
  '是否显示弹出层（数据权限）': 'Whether to show popup layer (data permissions)',
  '是否确认删除角色编号为': 'Are you sure to delete role number',
  '显示顺序': 'Display order',
  '本部门及以下数据权限': 'This department and below data permissions',
  '本部门数据权限': 'This department data permissions',
  '权限范围': 'Permission scope',
  '查询菜单树结构': 'Query menu tree structure',
  '查询角色列表': 'Query role list',
  '查询部门树结构': 'Query department tree structure',
  '树权限（全选': 'Tree permissions (select all',
  '树权限（展开': 'Tree permissions (expand',
  '树权限（父子联动）': 'Tree permissions (parent-child linkage)',
  '根据角色': 'According to role',
  '添加或修改角色配置对话框': 'Add or modify role configuration dialog',
  '添加角色': 'Add role',
  '目前被选中的菜单节点': 'Currently selected menu node',
  '目前被选中的部门节点': 'Currently selected department node',
  '自定数据权限': 'Custom data permissions',
  '菜单列表': 'Menu list',
  '菜单权限': 'Menu permissions',
  '角色名称': 'Role name',
  '角色状态': 'Role status',
  '角色状态修改': 'Role status modification',
  '角色编号': 'Role number',
  '角色表格数据': 'Role table data',
  '角色顺序': 'Role order',
  '请输入权限字符': 'Please enter permission character',
  '请输入角色名称': 'Please enter role name',
  '选择角色权限范围触发': 'Trigger by Selecting Role Permission Scope',
  '部门列表': 'Department List',
  '授权用户': 'Authorized Users',
  '显示弹框': 'Display Pop-up Box',
  '未授权用户数据': 'Unauthorized User Data',
  '查询表数据': 'Query Table Data',
  '请选择要分配的用户': 'Please Select Users to Assign',
  '选中数组值': 'Selected Array Values',
  '选择授权用户操作': 'Select Authorized User Operation',
  '选择用户': 'Select User',
  '保存选中的数据编号': 'Save Selected Data Numbers',
  '关闭按钮': 'Close Button',
  '分页信息': 'Pagination Information',
  '单击选中行数据': 'Click to Select Row Data',
  '检查角色状态': 'Check Role Status',
  '用户信息': 'User Information',
  '登录账号': 'Login Account',
  '选中角色编号': 'Selected Role Number',
  '上传的地址': 'Upload Address',
  '下载模板操作': 'Download Template Operation',
  '仅允许导入': 'Import Only Allowed',
  '修改用户': 'Modify User',
  '分配角色操作': 'Assign Role Operation',
  '列信息': 'Column Information',
  '导入按钮操作': 'Import Button Operation',
  '导入结果': 'Import Results',
  '岗位': 'Post',
  '岗位选项': 'Post Options',
  '弹出层标题（用户导入）': 'Pop-up Layer Title (User Import)',
  '归属部门': 'Assigned Department',
  '所有部门树选项': 'All Department Tree Options',
  '提交上传文件': 'Submit Uploaded File',
  '提示': 'Prompt',
  '文件上传中处理': 'File Uploading Process',
  '文件上传成功处理': 'File Upload Success Process',
  '是否显示弹出层（用户导入）': 'Whether to Display Pop-up Layer (User Import)',
  '是否确认删除用户编号为': 'Confirm Deletion of User Number',
  '是否禁用上传': 'Whether to Disable Upload',
  '查询用户列表': 'Query User List',
  '查询部门下拉树结构': 'Query Department Drop-down Tree Structure',
  '根据名称筛选部门树': 'Filter Department Tree by Name',
  '格式文件。': 'Format File.',
  '添加或修改用户配置对话框': 'Add or Modify User Configuration Dialog',
  '用户名称长度必须介于': 'User Name Length Must Be Between',
  '用户吗？': 'User?',
  '用户密码': 'User Password',
  '用户导入': 'User Import',
  '用户导入参数': 'User Import Parameters',
  '用户导入对话框': 'User Import Dialog',
  '用户数据': 'User Data',
  '用户状态': 'User Status',
  '用户状态修改': 'User Status Modification',
  '的新密码': 'New Password',
  '确定': 'Confirm',
  '筛选节点': 'Filter Nodes',
  '节点单击事件': 'Node Click Event',
  '角色': 'Role',
  '角色选项': 'Role Options',
  '设置上传的请求头部': 'Set Upload Request Headers',
  '请输入用户密码': 'Please Enter User Password',
  '请输入用户昵称': 'Please Enter User Nickname',
  '请选择岗位': 'Please Select Position',
  '请选择归属部门': 'Please Select Department',
  '请选择性别': 'Please Select Gender',
  '请选择角色': 'Please Select Role',
  '过滤掉已禁用部门树选项': 'Filter Out Disabled Department Tree Options',
  '过滤禁用的部门': 'Filter Disabled Departments',
  '部门': 'Department',
  '部门数据': 'Department Data',
  '重置密码按钮操作': 'Reset Password Button Action',
  '默认密码': 'Default Password',
  '修改密码': 'Change Password',
  '个字符': 'Characters',
  '到': 'To',
  '新密码': 'New Password',
  '旧密码': 'Old Password',
  '长度在': 'Length Between',
  '上传图片': 'Upload Image',
  '上传预处理': 'Upload Preprocessing',
  '不允许改变': 'Not Allowed to Change',
  '交': 'Submit',
  '修改头像': 'Modify Avatar',
  '关闭窗口': 'Close Window',
  '刷新组件': 'Refresh Component',
  '后缀的文件。': 'Files with Extension',
  '向右旋转': 'Rotate Right',
  '向左旋转': 'Rotate Left',
  '固定截图框大小': 'Fixed Crop Box Size',
  '图片缩放': 'Image Scaling',
  '如:': 'For Example:',
  '实时预览': 'Real-time Preview',
  '打开弹出层结束时的回调': 'Callback When Popup Closes',
  '提': 'Submit',
  '文件名称': 'File Name',
  '文件格式错误，请上传图片类型': 'Invalid file format, please upload an image',
  '是否默认生成截图框': 'Whether to Generate Crop Box by Default',
  '格式': 'Format',
  '点击上传头像': 'Click to Upload Avatar',
  '编辑头像': 'Edit Avatar',
  '裁剪图片的地址': 'Image Cropping Address',
  '覆盖默认的上传行为': 'Override the default upload behavior',
  '默认生成截图为': 'Default generated screenshot is',
  '默认生成截图框宽度': 'Default screenshot frame width',
  '默认生成截图框高度': 'Default screenshot frame height',
  '性别': 'Gender',
  // 路由标题翻译
  'Home': 'Home',
  '个人中心': 'Personal Center',
  '用户权限分配': 'User Permission Assignment',
  '修改生成配置': 'Modify Generation Configuration',
  '工单受理': 'Work Order Acceptance',
  '工单分派': 'Work Order Assignment',
  '工单进度': 'Work Order Progress',
  '工单结算': 'Work Order Settlement',
  '工单完结': 'Work Order Completion',
  '工单查询': 'Work Order Query',
  '客户管理': 'Customer Management',
  '采购管理': 'Procurement Management',
  '库存管理': 'Inventory Management',
  '财务管理': 'Financial Management',
  '系统管理': 'System Management',
  '系统监控': 'System Monitoring',
  '系统工具': 'System Tools',
  '定损问题信息': 'Damage Assessment Information',
  '菜单管理': 'Menu Management',
  '部门管理': 'Department Management',
  '岗位管理': 'Position Management',
  '字典管理': 'Dictionary Management',
  '参数设置': 'Parameter Settings',
  '通知公告': 'Notifications',
  '日志管理': 'Log Management',
  '用户管理': 'User Management',
  '角色管理': 'Role Management',
  '在线用户': 'Online Users',
  '定时任务': 'Scheduled Tasks',
  '数据监控': 'Data Monitoring',
  '服务监控': 'Service Monitoring',
  '缓存监控': 'Cache Monitoring',
  '缓存列表': 'Cache List',
  '表单构建': 'Form Builder',
  '代码生成': 'Code Generation',
  '系统接口': 'System Interface'
}
