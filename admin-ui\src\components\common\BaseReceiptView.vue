<template>
  <div class="base-receipt-view">

    <!-- 表单区域 -->
    <div class="form-section" v-if="showForm && formConfig">
      <el-card class="form-card">
        <div slot="header" class="form-header">
          <span>{{ formConfig.title || '基本信息' }}</span>
          <div class="form-actions">
            <slot name="form-actions">
              <el-button
                v-if="!readonly"
                type="primary"
                @click="handleSave"
                :loading="saveLoading"
                size="small">
                保存
              </el-button>
            </slot>
          </div>
        </div>

        <!-- 动态表单 -->
        <el-form
          ref="receiptForm"
          :model="formData"
          :rules="formRules"
          :label-width="formConfig.labelWidth || '120px'"
          :disabled="readonly">

          <el-row :gutter="20">
            <el-col
              v-for="field in formFields"
              :key="field.prop"
              :span="field.span || 12">

              <el-form-item
                :label="field.label"
                :prop="field.prop"
                :required="field.required">

                <!-- 输入框 -->
                <el-input
                  v-if="field.type === 'input'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder"
                  :disabled="field.disabled"
                  @change="handleFieldChange(field.prop, $event)">
                </el-input>

                <!-- 选择器 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder"
                  :disabled="field.disabled"
                  @change="handleFieldChange(field.prop, $event)">
                  <el-option
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value">
                  </el-option>
                </el-select>

                <!-- 日期选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'date'"
                  v-model="formData[field.prop]"
                  type="date"
                  :placeholder="field.placeholder"
                  :disabled="field.disabled"
                  @change="handleFieldChange(field.prop, $event)">
                </el-date-picker>

                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="field.type === 'number'"
                  v-model="formData[field.prop]"
                  :min="field.min"
                  :max="field.max"
                  :precision="field.precision"
                  :disabled="field.disabled"
                  @change="handleFieldChange(field.prop, $event)">
                </el-input-number>

                <!-- 文本域 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  v-model="formData[field.prop]"
                  type="textarea"
                  :rows="field.rows || 3"
                  :placeholder="field.placeholder"
                  :disabled="field.disabled"
                  @change="handleFieldChange(field.prop, $event)">
                </el-input>

                <!-- 自定义字段 -->
                <slot
                  v-else
                  :name="`field-${field.prop}`"
                  :field="field"
                  :value="formData[field.prop]"
                  :onChange="(value) => handleFieldChange(field.prop, value)">
                </slot>

              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>

    <!-- 头部操作区域 -->
    <div class="header-actions" v-if="$slots['header-actions']">
      <slot name="header-actions"></slot>
    </div>

    <!-- 表格区域 -->
    <div class="spreadsheet-section">
      <universal-spreadsheet-univer
        ref="spreadsheet"
        :converter="converter"
        :form-data="formData"
        :order-detail-list="orderDetailList"
        :service-type-dict="serviceTypeDict"
        :width="spreadsheetConfig.width"
        :height="spreadsheetConfig.height"
        :readonly="spreadsheetConfig.readonly"
        :show-operations="spreadsheetConfig.showOperations"
        :custom-config="spreadsheetConfig.customConfig"
        @initialized="handleSpreadsheetInitialized"
        @refreshed="handleSpreadsheetRefreshed"
        @exported="handleSpreadsheetExported"
        @error="handleSpreadsheetError">
      </universal-spreadsheet-univer>
    </div>

    <!-- 底部操作区域 -->
    <div class="footer-actions" v-if="showFooter">
      <slot name="footer-actions">
        <!-- 默认底部操作 -->
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          v-if="!readonly"
          type="primary"
          @click="handleSubmit"
          :loading="submitLoading">
          提交
        </el-button>
      </slot>
    </div>
  </div>
</template>

<script>
import UniversalSpreadsheetUniver from './UniversalSpreadsheetUniver.vue'
import { exportMixin } from '@/mixins/exportMixin.js'
import { receiptConfigFactory } from '@/utils/spreadsheet/ReceiptConfigFactory.js'

export default {
  name: 'BaseReceiptView',
  components: {
    UniversalSpreadsheetUniver
  },
  mixins: [exportMixin],
  props: {
    // 单据类型
    receiptType: {
      type: String,
      required: true
    },
    // 表单数据
    formData: {
      type: Object,
      default: () => ({})
    },
    // 订单详情列表
    orderDetailList: {
      type: Array,
      default: () => []
    },
    // 服务类型字典
    serviceTypeDict: {
      type: [Array, Object],
      default: () => []
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 是否显示头部
    showHeader: {
      type: Boolean,
      default: true
    },
    // 是否显示表单
    showForm: {
      type: Boolean,
      default: true
    },
    // 是否显示底部
    showFooter: {
      type: Boolean,
      default: true
    },
    // 表单配置
    formConfig: {
      type: Object,
      default: null
    },
    // 表单字段配置
    formFields: {
      type: Array,
      default: () => []
    },
    // 表单验证规则
    formRules: {
      type: Object,
      default: () => ({})
    },
    // 自定义转换器配置
    customConverterConfig: {
      type: Object,
      default: () => ({})
    },
    // 自定义表格配置
    customSpreadsheetConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      converter: null,
      saveLoading: false,
      submitLoading: false,
      spreadsheetInitialized: false,
      hasError: false,
      errorMessage: ''
    }
  },
  computed: {
    // 单据标题
    receiptTitle() {
      try {
        const config = receiptConfigFactory.getReceiptConfig(this.receiptType)
        return config.title
      } catch {
        return '单据'
      }
    },

    // 单据信息
    receiptInfo() {
      const info = {}
      if (this.formData.serviceId) {
        info['单据编号'] = this.formData.serviceId
      }
      if (this.formData.createTime) {
        info['创建时间'] = this.formatDateTime(this.formData.createTime)
      }
      return Object.keys(info).length > 0 ? info : null
    },

    // 表格配置
    spreadsheetConfig() {
      try {
        const defaultConfig = receiptConfigFactory.getDefaultComponentConfig(this.receiptType)
        return {
          ...defaultConfig,
          readonly: this.readonly,
          // 如果自定义头部操作按钮存在，则隐藏表格内置的操作按钮
          showOperations: this.$slots['header-actions'] ? false : (this.customSpreadsheetConfig.showOperations !== undefined ? this.customSpreadsheetConfig.showOperations : defaultConfig.showOperations),
          ...this.customSpreadsheetConfig
        }
      } catch {
        return {
          width: '100%',
          height: '600px',
          readonly: this.readonly,
          // 如果自定义头部操作按钮存在，则隐藏表格内置的操作按钮
          showOperations: this.$slots['header-actions'] ? false : true,
          customConfig: {}
        }
      }
    }
  },
  created() {
    this.initializeConverter()
  },

  methods: {
    /**
     * 初始化转换器
     */
    initializeConverter() {
      try {
        // 验证必要参数
        if (!this.receiptType) {
          throw new Error('单据类型不能为空')
        }

        console.log('🔧 [DEBUG] 开始初始化转换器:', {
          receiptType: this.receiptType,
          customConfig: this.customConverterConfig
        })

        this.converter = receiptConfigFactory.createConverter(
          this.receiptType,
          this.customConverterConfig
        )

        if (!this.converter) {
          throw new Error('转换器创建失败，返回值为空')
        }

        console.log('✅ [DEBUG] 转换器初始化成功:', this.converter.constructor.name)

      } catch (error) {
        console.error('❌ [ERROR] 初始化转换器失败:', {
          error: error.message,
          stack: error.stack,
          receiptType: this.receiptType,
          customConfig: this.customConverterConfig
        })
        
        // 显示用户友好的错误信息
        const userMessage = this.getUserFriendlyErrorMessage(error)
        this.$message.error(userMessage)
        
        // 设置错误状态
        this.hasError = true
        this.errorMessage = error.message
      }
    },

    /**
     * 获取用户友好的错误信息
     */
    getUserFriendlyErrorMessage(error) {
      const errorMessage = error.message || '未知错误'
      
      if (errorMessage.includes('单据类型不能为空')) {
        return '配置错误：缺少单据类型参数'
      }
      
      if (errorMessage.includes('转换器创建失败')) {
        return '系统错误：无法创建数据转换器，请联系管理员'
      }
      
      if (errorMessage.includes('不支持的单据类型')) {
        return `配置错误：不支持的单据类型 "${this.receiptType}"`
      }
      
      return `初始化失败：${errorMessage}`
    },

    /**
     * 处理字段变化
     */
    handleFieldChange(prop, value) {
      this.$emit('field-change', { prop, value, formData: this.formData })
    },

    /**
     * 处理保存
     */
    async handleSave() {
      if (!this.$refs.receiptForm) {
        this.$emit('save', this.formData)
        return
      }

      this.saveLoading = true
      try {
        await this.$refs.receiptForm.validate()
        this.$emit('save', this.formData)
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        this.saveLoading = false
      }
    },

    /**
     * 处理提交
     */
    async handleSubmit() {
      if (!this.$refs.receiptForm) {
        this.$emit('submit', this.formData)
        return
      }

      this.submitLoading = true
      try {
        await this.$refs.receiptForm.validate()

        // 验证单据数据
        const validation = receiptConfigFactory.validateFormData(this.receiptType, this.formData)
        if (!validation.isValid) {
          this.$message.error('数据验证失败: ' + validation.errors.join(', '))
          return
        }

        if (validation.warnings.length > 0) {
          this.$message.warning('提醒: ' + validation.warnings.join(', '))
        }

        this.$emit('submit', this.formData)
      } catch (error) {
        console.error('提交失败:', error)
      } finally {
        this.submitLoading = false
      }
    },

    /**
     * 处理取消
     */
    handleCancel() {
      this.$emit('cancel')
    },

    /**
     * 处理刷新
     * @param {Object} options - 刷新选项
     * @param {boolean} options.forceFullRefresh - 是否强制全量刷新
     * @param {boolean} options.formDataChanged - formData是否变化
     * @param {boolean} options.orderDetailListChanged - orderDetailList是否变化
     */
    handleRefresh(options = {}) {
      if (this.$refs.spreadsheet) {
        // 使用智能刷新，传入刷新选项
        this.$refs.spreadsheet.refreshSpreadsheetData(options)
      }
      this.$emit('refresh', options)
    },

    /**
     * 表格初始化完成
     */
    handleSpreadsheetInitialized() {
      this.spreadsheetInitialized = true
      this.$emit('spreadsheet-initialized')
    },

    /**
     * 表格刷新完成
     */
    handleSpreadsheetRefreshed() {
      this.$emit('spreadsheet-refreshed')
    },

    /**
     * 表格导出完成
     */
    handleSpreadsheetExported(data) {
      this.$emit('spreadsheet-exported', data)
    },

    /**
     * 表格错误处理
     */
    handleSpreadsheetError(error) {
      console.error('表格错误:', error)
      this.$emit('spreadsheet-error', error)
    },

    /**
     * 获取表格实例
     */
    getSpreadsheetInstance() {
      return this.$refs.spreadsheet
    },

    /**
     * 获取表单实例
     */
    getFormInstance() {
      return this.$refs.receiptForm
    },

    /**
     * 验证表单
     */
    async validateForm() {
      if (!this.$refs.receiptForm) {
        return true
      }

      try {
        await this.$refs.receiptForm.validate()
        return true
      } catch {
        return false
      }
    },

    /**
     * 重置表单
     */
    resetForm() {
      if (this.$refs.receiptForm) {
        this.$refs.receiptForm.resetFields()
      }
    },

    /**
     * 清空表单验证
     */
    clearValidation() {
      if (this.$refs.receiptForm) {
        this.$refs.receiptForm.clearValidate()
      }
    }
  }
}
</script>

<style scoped>
.base-receipt-view {
  min-height: 100vh;
}

.receipt-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.receipt-title {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.receipt-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.info-item {
  color: #606266;
  font-size: 14px;
}

.header-right {
  flex-shrink: 0;
}

.form-section {
  margin-bottom: 20px;
}

.form-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-actions {
  display: flex;
  gap: 10px;
}

.spreadsheet-section {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer-actions {
  text-align: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer-actions .el-button {
  margin: 0 10px;
}

/* 打印样式 - 只显示表格部分 */
@media print {
  .base-receipt-view {
    padding: 0 !important;
    margin: 0 !important;
    background-color: #fff !important;
  }

  /* 隐藏头部区域 */
  .receipt-header {
    display: none !important;
  }

  /* 确保头部操作按钮被隐藏 */
  .header-right {
    display: none !important;
  }

  /* 隐藏表单区域 */
  .form-section {
    display: none !important;
  }

  /* 隐藏底部操作区域 */
  .footer-actions {
    display: none !important;
  }

  /* 表格区域样式优化 */
  .spreadsheet-section {
    margin: 0 !important;
    padding: 0 !important;
    background-color: #fff !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    border: none !important;
  }

  /* 确保表格内容正确显示 */
  .spreadsheet-section * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .base-receipt-view {
    padding: 10px;
  }

  .receipt-header {
    flex-direction: column;
    gap: 15px;
  }

  .receipt-info {
    flex-direction: column;
    gap: 10px;
  }

  .form-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
}
</style>
