/**
 * Universal数据转换器基类
 *
 * 简化架构的核心组件，提供直接生成Univer格式数据的统一接口。
 * 所有转换器都应继承此基类并实现convertToUniverData方法。
 *
 * 架构特点：
 * - 直接生成Univer格式，无中间转换步骤
 * - 统一的转换器接口，易于扩展和维护
 * - 高性能，减少数据转换开销
 *
 * @version 2.0.0
 * @since 2024
 */

import { formatDate, formatWesternDate, isValidString } from '@/utils'

export class UniversalDataConverter {
  constructor(config = {}) {
    this.config = config
    this.initializeStyles()
  }

  /**
   * 初始化样式配置（合并BaseConverter配置）
   */
  initializeStyles() {
    this.styles = {
      default: {
        bg: null,
        bl: 0,
        it: 0,
        ff: 0,
        fs: 10,
        fc: 'rgb(0, 0, 0)',
        horizontalAlign: 'left',
        verticalAlign: 'middle',
        ...this.config.defaultStyle
      },
      header: {
        bg: 'rgb(245, 247, 250)',
        borderTop: { color: '#000000', width: 1, style: 'solid' },
        borderBottom: { color: '#000000', width: 1, style: 'solid' },
        borderLeft: { color: '#000000', width: 1, style: 'solid' },
        borderRight: { color: '#000000', width: 1, style: 'solid' },
        it: 0,
        ff: 0,
        fs: 10,
        fc: 'rgb(0, 0, 0)',
        horizontalAlign: 'left',
        verticalAlign: 'middle',
        ...this.config.headerStyle
      },
      title: {
        bg: null,
        borderTop: { color: '#000000', width: 1, style: 'solid' },
        borderBottom: { color: '#000000', width: 1, style: 'solid' },
        borderLeft: { color: '#000000', width: 1, style: 'solid' },
        borderRight: { color: '#000000', width: 1, style: 'solid' },
        it: 0,
        ff: 0,
        fs: 16,
        fc: 'rgb(0, 0, 0)',
        horizontalAlign: 'center',  // 标题居中对齐
        verticalAlign: 'middle',
        ...this.config.titleStyle
      },
      subtotal: {
        bg: 'rgb(217, 225, 242)',
        borderTop: { color: '#000000', width: 1, style: 'solid' },
        borderBottom: { color: '#000000', width: 1, style: 'solid' },
        borderLeft: { color: '#000000', width: 1, style: 'solid' },
        borderRight: { color: '#000000', width: 1, style: 'solid' },
        it: 0,
        ff: 0,
        fs: 11,
        fc: 'rgb(0, 0, 0)',
        horizontalAlign: 'center',
        verticalAlign: 'middle',
        ...this.config.subtotalStyle
      },
      tableHeader: {
        bg: '#4472C4',
        fc: '#FFFFFF',
        borderTop: { color: '#FFFFFF', width: 1, style: 'solid' },
        borderBottom: { color: '#FFFFFF', width: 1, style: 'solid' },
        borderLeft: { color: '#FFFFFF', width: 1, style: 'solid' },
        borderRight: { color: '#FFFFFF', width: 1, style: 'solid' },
        it: 0,
        fs: 12,
        horizontalAlign: 'center',
        verticalAlign: 'middle',
        ...this.config.tableHeaderStyle
      }
    }
  }

  /**
   * 创建单元格数据（适配Univer格式）
   * @param {number} row 行号
   * @param {number} col 列号
   * @param {any} value 值
   * @param {object} style 样式
   * @returns {object} 单元格数据
   */
  createCell(row, col, value, style = {}) {
    // 转换样式为Univer格式
    const univerStyle = this.convertToUniverStyle(style)

    return {
      r: row,
      c: col,
      v: {
        v: value,
        ct: { fa: 'General', t: typeof value === 'number' ? 'n' : 'g' },
        m: value?.toString() || '',
        ...univerStyle
      }
    }
  }

  /**
   * 创建Univer格式的单元格数据（兼容方法）
   * @param {number} row 行号
   * @param {number} col 列号
   * @param {any} value 值
   * @param {object} style 样式配置
   * @returns {object} Univer格式的单元格数据
   */
  createUniverCell(row, col, value, style = {}) {
    return this.createCell(row, col, value, style)
  }

  /**
   * 转换样式为Univer格式
   * @param {object} style 原始样式
   * @returns {object} Univer格式样式
   */
  convertToUniverStyle(style) {
    const univerStyle = {}

    // 字体大小
    if (style.fontsize || style.fs) {
      univerStyle.fs = style.fontsize || style.fs
    }

    // 粗体
    if (style.bold !== undefined) {
      univerStyle.bl = style.bold ? 1 : 0
    } else if (style.bl !== undefined && typeof style.bl === 'number' && style.bl <= 1) {
      // 只有当bl是0或1时才作为粗体处理，否则作为边框处理
      univerStyle.bl = style.bl
    }

    // 斜体
    if (style.italic || style.it) {
      univerStyle.it = style.italic ? 1 : (style.it || 0)
    }

    // 水平对齐 - 使用Univer标准格式：horizontalAlign属性，字符串值
    if (style.horizontalAlign) {
      // 如果已经是Univer格式，直接使用
      univerStyle.horizontalAlign = style.horizontalAlign
    } else if (style.align || style.ht !== undefined) {
      // 转换旧格式
      const alignMap = { 0: 'left', 1: 'center', 2: 'right', left: 'left', center: 'center', right: 'right' }
      const alignValue = style.ht !== undefined ? alignMap[style.ht] : alignMap[style.align]
      if (alignValue) {
        univerStyle.horizontalAlign = alignValue
      }
    }

    // 垂直对齐 - 使用Univer标准格式：verticalAlign属性，字符串值
    if (style.verticalAlign) {
      // 如果已经是Univer格式，直接使用
      univerStyle.verticalAlign = style.verticalAlign
    } else if (style.valign || style.vt !== undefined) {
      // 转换旧格式
      const valignMap = { 0: 'top', 1: 'middle', 2: 'bottom', top: 'top', middle: 'middle', bottom: 'bottom' }
      const valignValue = style.vt !== undefined ? valignMap[style.vt] : valignMap[style.valign]
      if (valignValue) {
        univerStyle.verticalAlign = valignValue
      }
    }

    // 背景色
    if (style.bg || style.background) {
      univerStyle.bg = style.bg || style.background
    }

    // 字体颜色
    if (style.fc || style.color) {
      univerStyle.fc = style.fc || style.color
    }

    // 字体系列
    if (style.ff || style.fontFamily) {
      univerStyle.ff = style.ff || style.fontFamily
    }

    // 边框样式 - 处理Univer格式的边框属性
    if (style.borderTop !== undefined) {
      univerStyle.borderTop = style.borderTop
    }
    if (style.borderBottom !== undefined) {
      univerStyle.borderBottom = style.borderBottom
    }
    if (style.borderLeft !== undefined) {
      univerStyle.borderLeft = style.borderLeft
    }
    if (style.borderRight !== undefined) {
      univerStyle.borderRight = style.borderRight
    }
    
    // 处理通用边框样式（如果bl值大于1，转换为Univer边框格式）
    if (style.bl !== undefined && style.bl > 1) {
      const borderStyle = {
        color: style.borderColor || '#000000',
        width: style.bl,
        style: 'solid'
      }
      univerStyle.borderTop = borderStyle
      univerStyle.borderBottom = borderStyle
      univerStyle.borderLeft = borderStyle
      univerStyle.borderRight = borderStyle
    }

    return univerStyle
  }

  /**
   * 获取值类型
   * @param {any} value 值
   * @returns {number} 类型编号
   */
  getValueType(value) {
    if (typeof value === 'number') return 2 // 数字
    if (typeof value === 'boolean') return 4 // 布尔值
    if (value instanceof Date) return 3 // 日期
    return 1 // 字符串
  }

  /**
   * 获取水平对齐值
   * @param {string} align 对齐方式
   * @returns {number} 对齐值
   */
  getHorizontalAlignValue(align) {
    const alignMap = {
      'left': 0,
      'center': 1,
      'right': 2
    }
    return alignMap[align] || 0
  }

  /**
   * 获取垂直对齐值
   * @param {string} align 对齐方式
   * @returns {number} 对齐值
   */
  getVerticalAlignValue(align) {
    const alignMap = {
      'top': 1,
      'middle': 2,
      'bottom': 3
    }
    return alignMap[align] || 2
  }

  /**
   * 创建表头单元格
   * @param {number} row 行号
   * @param {number} col 列号
   * @param {string} value 值
   * @returns {object} 表头单元格数据
   */
  createHeaderCell(row, col, value) {
    return this.createCell(row, col, value, this.styles.header)
  }

  /**
   * 创建标题单元格
   * @param {number} row 行号
   * @param {number} col 列号
   * @param {string} value 值
   * @param {number} fontSize 字体大小
   * @returns {object} 标题单元格数据
   */
  createTitleCell(row, col, value, fontSize = 16) {
    const style = { ...this.styles.title, fs: fontSize }
    return this.createCell(row, col, value, style)
  }

  /**
   * 创建表格表头单元格
   * @param {number} row 行号
   * @param {number} col 列号
   * @param {string} value 值
   * @returns {object} 表格表头单元格数据
   */
  createTableHeaderCell(row, col, value) {
    return this.createCell(row, col, value, this.styles.tableHeader)
  }

  /**
   * 创建小计单元格
   * @param {number} row 行号
   * @param {number} col 列号
   * @param {string} value 值
   * @returns {object} 小计单元格数据
   */
  createSubtotalCell(row, col, value) {
    return this.createCell(row, col, value, this.styles.subtotal)
  }

  /**
   * 批量创建单元格
   * @param {number} startRow 起始行
   * @param {number} startCol 起始列
   * @param {array} data 二维数组数据
   * @param {object} style 样式
   * @returns {array} 单元格数组
   */
  createCellRange(startRow, startCol, data, style = {}) {
    const cells = []
    data.forEach((row, rowIndex) => {
      row.forEach((value, colIndex) => {
        cells.push(this.createCell(
          startRow + rowIndex,
          startCol + colIndex,
          value,
          style
        ))
      })
    })
    return cells
  }

  /**
   * 创建合并单元格配置
   * @param {number} startRow 起始行
   * @param {number} startCol 起始列
   * @param {number} rowSpan 行跨度
   * @param {number} colSpan 列跨度
   * @returns {object} 合并配置
   */
  createMergeConfig(startRow, startCol, rowSpan, colSpan) {
    const endRow = startRow + rowSpan - 1
    const endCol = startCol + colSpan - 1
    const key = `${startRow}_${startCol}_${endRow}_${endCol}`
    return {
      key,
      config: {
        r: startRow,
        c: startCol,
        rs: rowSpan,
        cs: colSpan
      }
    }
  }

  /**
   * 格式化日期
   * @param {Date|string} date 日期
   * @returns {string} 格式化后的日期
   */
  formatDate(date) {
    return formatDate(date)
  }

  /**
   * 格式化西式日期
   * @param {Date|string} date 日期
   * @returns {string} 格式化后的日期
   */
  formatWesternDate(date) {
    return formatWesternDate(date)
  }

  /**
   * 验证字符串有效性
   * @param {string} str 字符串
   * @returns {boolean} 是否有效
   */
  isValidString(str) {
    return isValidString(str)
  }

  /**
   * 转换为Univer格式数据
   *
   * 核心转换方法，子类必须实现此方法来直接生成Univer格式数据。
   * 返回的数据结构应包含sheets、workbook等Univer所需的完整信息。
   *
   * @param {Object} formData 表单数据 - 包含单据的基本信息
   * @param {Array} detailList 详情列表 - 包含服务项目明细
   * @param {Object} options 选项 - 转换配置选项
   * @returns {Promise<Object>} Univer格式的数据结构
   * @throws {Error} 当转换失败时抛出错误
   */
  convertToUniverData(formData, detailList = [], options = {}) {
    throw new Error('convertToUniverData method must be implemented by subclass')
  }

  /**
   * 抽象方法：转换为表格数据（兼容BaseConverter）
   * 子类必须实现此方法
   * @param {object} formData 表单数据
   * @param {array} detailList 详情列表
   * @param {object} options 额外选项
   * @returns {array} Univer格式的表格数据
   */
  convertToSpreadsheetData(formData, detailList = [], options = {}) {
    throw new Error('子类必须实现 convertToSpreadsheetData 方法')
  }

  /**
   * 获取表格配置
   * 子类可以重写此方法来提供自定义配置
   * @returns {object} 表格配置
   */
  getSheetConfig() {
    return {
      merge: {},
      rowlen: this.getDefaultRowConfig(),
      columnlen: this.getDefaultColumnConfig(),
      borderInfo: []
    }
  }

  /**
   * 获取默认的行高配置
   * @returns {object} 行高配置
   */
  getDefaultRowConfig() {
    return {
      0: 100,  // 标题行
      1: 25,  // 副标题行
      2: 25,  // 中文标题行
      3: 20   // 默认行高
    }
  }

  /**
   * 获取默认的列宽配置
   * @returns {object} 列宽配置
   */
  getDefaultColumnConfig() {
    return {
      0: 80,   // A列
      1: 100,  // B列
      2: 150,  // C列
      3: 120,  // D列
      4: 100,  // E列
      5: 120,  // F列
      6: 120,  // G列
      7: 100   // H列
      // 只配置A-H列(0-7)，符合8列限制
    }
  }
}

export default UniversalDataConverter
