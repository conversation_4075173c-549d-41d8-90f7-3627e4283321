<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="88px">
      <el-form-item :label="strConvert('类型名称')" prop="partsTypeName">
        <el-input
          v-model="queryParams.partsTypeName"
          :placeholder="strConvert('请输入配件类型名称')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('配件部位')" prop="partsPosition">
        <el-select v-model="queryParams.partsPosition" :placeholder="strConvert('请选择配件部位')" clearable>
          <el-option
            v-for="dict in dict.type.t_parts_position"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('创建者')" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          :placeholder="strConvert('请输入创建者')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')" prop="createTime">
        <el-date-picker clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="strConvert('请选择创建时间')">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['manage:partsType:add']"
        >{{ strConvert('新增') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:partsType:edit']"
        >{{ strConvert('修改') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:partsType:remove']"
        >{{ strConvert('删除') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['manage:partsType:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="partsTypeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
<!--      <el-table-column :label="strConvert('编号')" align="center" prop="id" />-->
      <el-table-column :label="strConvert('配件部位编号')" align="center" prop="partsPosition"/>
      <el-table-column :label="strConvert('配件部位')" align="center" prop="partsPosition">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_parts_position" :value="scope.row.partsPosition"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('配件类型编号')" align="center" prop="partsTypeId" />
      <el-table-column :label="strConvert('配件类型名称')" align="center" prop="partsTypeName" />
      <el-table-column :label="strConvert('备注')" align="center" prop="remark" />
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['manage:partsType:edit']"
          >{{ strConvert('修改') }}</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['manage:partsType:remove']"
          >{{ strConvert('删除') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改配件类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item :label="strConvert('配件部位')" prop="partsPosition">
          <el-select v-model="form.partsPosition" :placeholder="strConvert('请选择配件部位')">
            <el-option
              v-for="dict in dict.type.t_parts_position"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="strConvert('配件类型名称')" prop="partsTypeName">
          <el-input v-model="form.partsTypeName" :placeholder="strConvert('请输入配件类型名称')" />
        </el-form-item>
        <el-form-item :label="strConvert('备注')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { listPartsType, getPartsType, delPartsType, addPartsType, updatePartsType } from "@/api/manage/partsType"

export default {
  name: "PartsType",
  dicts: ['t_parts_position'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 配件类型表格数据
      partsTypeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partsTypeName: null,
        partsPosition: null,
        createBy: null,
        createTime: null,
        remark: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        partsTypeName: [
          { required: true, message: "配件类型名称不能为空", trigger: "blur" }
        ],
        partsPosition: [
          { required: true, message: "配件部位不能为空", trigger: "change" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询配件类型列表 */
    getList() {
      this.loading = true
      listPartsType(this.queryParams).then(response => {
        this.partsTypeList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        partsTypeId: null,
        partsTypeName: null,
        partsPosition: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.partsTypeId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加配件类型"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const partsTypeId = row.partsTypeId || this.ids
      getPartsType(partsTypeId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改配件类型"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.partsTypeId != null) {
            updatePartsType(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addPartsType(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const partsTypeIds = row.partsTypeId || this.ids
      this.$modal.confirm('是否确认删除配件类型编号为"' + partsTypeIds + '"的数据项？').then(function() {
        return delPartsType(partsTypeIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/partsType/export', {
        ...this.queryParams
      }, `partsType_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
