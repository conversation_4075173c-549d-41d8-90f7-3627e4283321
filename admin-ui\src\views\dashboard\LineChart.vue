<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { strConvert } from '@/i18n/i18nMix'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '250px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null,
      listData:[],
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.listData = Object.values(val);
          if (this.chart) {
            this.setOptions(); // 不再传递参数
          } else {
            this.initChart();
          }
        }
      },
      immediate: true // 添加立即执行
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      if (Object.keys(this.chartData).length > 0) {
        this.listData = Object.values(this.chartData);
        this.initChart();
      }
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    strConvert,
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.listData)
    },
    setOptions() {
      // 确保数据存在
      if (!this.listData || this.listData.length === 0) {
        console.warn('No chart data available');
        return;
      }

      this.chart.setOption({
        xAxis: {
          data: Object.keys(this.chartData),
          boundaryGap: false,
          axisTick: { show: false }
        },
        yAxis: { axisTick: { show: false } },
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'cross' },
          padding: [5, 10]
        },
        grid: {
          left: 10, right: 10,
          bottom: 20, top: 30,
          containLabel: true
        },
        series: [{
          name: '近一周帐面统计',
          type: 'line',
          smooth: true,
          data: this.listData, // 直接使用转换后的数据
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
            emphasis: { borderWidth: 4 }
          },
          animationDuration: 2800,
          animationEasing: 'cubicInOut'
        }]
      });
    }
  }
}
</script>
