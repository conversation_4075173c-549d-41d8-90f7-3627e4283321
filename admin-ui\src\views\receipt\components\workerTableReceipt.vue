<template>
  <div class="table-data">
    <table style="width: 735px;">
      <thead class="tableTitle">
      <tr><th style="font-weight:initial;width: 30%">{{ title}}</th><!--<th style="font-weight:initial;width: 30%">管理签字ხელ：</th>--></tr>
      </thead>
      <tbody>
      <tr>
        <td colspan="2">
        <el-table
          size="mini"
          :data="OrderQuestionDetailList"
          border
          :summary-method="getSummaries"
          :show-summary="false"
          class="table-info">
          <el-table-column label="NO" align="center" type="index" width="60" />
          <el-table-column width="200" label="服务（სამღებრო）" prop="questionName" />
        
           <el-table-column width="100" label="金额სულ"  >
              <template slot-scope="scope">
                {{scope.row.amount.toFixed(2)}} 
              </template>
            </el-table-column>
<!--      <el-table-column width="100" label="数量ნაჭერი" prop="num" />
            <el-table-column width="100" label="单价ფასი" prop="price" />-->
          <el-table-column width="90" label="შენიშვნა备注" prop="questionComment" />
        </el-table>
        </td>
        <td style="text-align: left;padding-left: 40px;">
<!--          签字ხელ：-->
          <div>费用合计/ფასი:  {{totalAmount.toFixed(2)}}  GEL</div>
        </td>
      </tr>
      </tbody>
    </table>

  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

export default {
  name: 'workerTableReceipt',
  data() {
    return {
    }
  },
  props: {
    OrderQuestionDetailList: {
      type: Array,
      default: () => []
    },
    totalAmount:{
      type:Number,
      default:0.00,
    },
    isSummary: {
      type: Boolean,
      default: true
    },
    countAmount:{
      type:Number,
      default:0.00,
    },
    ratio:{
      type:Number,
      default:0,
    },
    title: {
      type: String,
      default: ""
    }
  },
  created() {

  },
  methods: {
    strConvert,
    getSummaries(param) {
     /*  const { columns, data } = param;
      const sums = [];

      // 计算费用总和
      const totalAmount = data.reduce((sum, item) => {
        return sum + (parseFloat(item.amount) || 0);
      }, 0);

      this.totalAmount=totalAmount.toFixed(2)
      columns.forEach((column, index) => {
        if (index === 0) {
          // 第一列：显示"费用合计"并合并两列
          sums[index] = '费用合计/ფასი:  '+totalAmount.toFixed(2) + ' GEL';
          // 在Element UI 2.x中需要通过特殊方式合并
          this.$nextTick(() => {
            const footer = this.$el.querySelector('.el-table__footer');
            if (footer) {
              const cells = footer.querySelectorAll('.el-table__cell');
              if (cells.length >= 3) {
                cells[0].colSpan = 4;
                /!*    cells[1].style.display = 'none';
                   cells[2].style.display = 'none';
                   cells[3].style.display = 'none'; *!/
              }
            }
          });
        }
        else if (index === 1||index === 2) {
          // 第二列：跳过处理
          sums[index] = '';
        }
        else if (column.property === 'amount') {
          // 费用列显示合计值
          sums[index] = '';
        }
        else {
          // 其他列留空
          sums[index] = '';
        }
      });
      return sums; */
    }
  }
}
</script>

<style scoped>
/* 确保统计行样式正确 */
/*::v-deep .el-table__footer-wrapper tbody td.el-table__cell {
  background-color: #f5f7fa;
}*/
::v-deep .el-table--scrollable-x .el-table__body-wrapper{
  overflow-x:hidden;
}

::v-deep .el-table--border .el-table__cell{
  font-size: 9px;
  border: 1px solid #3b3b3b;

}

::v-deep .el-table th.el-table__cell > .cell{
  line-height:15px
}


.el-table__footer .el-table__cell:first-child {
  text-align: center;
  font-weight: bold;
}

.table-data{
  /*border: 1px solid #1e1e1e;*/
  .el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell{
    height: 14px;
    background-color: #FFFFFF;
    font-weight:normal;
  }
  .el-table--mini .el-table__cell{
    padding: 3px 0;
  }

}

.table-info{
  margin: 5px;
  width: 453px;
}

.tableTitle{
  font-size:12px;
  text-align: left;
  line-height: 18px;
  font-weight:initial;
}
</style>


