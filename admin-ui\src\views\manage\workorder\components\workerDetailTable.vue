<script >
import { strConvert } from '@/i18n/i18nMix'

export default {
  name: 'workerDetailTable',
  props: {
    saveOrderDetailList: {
      type: Array,
      default: []
    },
    isSummary: {
      type: Boolean,
      default: true
    },
    //主项总金额
    countAmount:{
      type:Number,
      default:0.00,
    },
    //当前类目金额
    totalAmount:{
      type:Number,
      default:0.00,
    },
    //当前类目提成%
    ratio:{
      type:Number,
      default:0,
    },
    tableName: {
      type: String,
      default:""
    },
    //是否允许删除等操作
    isOption: {
      type: Boolean,
      default:true
    },
  /*   updateTotalAmount:{
      type: Function,
      default: null
    }, */
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
    /*   totalAmount:0.00, */
      isMobile: false,
    }
  },
  created() {
  },

  methods: {
    strConvert,
    deleteRow(index,rows,questionName) {
      rows.splice(index, 1);
      this.$emit("resetCountData",questionName)
    },
    /** 工单详情序号 */
    rowTWorkOrderDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 复选框选中数据 */
    handleTWorkOrderDetailSelectionChange(selection) {
      this.checkedTWorkOrderDetail = selection.map(item => item.index)
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 只在合计列(最后一列)显示合并单元格
      const lastColIndex = this.isSummary ? 7 : 5; // 根据isSummary确定最后一列的索引
      if (columnIndex === lastColIndex) {
        // 只在第一行显示合计
        if (rowIndex === 0) {
          return {
            rowspan: this.saveOrderDetailList.length, // 跨所有行
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 1
          };
        }
      }
    }
  }
}
</script>

<template>
  <div class="tableChild">
    <el-table v-if="saveOrderDetailList.length>0" :data="saveOrderDetailList"
              :row-class-name="rowTWorkOrderDetailIndex"
              @selection-change="handleTWorkOrderDetailSelectionChange"
              :ref="'detail'+tableName"
              :max-height="isMobile ? '300px' : '400px'"
              border
              size="mini"
              :span-method="objectSpanMethod"
              style="width: 100%" >
      <!--              :show-header="false"-->
      <el-table-column :label="strConvert('编号')" align="center" prop="index" width="90" />
      <el-table-column width="120" :label="strConvert('服务')" prop="questionName" />
      <el-table-column width="100" :label="strConvert('数量')" prop="num" />
      <el-table-column width="80" v-if="isSummary" :label="strConvert('单价')" prop="price" />
      <el-table-column width="60" v-if="isSummary" :label="strConvert('金额')" prop="amount" align="right"/>
      <el-table-column width="100" :label="strConvert('备注')" prop="questionComment" />
      <el-table-column  :width="isOption?'80':'0'" :label="strConvert('操作')" align="center"  class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div  v-if="scope.row.entryType==='2'&&isOption" class="mobile-action-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click.native.prevent="deleteRow(scope.$index, saveOrderDetailList,scope.row.questionName)"
              v-hasPermi="['manage:workorder:edit']"
            >{{strConvert('删除')}}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="170" :label="strConvert('合计')"  align="left"  prop="totalAmount" >
        <template slot-scope="scope">
          <div >
            <span v-if="!isSummary">
               <span v-if="totalAmount>0">
                   {{countAmount}} * <span style="color: #e60000">{{ratio}}%</span> =
                    <span style="font-weight: bold;font-size: 13px;">{{totalAmount.toFixed(2)}} GEL </span>
               </span>
            </span>
            <span v-else>
              <span style="font-weight: bold;font-size: 13px;">{{totalAmount.toFixed(2)}} GEL </span>（<span style="color: #e60000">{{ratio}}%</span>）
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss">
.tableChild{
  margin-right: 15px;
}
</style>
