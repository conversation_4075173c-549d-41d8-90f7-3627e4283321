<template>
  <div v-if="isShow">
    <template v-if="hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow">
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{'submenu-title-noDropdown':!isNest}"
          :style="menuItemStyle"
        >
          <item :icon="onlyOneChild.meta.icon||(item.meta&&item.meta.icon)" :title="onlyOneChild.meta.title" />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
      :style="submenuStyle"
    >
      <template slot="title">
        <item v-if="item.meta"  :icon="item.meta && item.meta.icon" :title="languageTitle" />
      </template>
      <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import FixiOSBug from './FixiOSBug'
import Cookies from 'js-cookie'
import { isValidString } from '@/utils'
import { ensureLocaleLoaded, strConvert } from '@/i18n/i18nMix'
import store from '@/store'
import variables from '@/assets/styles/variables.js'
import { mapState } from 'vuex'
export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  computed: {
    ...mapState(["settings"]),
    variables() {
      return variables
    },
    menuItemStyle() {
      const isDark = this.settings.sideTheme === 'theme-dark'
      const vars = this.variables
      return {
        backgroundColor: isDark ? vars.menuBackground : vars.menuLightBackground,
        color: isDark ? vars.menuColor : vars.menuLightColor
      }
    },
    submenuStyle() {
      const isDark = this.settings.sideTheme === 'theme-dark'
      const vars = this.variables
      return {
        backgroundColor: isDark ? vars.menuBackground : vars.menuLightBackground,
        color: isDark ? vars.menuColor : vars.menuLightColor
      }
    }
  },
  data() {
    this.onlyOneChild = null
    return {
      languageTitle:"",
      isShow:false,
    }
  },
  created() {
    const language = Cookies.get("language")
    if(isValidString(language)&&this.item.meta!==undefined){
      ensureLocaleLoaded(language)
      setTimeout(
        () => {
          this.$i18n.locale=language;
          this.languageTitle=strConvert(this.item.meta.title)
          this.isShow=!this.item.hidden;
        }, 1000)
    }else{
      this.isShow=!this.item.hidden;
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      if (!children) {
        children = []
      }
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        }
        // Temp set(will be used if only has one showing child)
        this.onlyOneChild = item
        return true
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    resolvePath(routePath, routeQuery) {
      if (isExternal(routePath)) {
        return routePath
      }
      if (isExternal(this.basePath)) {
        return this.basePath
      }
      if (routeQuery) {
        let query = JSON.parse(routeQuery)
        return { path: path.resolve(this.basePath, routePath), query: query }
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
