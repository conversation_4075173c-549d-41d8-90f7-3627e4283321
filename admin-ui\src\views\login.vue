<template>
  <div class="login">
    <el-form ref="loginForm" label-width="80px" label-position="top" :model="loginForm" :rules="loginRules" class="login-form">
<!--      <h3 class="title">{{strConvert(title)}}</h3>-->
      <el-form-item prop="language" >
        <span slot="label">{{strConvert('语言')}}:</span>
        <el-select v-model="loginForm.language" :placeholder="strConvert('请选择')" style="width: 100%" @change="changeLanguage(loginForm.language)">
          <el-option label="中文" value="zh">
            <span class="flag-icon flag-icon-cn"  style="margin-right: 6px;"></span>
            中文
          </el-option>
          <el-option label="English" value="en">
            <span class="flag-icon flag-icon-us" style="margin-right: 6px;"></span>
            English
          </el-option>
          <el-option label="Русский" value="ru">
            <span class="flag-icon flag-icon-ru" style="margin-right: 6px;"></span>
            Русский
          </el-option>
          <el-option label="ქართული" value="ka">
            <span class="flag-icon flag-icon-ge" style="margin-right: 6px;"></span>
            ქართული
          </el-option>
        </el-select>

      </el-form-item>
      <el-form-item prop="username" >
        <span slot="label">{{strConvert('账号')}}:</span>
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          :placeholder="strConvert('账号')"
        >
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password" >
        <span slot="label">{{strConvert('密码')}}:</span>
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          :placeholder="strConvert('密码')"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          :placeholder="strConvert('验证码')"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">{{strConvert('记住密码')}}</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">{{strConvert('登 录')}}</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2025 usedcar.vip All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login"
import Cookies from "js-cookie"
import { encrypt, decrypt } from '@/utils/jsencrypt'
import i18n from '@/i18n/i18n'
import { ensureLocaleLoaded, strConvert } from '@/i18n/i18nMix'
export default {
  name: "Login",
  components:{
    i18n
  },
  data() {
    return {
      title: process.env.VUE_APP_TITLE,
      codeUrl: "",
      loginForm: {
        username: "",
        language:"",
        password: "",
        rememberMe: false,
        code: "",
        uuid: ""
      },
      loginRules: {
        language: [
          { required: true, trigger: "blur", message: "语言不能为空" }
        ],
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.loginForm.language="zh"
    this.getCode()
    this.getCookie()
  },
  computed:{

  },
  methods: {
    strConvert,
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    },
    changeLanguage(language){
      this.$i18n.locale=language;
    },
    getCookie() {
      const language = Cookies.get("language")
      const username = Cookies.get("username")
      const password = Cookies.get("password")
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        language: language === undefined ? this.loginForm.language : language,
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          Cookies.set("language", this.loginForm.language, { expires: 30 })
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 })
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 })
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 })
          } else {
            Cookies.remove("username")
            Cookies.remove("password")
            Cookies.remove('rememberMe')
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(()=>{})
          }).catch(() => {
            this.loading = false
            if (this.captchaEnabled) {
              this.getCode()
            }
          })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  padding-top: 5%;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  z-index: 1;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}
</style>
