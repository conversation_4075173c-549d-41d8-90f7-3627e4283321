import request from '@/utils/request'

// 查询工单记录列表
export function listWorkorder(query) {
  return request({
    url: '/manage/workorder/list',
    method: 'get',
    params: query
  })
}

// 查询工单记录列表
export function optionListWorkorder(query) {
  return request({
    url: '/manage/workorder/optionList',
    method: 'get',
    params: query
  })
}


// 查询工单记录列表
export function detailListWorkorder(query) {
  return request({
    url: '/manage/workorder/detailList',
    method: 'get',
    params: query
  })
}

// 查询工单记录详细
export function getWorkorder(serviceId) {
  return request({
    url: '/manage/workorder/' + serviceId,
    method: 'get'
  })
}


// 根据id查询派工单明细记录
export function getOrderDetailInfo(detailId) {
  return request({
    url: '/manage/workorder/getOrderDetailInfo/' + detailId,
    method: 'get'
  })
}

// 新增工单记录
export function addWorkorder(data) {
  return request({
    url: '/manage/workorder',
    method: 'post',
    data: data
  })
}

// 派工单补充服务项目时调用添加服务记录
export function updateTWorkOrderDetail(data) {
  return request({
    url: '/manage/workorder/updateTWorkOrderDetail',
    method: 'put',
    data: data
  })
}


// 进度过程中添加配件信息
export function updateTWorkPartsDetail(data) {
  return request({
    url: '/manage/workorder/updateTWorkPartsDetail',
    method: 'put',
    data: data
  })
}


// 修改工单记录
export function updateWorkorder(data) {
  return request({
    url: '/manage/workorder',
    method: 'put',
    data: data
  })
}

// 发布工单
export function releaseWorkorder(data) {
  return request({
    url: '/manage/workorder/release',
    method: 'post',
    data: data
  })
}


// 工单指派
export function tWorkOrderAssign(data) {
  return request({
    url: '/manage/workorder/tWorkOrderAssign',
    method: 'post',
    data: data
  })
}


// 工单指派
export function tWorkOrderProgress(data) {
  return request({
    url: '/manage/workorder/progress',
    method: 'post',
    data: data
  })
}

// 工单指派
export function tWorkResultProgress(data) {
  return request({
    url: '/manage/workorder/resultProgress',
    method: 'post',
    data: data
  })
}


// 结算工单
export function settlementWorkorder(data) {
  return request({
    url: '/manage/workorder/settlement',
    method: 'post',
    data: data
  })
}

// 完结工单（汇总并保存相关数据）
export function endWorkorder(data) {
  return request({
    url: '/manage/workorder/end',
    method: 'post',
    data: data
  })
}

// 工单作废（并清空资金流水相关信息）
export function discardWorkorder(data) {
  return request({
    url: '/manage/workorder/discard',
    method: 'post',
    data: data
  })
}

// 删除工单记录
export function delWorkorder(serviceId) {
  return request({
    url: '/manage/workorder/' + serviceId,
    method: 'delete'
  })
}
