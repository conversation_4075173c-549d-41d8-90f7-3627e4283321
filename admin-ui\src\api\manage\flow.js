import request from '@/utils/request'

// 查询资金流水列表
export function listFlow(query) {
  return request({
    url: '/manage/flow/list',
    method: 'get',
    params: query
  })
}

// 查询资金流水详细
export function getFlow(flowId) {
  return request({
    url: '/manage/flow/' + flowId,
    method: 'get'
  })
}


// 统计资金流水
export function countAmountDayAndMonth(flowId) {
  return request({
    url: '/manage/flow/countAmountDayAndMonth',
    method: 'get'
  })
}

// 新增资金流水
export function addFlow(data) {
  return request({
    url: '/manage/flow',
    method: 'post',
    data: data
  })
}

// 修改资金流水
export function updateFlow(data) {
  return request({
    url: '/manage/flow',
    method: 'put',
    data: data
  })
}

// 删除资金流水
export function delFlow(flowId) {
  return request({
    url: '/manage/flow/' + flowId,
    method: 'delete'
  })
}
