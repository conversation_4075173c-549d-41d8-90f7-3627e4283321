/**
 * 国际化样式文件
 * 根据不同语言提供专属的样式定义
 * 使用方式：在body或html元素上添加对应的语言类
 */

// 中文样式
.lang-zh {
  // 字体设置
  font-family: "Microsoft YaHei", "微软雅黑", "PingFang SC", "Hiragino Sans GB", "Heiti SC", "WenQuanYi Micro Hei", sans-serif;
  
  // 文本间距
  letter-spacing: 0;
  
  // 行高
  line-height: 1.5;
  
  // 特定组件样式调整
  .card-panel-description {
    margin: 26px;
  }
  
  // 按钮尺寸
  .el-button {
    &.size-medium {
      padding: 10px 20px;
      font-size: 14px;
    }
  }
  
  // 表单标签宽度
  .el-form-item__label {
    min-width: 80px;
  }
  
  // 栅格间距
  .panel-group {
    .el-col {
      margin-bottom: 20px;
    }
  }
}

// 英文样式
.lang-en {
  // 字体设置
  font-family: "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  
  // 文本间距
  letter-spacing: 0.5px;
  
  // 行高
  line-height: 1.6;
  
  // 特定组件样式调整
  .card-panel-description {
    margin: 26px 6px;
  }
  
  // 按钮尺寸
  .el-button {
    &.size-small {
      padding: 8px 16px;
      font-size: 12px;
    }
  }
  
  // 表单标签宽度
  .el-form-item__label {
    min-width: 120px;
  }
  
  // 栅格间距
  .panel-group {
    .el-col {
      margin-bottom: 16px;
    }
  }
}

// 阿拉伯语样式（RTL支持）
.lang-ar {
  // 文本方向
  direction: rtl;
  text-align: right;
  
  // 字体设置
  font-family: "Tahoma", "Arial", "Microsoft Sans Serif", sans-serif;
  
  // 文本间距
  letter-spacing: 0;
  
  // 行高
  line-height: 1.8;
  
  // 特定组件样式调整
  .card-panel-description {
    margin: 26px 6px;
    text-align: right;
  }
  
  // 按钮样式
  .el-button {
    &.size-small {
      padding: 8px 16px;
      font-size: 12px;
    }
  }
  
  // 表单样式
  .el-form-item__label {
    min-width: 100px;
    text-align: right;
  }
  
  // 图标翻转
  .el-icon-arrow-right {
    transform: scaleX(-1);
  }
  
  .el-icon-arrow-left {
    transform: scaleX(-1);
  }
}

// 俄语样式
.lang-ru {
  // 字体设置
  font-family: "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  
  // 文本间距
  letter-spacing: 0.3px;
  
  // 行高
  line-height: 1.6;
  
  // 特定组件样式调整
  .card-panel-description {
    margin: 26px 6px;
  }
  
  // 按钮尺寸
  .el-button {
    &.size-small {
      padding: 8px 16px;
      font-size: 12px;
    }
  }
  
  // 表单标签宽度
  .el-form-item__label {
    min-width: 140px;
  }
}

// 通用样式优化
.lang-zh,
.lang-en,
.lang-ar,
.lang-ru {
  // 平滑过渡效果
  transition: all 0.3s ease;
  
  // 确保字体渲染质量
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  // 文本选择样式
  ::selection {
    background-color: #409eff;
    color: #fff;
  }
}

// 全局表格表头文字换行优化
.el-table {
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      // 按整个单词折行，避免拆开单词
      word-break: keep-all !important;
      word-wrap: break-word !important;
      white-space: normal !important;
      
      .cell {
        word-break: keep-all !important;
        word-wrap: break-word !important;
        white-space: normal !important;
        line-height: 1.4 !important;
      }
    }
  }
}

// 全局表单样式优化 - 覆盖移动端适配样式
.el-form.mobile-form-adapt.el-form--inline .el-form-item,
.el-form.el-form--inline .el-form-item {
  width: calc(33.333% - 10px) !important;
  margin-right: 10px !important;
  margin-bottom: 15px !important;
  display: inline-block !important;
  vertical-align: top !important;
  min-width: 280px !important;

  .el-form-item__label {
    display: inline-block !important;
    width: 240px !important;
    text-align: right !important;
    margin-right: 8px !important;
    vertical-align: top !important;
    line-height: 32px !important;
    margin-bottom: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .el-form-item__content {
    display: inline-block !important;
    width: calc(100% - 248px) !important;
    vertical-align: top !important;
    margin-left: 0 !important;

    .el-input, .el-select, .el-date-editor {
      width: 200px !important;
      max-width: 100% !important;
    }
  }
}

// 强制覆盖移动端媒体查询样式
@media screen and (min-width: 769px) {
  .el-form.mobile-form-adapt.el-form--inline .el-form-item,
  .el-form.el-form--inline .el-form-item {
    display: inline-block !important;
    
    .el-form-item__label {
      width: 240px !important;
      text-align: right !important;
      margin-bottom: 0 !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
    
    .el-form-item__content {
      margin-left: 0 !important;
      width: calc(100% - 248px) !important;
      
      .el-input, .el-select, .el-date-editor {
        width: 200px !important;
        max-width: 100% !important;
      }
    }
  }
}