/**
 * Univer 动态边框功能测试
 * 
 * 这个测试文件演示了如何使用新的动态边框功能
 */

import { AcceptReceiptUniversalConverter } from '../src/utils/spreadsheet/AcceptReceiptUniversalConverter.js';

/**
 * 测试基础边框添加功能
 */
function testBasicBorderAddition() {
  console.log('🧪 测试基础边框添加功能');
  
  const converter = new AcceptReceiptUniversalConverter();
  const cellData = [];
  
  // 添加一些测试数据
  cellData.push(converter.createCell(3, 0, '测试数据1'));
  cellData.push(converter.createCell(3, 1, '测试数据2'));
  cellData.push(converter.createCell(4, 0, '测试数据3'));
  cellData.push(converter.createCell(4, 1, '测试数据4'));
  
  // 添加基础边框
  converter.addDynamicBorders(cellData, {
    startRow: 3,
    endRow: 4,
    startCol: 0,
    endCol: 1,
    borderStyle: {
      color: '#000000',
      width: 1,
      style: 'solid'
    },
    borderTypes: ['all'],
    regionType: '测试区域'
  });
  
  console.log('✅ 基础边框添加测试完成');
  return cellData;
}

/**
 * 测试边框样式预设
 */
function testBorderStylePresets() {
  console.log('🧪 测试边框样式预设');
  
  const converter = new AcceptReceiptUniversalConverter();
  
  // 测试所有预设样式
  const presets = ['default', 'thick', 'thin', 'dashed', 'dotted', 'tableHeader', 'tableData'];
  
  presets.forEach(preset => {
    const style = converter.getBorderStylePreset(preset);
    console.log(`📋 ${preset} 样式:`, style);
  });
  
  console.log('✅ 边框样式预设测试完成');
}

/**
 * 测试智能边框添加
 */
function testSmartBorders() {
  console.log('🧪 测试智能边框添加');
  
  const converter = new AcceptReceiptUniversalConverter();
  const cellData = [];
  
  // 创建测试单元格
  const testCell = converter.createCell(5, 5, '智能边框测试');
  cellData.push(testCell);
  
  const borderStyle = {
    color: '#FF0000',
    width: 2,
    style: 'solid'
  };
  
  // 测试不同的边框类型
  const borderTypes = [
    ['top'],
    ['bottom'],
    ['left'],
    ['right'],
    ['top', 'bottom'],
    ['left', 'right'],
    ['all'],
    ['inner']
  ];
  
  borderTypes.forEach(types => {
    const testCellCopy = { ...testCell };
    converter.addSmartBorders(
      testCellCopy, 5, 5, 5, 5, 5, 5,
      borderStyle, types
    );
    console.log(`📋 边框类型 ${types.join(',')} 测试完成`);
  });
  
  console.log('✅ 智能边框添加测试完成');
}

/**
 * 测试边框管理器
 */
function testBorderManager() {
  console.log('🧪 测试边框管理器');
  
  const converter = new AcceptReceiptUniversalConverter();
  const cellData = [];
  
  // 添加测试数据
  for (let row = 0; row < 10; row++) {
    for (let col = 0; col < 5; col++) {
      cellData.push(converter.createCell(row, col, `R${row}C${col}`));
    }
  }
  
  // 注册多个边框区域
  converter.borderManager.registerBorderRegion(
    '区域1',
    0, 2, 0, 4,
    {
      borderStyle: converter.getBorderStylePreset('thick'),
      borderTypes: ['all'],
      priority: 1
    }
  );
  
  converter.borderManager.registerBorderRegion(
    '区域2',
    3, 5, 0, 4,
    {
      borderStyle: converter.getBorderStylePreset('dashed'),
      borderTypes: ['top', 'bottom'],
      priority: 2
    }
  );
  
  converter.borderManager.registerBorderRegion(
    '高优先级区域',
    1, 1, 1, 3,
    {
      borderStyle: converter.getBorderStylePreset('tableHeader'),
      borderTypes: ['all'],
      priority: 10
    }
  );
  
  console.log(`📋 注册了 ${converter.borderManager.getRegionCount()} 个边框区域`);
  
  // 应用所有边框
  converter.borderManager.applyAllBorders(cellData, converter);
  
  console.log('✅ 边框管理器测试完成');
  return cellData;
}

/**
 * 测试表格边框
 */
function testTableBorders() {
  console.log('🧪 测试表格边框');
  
  const converter = new AcceptReceiptUniversalConverter();
  const cellData = [];
  
  // 创建表格数据
  const headers = ['列1', '列2', '列3', '列4'];
  const data = [
    ['数据1', '数据2', '数据3', '数据4'],
    ['数据5', '数据6', '数据7', '数据8'],
    ['数据9', '数据10', '数据11', '数据12']
  ];
  
  // 添加表头
  headers.forEach((header, col) => {
    cellData.push(converter.createCell(0, col, header));
  });
  
  // 添加数据
  data.forEach((row, rowIndex) => {
    row.forEach((cell, col) => {
      cellData.push(converter.createCell(rowIndex + 1, col, cell));
    });
  });
  
  // 添加表格边框
  converter.addTableBorders(
    cellData,
    0,  // 表头行
    1,  // 数据开始行
    3,  // 数据结束行
    0,  // 起始列
    3,  // 结束列
    {
      headerBorderStyle: 'tableHeader',
      dataBorderStyle: 'tableData',
      outerBorderStyle: 'thick'
    }
  );
  
  console.log('✅ 表格边框测试完成');
  return cellData;
}

/**
 * 测试高亮边框
 */
function testHighlightBorders() {
  console.log('🧪 测试高亮边框');
  
  const converter = new AcceptReceiptUniversalConverter();
  const cellData = [];
  
  // 创建测试数据
  for (let row = 0; row < 5; row++) {
    for (let col = 0; col < 5; col++) {
      cellData.push(converter.createCell(row, col, `${row}-${col}`));
    }
  }
  
  // 添加高亮边框
  converter.addHighlightBorders(cellData, 1, 3, 1, 3, '#FF0000');
  
  console.log('✅ 高亮边框测试完成');
  return cellData;
}

/**
 * 运行所有测试
 */
function runAllTests() {
  console.log('🚀 开始运行 Univer 动态边框功能测试');
  console.log('='.repeat(50));
  
  try {
    testBasicBorderAddition();
    testBorderStylePresets();
    testSmartBorders();
    testBorderManager();
    testTableBorders();
    testHighlightBorders();
    
    console.log('='.repeat(50));
    console.log('🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined') {
  runAllTests();
}

export {
  testBasicBorderAddition,
  testBorderStylePresets,
  testSmartBorders,
  testBorderManager,
  testTableBorders,
  testHighlightBorders,
  runAllTests
};
