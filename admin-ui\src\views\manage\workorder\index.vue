<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item :label="strConvert('工单编号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('工单标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入工单标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('业务进度')" prop="businessType">
        <el-select v-model="queryParams.businessType" :placeholder="strConvert('请选择业务进度')" clearable>
          <el-option
            v-for="dict in dict.type.t_business_type"
            :key="dict.value"
            :label="strConvert(dict.label)"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('客户编号')" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          :placeholder="strConvert('请输入客户编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户姓名')" prop="customerNickName">
        <el-input
          v-model="queryParams.customerNickName"
          :placeholder="strConvert('请输入客户姓名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('车牌号')" prop="carPlateNumber">
        <el-input
          v-model="queryParams.carPlateNumber"
          :placeholder="strConvert('请输入车牌号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="strConvert('开始日期')"
          :end-placeholder="strConvert('结束日期')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{strConvert('搜索')}}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{strConvert('重置')}}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="workorderList" @selection-change="handleSelectionChange">
<!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column :label="strConvert('工单编号')"  width="155"  align="center" prop="serviceId" />
      <el-table-column :label="strConvert('工单标题')" width="250" align="center" prop="title" />
      <el-table-column :label="strConvert('业务进度')" align="center" width="200" prop="businessType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_business_type" :value="scope.row.businessType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('进度')" align="center" width="80" prop="operProgress">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_oper_progress" :value="scope.row.operProgress"/>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('操作人员')" align="center" prop="operName" />-->
      <el-table-column :label="strConvert('车牌号')" width="90" align="center" prop="carPlateNumber" />
<!--      <el-table-column :label="strConvert('客户编号')" width="80" align="center" prop="customerId" />-->
      <el-table-column :label="strConvert('客户姓名')" width="100" align="center" prop="customerNickName" />
<!--      <el-table-column :label="strConvert('预收款')" width="100" align="center" prop="advancePayment" />
      <el-table-column :label="strConvert('尾款')" width="100" align="center" prop="balancePayment" />-->
      <el-table-column :label="strConvert('总金额')" width="100" align="center" prop="amount" />
<!--      <el-table-column :label="strConvert('结算人')" width="100" align="center" prop="settlementBy" />-->
      <el-table-column :label="strConvert('结算时间')" align="center" prop="settlementTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.settlementTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('创建者')"  width="80"  align="center" prop="createBy" />-->
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('备注')" align="center" prop="remark" />-->
      <el-table-column :label="strConvert('操作')" width="180" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">

          <el-dropdown split-button type="info"  @command="(item)=>goPrint(item,scope.row)">
            {{strConvert('单据打印')}}
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="acceptReceipt">{{strConvert('受理单打印')}}</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.businessType!=='0'" command="workerReceipt">{{strConvert('派工单打印')}}</el-dropdown-item>
              <el-dropdown-item v-if="scope.row.businessType==='4'||scope.row.businessType==='5'" command="settlementReceipt">{{strConvert('结算单打印')}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            type="text"
            icon="el-icon-s-opportunity"
            @click="gotSInfo(scope.row)"
          >{{strConvert('查看')}}</el-button>
          <el-button
            type="text"
            v-if="($store.getters.roles.indexOf('admin')>=0||$store.getters.roles.indexOf('boss')>=0)"
            icon="el-icon-error"
            @click="discardWorkor(scope.row)"
          >{{strConvert('作废')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

   <receipt  show="index" :serviceId="serviceId" :printType="printType"   :OpenPrint.sync="open" />

  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'
import { sprintf } from 'sprintf-js'
import {
  optionListWorkorder,
  getWorkorder,
  delWorkorder,
  addWorkorder,
  updateWorkorder,
  discardWorkorder
} from '@/api/manage/workorder'
import { listCustomer, addCustomer, getInfoByNickName } from '@/api/manage/customer'
import { getInfo } from '@/api/login'
import acceptReceipt from '@/views/receipt/acceptReceipt.vue'
import WorkerReceipt from '@/views/receipt/workerReceipt.vue'
import settlementReceipt from '@/views/receipt/settlementReceipt.vue'
import Receipt from '@/views/receipt/components/index.vue'
export default {
  name: "Workorder",
  components: { Receipt, settlementReceipt, WorkerReceipt, acceptReceipt },
  dicts: ['sys_oper_type', 't_parts_type', 't_business_type', 'sys_common_status', 't_res_type', 't_is_buy_parts', 't_oper_progress', 't_currency_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      isEdit:true,
      // 选中数组
      ids: [],
      example:
        "样例1：5月25日接到奔驰引擎盖喷漆工单，检车后发现需要喷漆处理，ვანგ მინგი，558411526，KYB7673S\n"+
        "样例2：5月25日接到奔驰引擎盖喷漆工单@检车后发现需要喷漆处理@ვანგ მინგი@558411526@KYB7673S\n"+
         "样例3：5月25日接到奔驰引擎盖喷漆工单!检车后发现需要喷漆处理!ვანგ მინგი!558411526!KYB7673S",
      // 子表选中数据
      checkedTWorkOrderDetail: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      restCustomer: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单记录表格数据
      workorderList: [],
      // 表单参数
      form: {},

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 备注时间范围
      daterangeCreateTime: [],
      serviceId:"",
      printType:"",//打印单据类型
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceId: null,
        title: null,
        businessType: null,
        carPlateNumber:null,
        customerId: null,
        customerNickName: null,
        createTime: null,
      },
    }
  },
  created() {
    console.log(this.$store.getters.roles.indexOf('admin'),this.$store.getters.roles.indexOf('boss'))
    console.log((this.$store.getters.roles.indexOf('admin')>=0||this.$store.getters.roles.indexOf('boss')>=0))
    console.log(this.$store.getters.roles)
    this.getList()
  },
  methods: {
    strConvert,
    sprintf,
    /** 查询工单记录列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      /**此处添加角色权限过滤*/
      if(this.$store.getters.roles.indexOf('common')>=0){
        this.queryParams.operName=this.$store.getters.name;
      }
      optionListWorkorder(this.queryParams).then(response => {
        this.workorderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.serviceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const serviceIds = row.serviceId || this.ids
      this.$modal.confirm(this.sprintf(this.strConvert('是否确认删除工单记录编号为 %s 的数据项？'),serviceIds)).then(function() {
        return delWorkorder(serviceIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.strConvert('删除成功'))
      }).catch(() => {})
    },
    /** 工单作废 */
    discardWorkor(row) {
      const serviceIds = row.serviceId;
      this.$modal.confirm(this.sprintf(this.strConvert('确认是否作废工单编号为 %s 的工单？作废后将删除相关财务流水'),serviceIds)).then(function() {
        return discardWorkorder(row)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.strConvert('操作成功'))
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/workorder/export', {
        ...this.queryParams
      }, `workorder_${new Date().getTime()}.xlsx`)
    },
    /** 去查看 */
    gotSInfo(row) {
      const serviceId = row.serviceId || this.ids
      this.$router.push(`/manage/infoForm/info/`+serviceId);
    },
    goPrint(selectData,row){
      this.serviceId = row.serviceId || this.ids
      this.printType=selectData;
      this.open = true
    },
  }
}
</script>
