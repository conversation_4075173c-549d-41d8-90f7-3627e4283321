<template>
  <!-- 结算单据模板 -->
  <div v-if="open">
        <el-row>
          <div class="file-header">
            <img v-if="fileLog" :src="fileLog" class="file-logo" />
            <div> მანქანის მიტანის ანგარიშსწორების დეტალები/<br>汽车交付结算明细单</div>
          </div>
        </el-row>

        <div class="app-container">
          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="  grid-bottom">
                მიღების დრო时间: <span>  {{ FormData.title }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-right  grid-bottom">
                №/工单编号: <span>  {{ FormData.serviceId }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="grid-left  grid-bottom">
                მფლობელი车主: <span>  {{ FormData.customerNickName }}</span>
              </div>
            </el-col>

            <el-col :span="6">
              <div class="grid-right  grid-bottom">
                ა/მ მარკა品牌:  <span>  {{ FormData.brand }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="grid-right  grid-bottom">
                ფერი/ 颜色： <span>  {{ FormData.color }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="grid-left  grid-bottom">
                ტელეფონი电话: <span>  {{ FormData.phonenumber }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-right  grid-bottom">
                სახ. №车牌号: <span>  {{ FormData.carPlateNumber }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="grid-left  grid-bottom">
                მიღ.თარ/进厂日期：<span>  {{ formatDate(FormData.createTime) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-right  grid-bottom">
                შესრ.თარ/取车日期： <span>  {{ formatDate(FormData.expectedTime) }}</span>
              </div>
            </el-col>
          </el-row>

          <div class="app-table-list" >
            <div class="tables-border">
              <el-row type="flex"   v-for="detail in T_WORK_ORDER_SERVICE_TYPE"  :key="detail.value">
                <table-receipt class="row-table" :title="detail.label" :order-question-detail-list="detail[detail.value]"/>
              </el-row>
              <parts-table-receipt v-if="OrderPartsDetailList.length>0" class="row-table" title="配件明细" :order-question-detail-list="OrderPartsDetailList"/>

              <el-table  size="mini" border :data="[FormData]"  style="width: 100%">
                <el-table-column  prop="totalAmount"  width="90">
                  <template slot="header" slot-scope="scope">
                    <span class="totalHeader">სულ总额:</span>
                  </template>
                  <template slot-scope="scope">
                    {{scope.row.totalAmount}} GEL
                  </template>
                </el-table-column>
                <el-table-column  class="totalHeader" prop="discountAmount" label=""  width="190">
                  <template slot="header" slot-scope="scope">
                    <span class="totalHeader">ფასდ/შემდეგ(%) 折后金额:</span>
                  </template>
                  <template slot-scope="scope">
                    {{scope.row.discountAmount}} GEL
                  </template>
                </el-table-column>
                <el-table-column  prop="advancePayment"  width="100">
                  <template slot="header" slot-scope="scope">
                    <span class="totalHeader">ავანსი预付:</span>
                  </template>
                  <template slot-scope="scope">
                    {{scope.row.advancePayment}} GEL
                  </template>
                </el-table-column>
                <el-table-column  prop="balancePayment"  width="180">
                  <template slot="header" slot-scope="scope">
                    <span class="totalHeader">დამატ/გადახდა折扣后含税金额:</span>
                  </template>
                  <template slot-scope="scope">
                    {{scope.row.balancePayment}} GEL
                  </template>
                </el-table-column>
                <el-table-column  prop="balance" width="150">
                  <template slot="header" slot-scope="scope">
                    <span class="totalHeader">დარჩენილი余额:</span>
                  </template>
                  <template slot-scope="scope">
                    {{scope.row.balance}} GEL
                  </template>
                </el-table-column>
              </el-table>
              <el-table  size="mini" border :data="[FormData]"  :show-header="false" style="width: 100%">
                <el-table-column  prop="mainTotalAmount" >
                  <template slot-scope="scope">
                    主项合计金额：{{scope.row.mainTotalAmount}}GEL <span >{{totalText}}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!--  签字栏位   -->
          <div style="margin-top: 10px;">
            <el-row type="flex" style="line-height: 30px">
              {{ SIGNATURE_TEXTS.OWNER_SIGNATURE }}
            </el-row>
            <el-row type="flex" style="line-height: 30px">
              <el-col :span="18">
                {{ SIGNATURE_TEXTS.STAFF_SIGNATURE }}
              </el-col>
              <el-col :span="6">
                {{ SIGNATURE_TEXTS.PHONE_NUMBER }}
              </el-col>
            </el-row>
          </div>

          <el-row>
            <div style="margin-top: 25px;margin-right:45px;text-align: right;font-size: 15px">
              <p>  European Mazda Automobile Trading Co., Ltd.</p>
              <p>{{ formatWesternDate(new Date()) }}</p>
            </div>
          </el-row>
        </div>

        <!-- 强制分页 -->
        <!--        <div class="page-break"></div>-->

        <!-- 需要保持在一起的内容 -->
        <!--
          <div class="avoid-page-break">
          这些内容不会被分到两页...
        </div>-->

        <div v-if="isValidString(FormData.comment)&&isValidString(FormData.images)" class="app-container">
          <el-row>
            <div style="text-align: center;font-weight: bold;font-size: 18px"><p> 附件信息დანართის ინფორმაცია</p></div>
          </el-row>
          <el-descriptions   :column="1"  border>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                დამატებითი ინფორმაცია问题说明:
              </template>
              {{ FormData.comment }}
            </el-descriptions-item>
          </el-descriptions>
          <el-row v-if="optionPrint" class="avoid-break">
            <div style="margin: 8px">
              <ImageCustomPreview
                :src="FormData.images"
                :width="730"
                :height="600"
                pdf-optimized
              />
            </div>
          </el-row>
        </div>
  </div>
</template>

<script>
import { formatDate, formatWesternDate, isValidString } from '@/utils'
import i18n from '@/i18n/i18n'
import { strConvert } from '@/i18n/i18nMix'
import file_log from '@/assets/logo/fileLog.png'
import TableReceipt from '@/views/receipt/components/tableReceipt.vue'
import ImageCustomPreview from '@/components/ImageCustomPreview/index.vue'
import PartsTableReceipt from '@/views/receipt/components/partsTableReceipt.vue'
import { SIGNATURE_TEXTS } from '@/constants/signatureConstants.js'

export default {
  name: 'acceptReceipt',
  dicts: ['t_work_question_service_type'],
  components:{
    PartsTableReceipt,
    ImageCustomPreview,
    TableReceipt,
    i18n
  },
  props: {
    open: {
      type: Boolean,
      default: false
    },
    OrderDetailList: {
      type: Array,
      default: []
    },
    OrderPartsDetailList: {
      type: Array,
      default: []
    },
    // 表单参数
    FormData: {
      type: Object,
      default: () => ({
        serviceId: null,
        title: null,
        businessType: "0",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        phonenumber:null,
        advancePayment:0,//预收金额
        discountAmount: 0,//折后金额
        totalAmount: 0,//总金额
        balancePayment: 0,//再付金额
        balance:0,//余额
        comment:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      })
    },
  },
  data() {
    return {
      SIGNATURE_TEXTS, // 签名文本常量
      baseUrl: process.env.VUE_APP_BASE_API,
      loading:true,
      totalText: "",//统计数据文本
      rowKey:Math.random().toFixed(5).toString(),// 强制重新渲染
      saveOrderDetailList:[],
      T_WORK_ORDER_SERVICE_TYPE:[],
      typeOrderDetailMapList:{},
      fileLog:file_log,
      optionPrint:true,
    }
  },
  created() {
  },
  watch: {
    'OrderDetailList': {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.generateWorkOrderServiceType(newVal);
        }
      }
    },
  },
  methods: {
    isValidString,
    formatWesternDate,
    formatDate,
    strConvert,
    generateWorkOrderServiceType(detailList) {
      this.T_WORK_ORDER_SERVICE_TYPE = []; // 清空旧数据
      this.dict.type.t_work_question_service_type.forEach(item => {
        const printObj = {
          isShowCount:item.raw.cssClass,//此处是通过样式判断该项服务是否会被纳入到合并主项金额中去。
          value: item.value,
          label: item.label,
          amount:detailList.filter(data => item.value === data.questionType).reduce((sum, data) => {
            return sum + (parseFloat(data.amount) || 0);
          }, 0),
          [item.value]: detailList.filter(data => item.value === data.questionType)
        };
        if (printObj[item.value].length > 0) {
          this.T_WORK_ORDER_SERVICE_TYPE.push(printObj);
        }
      });
      this.computeMainTotalAmount()
    },
    //统计主项金额相关信息
    computeMainTotalAmount(){
      let texts=[];
      texts.push(" = "+this.strConvert("折扣金额")+"（"+this.FormData.discountAmount+"GEL）");
      /*  if(this.FormData.taxRate){
         texts.push(" - 附加税（"+this.FormData.taxRate+"%）");
       } */
      this.T_WORK_ORDER_SERVICE_TYPE.forEach(item => {
        if(!isValidString(item.isShowCount)&&item.amount>0){
          texts.push(" - 类目 "+item.value+" ( "+item.amount+"GEL ) ")
        }
      })
      this.totalText=texts.join("");
    },
  }
}
</script>
<style lang="scss" scoped>
#acceptTable .el-table__empty-block {
  display: none;
}

.file-header{
  text-align: center;
  font-size: 15px;
  .file-logo{
    width: 267px;
    height: 44px
  }
}

.grid-bottom{
  border-bottom: 1px solid #1e1e1e;
  line-height: 20px;
  font-size: 12px;
}

.grid-right  {
  padding-left: 10px;
}
.grid-left {
  border-right: 1px solid #1e1e1e;

}

.grid-count{
  text-align: left;
  margin-left: 2px;
  line-height: 20px;
  font-size: 13px;
  font-weight: bold;
}

::v-deep .el-table--group, .el-table--border{
/*  border: 1px solid #3b3b3b;*/
}
::v-deep .el-table--border .el-table__cell{
  border: 1px solid #3b3b3b;
}

::v-deep .el-table--mini .el-table__cell{
  padding: 3px 0;
}

::v-deep .el-descriptions--medium.is-bordered .el-descriptions-item__cell{
  width: 60px;
}
::v-deep .el-table .el-table__header-wrapper th{
  background-color: #FFFFFF;
  font-weight: initial;
  height:25px
}

.app-table-list{
  .tables-border{
   /* border: 2px solid #1e1e1e;*/
    .row-table{
      width: 100%;
    }
  }
}

.totalHeader{
  font-size:12px;
  line-height: 18px;
}

.receiptFont{
  font-family: 'DengXian', sans-serif;
}


</style>
