import request from '@/utils/request'

// 查询多语言列表
export function listLanguage(query) {
  return request({
    url: '/manage/language/list',
    method: 'get',
    params: query
  })
}

/**
 * 根据本地要求返回多语言结果
 * @param query
 * @returns {*}
 */
export function listAllLanguageByLocal() {
  return request({
    url: '/manage/language/listAllLanguageByLocal',
    method: 'get'
  })
}

// 查询多语言详细
export function getLanguage(id) {
  return request({
    url: '/manage/language/' + id,
    method: 'get'
  })
}

// 新增多语言
export function addLanguage(data) {
  return request({
    url: '/manage/language',
    method: 'post',
    data: data
  })
}

// 修改多语言
export function updateLanguage(data) {
  return request({
    url: '/manage/language',
    method: 'put',
    data: data
  })
}

// 删除多语言
export function delLanguage(id) {
  return request({
    url: '/manage/language/' + id,
    method: 'delete'
  })
}
