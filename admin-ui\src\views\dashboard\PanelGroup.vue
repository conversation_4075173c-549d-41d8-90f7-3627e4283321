<template>
  <el-row :gutter="40" class="panel-group">

    <el-col v-bind="adaptiveGridConfig" class="card-panel-col">
      <div class="card-panel" >
        <div class="card-panel-icon-wrapper icon-shopping" @click="handleSetLineChartData('shoppings')">
          <svg-icon icon-class="clipboard" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{strConvert('工单')}}
          </div>
          <count-to :start-val="0" :end-val="TableData?TableData.workOrderCount:0" :duration="3600" class="card-panel-num" />
        </div>
        <div class="option-description">
          <el-button class="card-panel-button" :size="adaptiveButtonSize" type="primary" @click="goTo('shoppings')" round>{{strConvert('快速前往')}}</el-button>
        </div>
      </div>
    </el-col>

    <el-col v-bind="adaptiveGridConfig" class="card-panel-col">
      <div class="card-panel">
        <div class="card-panel-icon-wrapper icon-money"   @click="handleSetLineChartData('purchases')">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{strConvert('金额')}}
          </div>
          <count-to :start-val="0" :end-val="TableData?TableData.amountCount:0" :duration="3200" class="card-panel-num" />
        </div>
        <div class="option-description">
          <el-button class="card-panel-button" :size="adaptiveButtonSize" type="primary" @click="goTo('purchases')" round>{{strConvert('快速前往')}}</el-button>
        </div>
      </div>
    </el-col>

    <el-col v-bind="adaptiveGridConfig" class="card-panel-col">
      <div class="card-panel" >
        <div class="card-panel-icon-wrapper icon-people" @click="handleSetLineChartData('newVisitis')">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            {{strConvert('客户')}}
          </div>
          <count-to :start-val="0" :end-val="TableData?TableData.customerCount:0" :duration="2600" class="card-panel-num" />
        </div>
        <div class="option-description">
            <el-button class="card-panel-button" :size="adaptiveButtonSize" type="primary" @click="goTo('newVisitis')" round>{{strConvert('快速前往')}}</el-button>
        </div>
      </div>
    </el-col>

    <el-col v-bind="adaptiveGridConfig" class="card-panel-col">
      <div class="card-panel" >
        <div class="card-panel-icon-wrapper icon-message" @click="handleSetLineChartData('messages')">
          <svg-icon icon-class="message" class-name="card-panel-icon" />
        </div>

        <div class="card-panel-description">
          <div class="card-panel-text">
            {{strConvert('库存预警')}}
          </div>
          <count-to style="color: #e60000" :start-val="0" :end-val="TableData?TableData.partsCount:0" :duration="500" class="card-panel-num" />
        </div>
        <div class="option-description">
          <el-button class="card-panel-button" :size="adaptiveButtonSize" type="primary" @click="goTo('parts')" round>{{strConvert('快速前往')}}</el-button>
        </div>
      </div>
    </el-col>

    <el-col v-bind="adaptiveGridConfig" class="card-panel-col">
      <div class="card-panel" >
        <div class="card-panel-icon-wrapper icon-message" @click="handleSetLineChartData('messages')">
          <svg-icon icon-class="message" class-name="card-panel-icon" />
        </div>

        <div class="card-panel-description">
          <div class="card-panel-text">
            {{strConvert('网站')}}
          </div>
          <count-to :start-val="0" :end-val="TableData?TableData.messageCount:0" :duration="2600" class="card-panel-num" />
        </div>
        <div class="option-description">
          <el-badge :value="TableData?Object.keys(TableData.noReadMsgCount).length:0" size="min" class="item" type="danger">
            <el-button class="card-panel-button" :size="adaptiveButtonSize" type="primary" @click="goTo('messages')" round>{{strConvert('未读消息')}}</el-button>
          </el-badge>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CountTo from 'vue-count-to'
import { strConvert } from '@/i18n/i18nMix'
import languageStyleMixin from '@/mixins/languageStyleMixin'

export default {
  components: {
    CountTo
  },
  mixins: [languageStyleMixin],
  props:{
    TableData:{
      type: Object,
      default: null
    }
  },
  data() {
    return {
      // 其他组件数据
    }
  },
  methods: {
    strConvert,
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
    goTo(type) {
      this.$emit('goTo', type)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
/*  margin-top: 18px;*/

  .card-panel-col {
    margin-bottom: 12px;
  }

  .card-panel {
    display: flex;
   /* justify-content: space-between;*/
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);


    .card-panel-icon-wrapper {
      /* 原有样式 */
      transition: all 0.38s ease-out; /* 保留过渡效果 */
      &:hover {
        color: #fff; /* 悬停时图标颜色变白 */
      }

      /* 分别设置不同图标的悬停背景色 */
      &.icon-people:hover {
        background: #40c9c6;
      }
      &.icon-message:hover {
        background: #36a3f7;
      }
      &.icon-money:hover {
        background: #f4516c;
      }
      &.icon-shopping:hover {
        background: #34bfa3;
      }
    }

    /*&:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }
    }*/

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .card-panel-icon-wrapper {
      width: 100px;
      margin: 14px;
      padding: 14px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      width: 100px;
      text-align: center;
      font-weight: bold;


      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 14px;
      }
    }


    .option-description {
      width: 100px;
      font-weight: bold;
      margin: 26px;

      .card-panel-button{
        line-height: 25px;
      }

    }

  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
