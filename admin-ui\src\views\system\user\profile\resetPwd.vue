<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="80px">
    <el-form-item :label="strConvert('旧密码')" prop="oldPassword">
      <el-input v-model="user.oldPassword" :placeholder="strConvert('请输入旧密码')" type="password" show-password/>
    </el-form-item>
    <el-form-item :label="strConvert('新密码')" prop="newPassword">
      <el-input v-model="user.newPassword" :placeholder="strConvert('请输入新密码')" type="password" show-password/>
    </el-form-item>
    <el-form-item :label="strConvert('确认密码')" prop="confirmPassword">
      <el-input v-model="user.confirmPassword" :placeholder="strConvert('请确认新密码')" type="password" show-password/>
    </el-form-item>
    <el-form-item>
      <el-button type="primary"  @click="submit">{{ strConvert('保存') }}</el-button>
      <el-button type="danger"  @click="close">{{ strConvert('关闭') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { updateUserPwd } from "@/api/system/user"

export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"))
      } else {
        callback()
      }
    }
    return {
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" },
          { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ]
      }
    }
  },
  methods: {
    strConvert,

    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(response => {
            this.$modal.msgSuccess("修改成功")
          })
        }
      })
    },
    close() {
      this.$tab.closePage()
    }
  }
}
</script>
