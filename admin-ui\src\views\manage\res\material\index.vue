<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('物料编号')" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          :placeholder="strConvert('请输入物料编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('物料名称')" prop="materialName">
        <el-input
          v-model="queryParams.materialName"
          :placeholder="strConvert('请输入物料名称')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item :label="strConvert('库存数量')" prop="num">
        <el-input
          v-model="queryParams.num"
          :placeholder="strConvert('请输入库存数量')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          @click="addRow"
          v-hasPermi="['manage:material:add']"
        >{{ strConvert('新增') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          :disabled="addShow"
          @click="batchSubmitForm"
          v-hasPermi="['manage:material:add']"
        >{{ strConvert('批量保存') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"

          icon="el-icon-delete"

          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:material:remove']"
        >{{ strConvert('批量删除') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"

          @click="handleExport"
          v-hasPermi="['manage:material:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" tabindex="0" align="center" />
      <el-table-column :label="strConvert('物料编号')" align="center" prop="materialId" />
      <el-table-column :label="strConvert('物料名称')" align="center" prop="materialName">
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing" v-model="scope.row.materialName"  tabindex="0"/>
          <span v-else>{{ scope.row.materialName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('库存数量')" align="center" prop="num" >
        <template slot-scope="scope">
          <el-input   v-if="scope.row.isEditing" v-model="scope.row.num" type="number" min="1"  tabindex="0" />
          <span v-else>{{ scope.row.num }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('描述')" align="center" prop="comment" >
        <template slot-scope="scope">
          <el-input type="textarea"  v-if="scope.row.isEditing" v-model="scope.row.comment"  tabindex="0"/>
          <span v-else>{{ scope.row.comment }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('备注')" align="center" prop="remark" >
        <template slot-scope="scope">
          <el-input  type="number" v-if="scope.row.isEditing" v-model="scope.row.remark" />
          <span v-else>{{ scope.row.remark }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <template v-if="scope.row.isEditing">
            <el-button size="small" type="primary"  tabindex="-1"  @click="saveRow(scope.$index, scope.row)">{{ strConvert('保存') }}</el-button>
            <el-button size="small" @click="cancelEdit(scope.$index, scope.row)" tabindex="-1">{{ strConvert('取消') }}</el-button>
          </template>
          <template v-else>
            <el-button size="small"  v-hasPermi="['manage:material:edit']" @click="editRowFn(scope.$index, scope.row)">{{ strConvert('编辑') }}</el-button>
            <el-button size="small" v-hasPermi="['manage:material:edit']" type="danger" @click="handleDelete(scope.row)">{{ strConvert('删除') }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { listMaterial, delMaterial, batchAddMaterial, updateMaterial } from "@/api/manage/material"

export default {
  name: "Material",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料表格数据
      materialList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialId: null,
        materialName: null,
        num: null,
      },
      editIndex: null, // 当前编辑行索引
      editRow: {},     // 编辑中的行数据
      addShow: true, // 是否为新增
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询物料列表 */
    getList() {
      this.loading = true
      listMaterial(this.queryParams).then(response => {
        this.materialList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.materialId)
      this.single = selection.length!==1
      if(this.addShow){
        this.multiple = !selection.length
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const materialIds = row.materialId || this.ids
      this.$modal.confirm('是否确认删除物料编号为"' + materialIds + '"的数据项？').then(function() {
        return delMaterial(materialIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/material/export', {
        ...this.queryParams
      }, `material_${new Date().getTime()}.xlsx`)
    },
    /** 新增按钮操作 */
    addRow() {
      this.addShow=false;
      this.materialList.unshift({
        materialName: '',
        comment: '',
        num: 1,
       /*  remark: '', */
        isEditing: true, // 新增行直接进入编辑状态
        isNew: true      // 标记为新增
      });
    },
    /** 批量提交按钮 */
    batchSubmitForm() {
      var that=this;
      if(this.materialList.length>0){
        var submitData=this.materialList.filter(function(item) {
          return item.isNew!==undefined&&item.isNew&&that.validateData(item)
        })
        if(submitData.length>0){
          batchAddMaterial(submitData).then(response => {
            this.$modal.msgSuccess("新增成功")
            this.getList()
            this.editIndex = null;
            this.editRow = {};
          })
        }else {
          this.$message.warning('没有要提交的数据');
        }
      }
    },
    /** 单条数据提交按钮 */
    saveRow(index, row) {
      if(this.validateData(row)) {
        if (row.isNew) {
          // 新增
          batchAddMaterial([row]).then(() => {
            this.$message.success('新增成功');
            this.getList();
          });
        } else {
          // 编辑
          updateMaterial(row).then(() => {
            this.$message.success('修改成功');
            this.getList();
          });
        }
        this.addShow = true;
        this.reBackEdit(index);
      }
    },
    // 校验
    validateData(material){
      if (!material.materialName) {
        this.$message.error('物料名称不能为空');
        return false;
      }else if (!material.num) {
        this.$message.error('数量不能为空');
        return false;
      }
      return true;
    },
    // 退出编辑状态
    reBackEdit(index){
      this.$set(this.materialList[index], 'isEditing', false);
      delete this.materialList[index].isNew;
      delete this.materialList[index]._backup;
    },
    cancelEdit(index, row) {
      if (row.isNew) {
        // 新增未保存，直接移除
        this.materialList.splice(index, 1);
      } else {
        // 编辑还原
        Object.assign(row, row._backup);
        this.$set(this.materialList[index], 'isEditing', false);
        delete this.materialList[index]._backup;
      }
    },
    editRowFn(index, row) {
      // 进入编辑状态，备份原始数据以便取消时还原
      this.$set(this.materialList[index], 'isEditing', true);
      this.$set(this.materialList[index], '_backup', { ...row });
    }
  }
}
</script>
