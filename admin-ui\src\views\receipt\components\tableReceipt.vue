<template>
  <div class="table-data">
    <table style="width: 735px;">
      <thead class="tableTitle">
      <tr><th style="font-weight:initial;">{{ title}}</th></tr>
      </thead>
      <tbody>
      <tr>
        <el-table
          size="mini"
          :data="OrderQuestionDetailList"
          border
          :summary-method="getSummaries"
          show-summary
          class="table-info">
          <el-table-column label="NO" align="center" type="index" width="60" />
          <el-table-column width="200" label="服务" prop="oldQuestionName" />
          <el-table-column width="200" label="სამღებრო სამუშაოები" prop="questionName" />
          <el-table-column width="150" label="სამ.ღირებულება费用" prop="amount" align="right"/>
          <el-table-column width="90" label="შენიშვნა备注" prop="questionComment" />
        </el-table>
      </tr>
      </tbody>
    </table>

  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

export default {
  name: 'tableReceipt',
  props: {
    OrderQuestionDetailList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ""
    }
  },
  methods: {
    strConvert,
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];

      // 计算费用总和
      const totalAmount = data.reduce((sum, item) => {
        return sum + (parseFloat(item.amount) || 0);
      }, 0);

      columns.forEach((column, index) => {
        if (index === 0) {
          // 第一列：显示"费用合计"并合并两列
          sums[index] = '费用合计/ფასი:';
          // 在Element UI 2.x中需要通过特殊方式合并
          this.$nextTick(() => {
            const footer = this.$el.querySelector('.el-table__footer');
            if (footer) {
              const cells = footer.querySelectorAll('.el-table__cell');
              if (cells.length >= 3) {
                cells[0].colSpan = 3;
                cells[1].style.display = 'none';
                cells[2].style.display = 'none';
              }
            }
          });
        }
        else if (index === 1||index === 2) {
          // 第二列：跳过处理
          sums[index] = '';
        }
        else if (column.property === 'amount') {
          // 费用列显示合计值
          sums[index] = totalAmount.toFixed(2) + ' GEL';
        }
        else {
          // 其他列留空
          sums[index] = '';
        }
      });

      return sums;
    }
  }
}
</script>

<style scoped>
/* 确保统计行样式正确 */
/*::v-deep .el-table__footer-wrapper tbody td.el-table__cell {
  background-color: #f5f7fa;
}*/
::v-deep .el-table--scrollable-x .el-table__body-wrapper{
  overflow-x:hidden;
}

::v-deep .el-table--border .el-table__cell{
  font-size: 9px;
  border: 1px solid #3b3b3b;
  line-height:18px
}

.el-table__footer .el-table__cell:first-child {
  text-align: center;
  font-weight: bold;
}

::v-deep .el-table .cell{
  line-height:18px
}

.table-data{
  /*border: 1px solid #1e1e1e;*/
  .el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell{
    height: 22px;
    font-weight:normal;
    background-color: #FFFFFF;
  }
  .el-table--mini .el-table__cell{
    padding: 3px 0;

  }

}

.table-info{
  margin: 5px;
  width: 702px;
}

.tableTitle{
  font-size:12px;
  text-align: left;
  line-height: 18px;
  font-weight:initial;
}
</style>


