<template>
  <!-- 结算单据模板 -->
  <div>
    <el-dialog  :visible.sync="dialogVisible"  @opened="onDialogOpened" :close-on-click-modal="false" title=""  width="800px" >
      <el-divider content-position="center">打印预览</el-divider>
      <print-component
        ref="printComponent"
        v-loading="loading"
        :show-buttons="false"
        :print-options="{
      popTitle: '销售报表',
      extraCss: '@/assets/styles/print.scss'
      }"
        :pdf-options="{
        filename: 'sales-report.pdf',
        margin: 5
      }"
      >
        <el-row>
          <div class="file-header">
            <img v-if="fileLog" :src="fileLog" class="file-logo" />
            <div>საწყობის ქვითარი<br>入库单</div>
          </div>
        </el-row>

        <el-descriptions style="margin-top:10px;" :title="'采购信息შესყიდვის შესახებ'" :column="3"  border>
          <el-descriptions-item  >
            <template slot="label">
              采购单号შესყიდვის:
            </template>
            {{ saveForm.purchaseId }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              物件类型ობიექტის ტიპი:
            </template>
            {{
              strConvert(saveForm.resTypeName)
            }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              操作人ოპერატორი:
            </template>
            {{ saveForm.createBy }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              描述შენიშვნა:
            </template>
            {{ saveForm.comment }}
          </el-descriptions-item>
        </el-descriptions>
        <el-row>
          <div style="text-align: left;font-weight: bold;font-size: 18px"><p>物件详情ობიექტის დეტალები</p></div>
        </el-row>

        <el-table id="acceptTable" :data="saveOrderDetailList"  border
                 style="width: 900px"
                 class="print-table"
                  :row-class-name="rowTWorkOrderDetailIndex"
                  ref="tWorkOrderDetail2">
<!--          <el-table-column :label="strConvert('序号')" align="center" prop="index" width="50"/>-->
          <el-table-column :label="'编号№'" prop="partsId"  width="90"/>
          <el-table-column :label="'配件名称სახელი'" prop="partsName" width="120"/>
          <el-table-column :label="'规格სპეციფიკაცია'" prop="norm" width="130"/>
          <el-table-column :label="'单价ფასი'" prop="price" width="80"/>
          <el-table-column :label="'数量რაოდენობა'" prop="num" width="120"/>
          <el-table-column :label="'金额თანხა'" prop="amount" width="90"/>
          <el-table-column :label="'描述აღწერე'" prop="comment"  width="129"/>
        </el-table>

        <div class="app-table-list" >
          <div class="tables-border">
            <el-row type="flex" class="row-bg">
              <el-col :span="8">
                <div class="  grid-bottom">
                  სულ总额: <span>  {{totalCountAmount }} GEL</span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-right  grid-bottom">
                  库管სახაზინო აუდიტი: <span> </span>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-right  grid-bottom">
                  总经理დირექტორი: <span>  </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-row>
          <el-row>
            <div style="margin-top: 25px;margin-right:45px;text-align: right;font-size: 15px">
              <p>  European Mazda Automobile Trading Co., Ltd.</p>
              <p>{{ formatWesternDate(new Date()) }}</p>
            </div>
          </el-row>
        </el-row>
      </print-component>
      <div slot="footer" class="dialog-footer">
        <el-row :gutter="12" class="mb8">
          <el-col :span="14" >
            <el-radio-group v-model="$i18n.locale" size="small">
              <el-radio-button label="zh">
                <span class="flag-icon flag-icon-cn"  style="margin-right: 6px;"></span>
                中文
              </el-radio-button>
              <el-radio-button label="en">
                <span class="flag-icon flag-icon-us" style="margin-right: 6px;"></span>
                English
              </el-radio-button>
              <el-radio-button label="ru">
                <span class="flag-icon flag-icon-ru" style="margin-right: 6px;"></span>
                Русский
              </el-radio-button>
              <el-radio-button label="ka">
                <span class="flag-icon flag-icon-ge" style="margin-right: 6px;"></span>
                ქართული
              </el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="10" >
            <el-button  type="primary" icon="el-icon-printer" @click="handlePrint">打  印</el-button>
            <el-button  type="warning" icon="el-icon-download" @click="handleExportPDF">下  载</el-button>
            <el-button @click="handleClose">取 消</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>


<script>
import {
  applyTranslation,
  formatDate,
  formatWesternDate,
  getDictDataLabelByValue,
  isValidString,
  trimPunctuation
} from '@/utils'
import i18n from '@/i18n/i18n'
import { tMix, ensureLocaleLoaded,strConvert } from '@/i18n/i18nMix'
import { translate } from '@/api/tool/translate'
import file_log from '@/assets/logo/fileLog.png'
import WorkerTableReceipt from '@/views/receipt/components/workerTableReceipt.vue'
import store from '@/store'

export default {
  name: 'inputStoreReceipt',
  dicts: ['t_res_type'],
  components:{
    WorkerTableReceipt,
    i18n
  },
  props: {
    OpenPrint: {
      type: Boolean,
      default: false
    },
    OrderDetailList: {
      type: Array,
      default: []
    },
    // 表单参数
    FormData: {
      type: Object,
      default: () => ({
        serviceId: null,
        title: null,
        businessType: "0",
        resTypeName:"",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        phonenumber:null,
        advancePayment:null,
        balancePayment:null,
        comment:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      })
    },
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      loading:true,
      resTypeDictList:[],
      saveForm:{
      },
      saveOrderDetailList:[],
      fileLog:file_log,
      totalCountAmount: 0.00,//工单总计
    }
  },
  created() {

  },
  watch: {
    '$i18n.locale': {
      handler: async function (val) {
        /* 加载语言文件 */
        await ensureLocaleLoaded(val);
        const transData = { text: "", sourceLang: "zh", targetLang: val };
        if (transData.sourceLang !== transData.targetLang) {
          this.loading=true;

          const fransStr = [];
          if (isValidString(this.saveForm.title)) {
            fransStr.push(this.saveForm.title);
          }

          if (isValidString(this.saveForm.comment)) {
            fransStr.push(this.saveForm.comment);
          }

          this.saveOrderDetailList.forEach(item => {
            fransStr.push(item.partsName);
            fransStr.push(item.optionComment);
          });

          const finalFransStr = [...new Set(fransStr)];
          if(finalFransStr.length===0){
            /*  this.loading=false; */
            this.isShowOption=true;
             return
          }
          store.dispatch('customerTranslation/translateBatchGoogle', {
            texts: finalFransStr,
            targetLang: val,
            sourceLang:this.currentLanguage,
          }).then(res => {
            if (isValidString(res.data.translated)) {
              const newTranslate = res.data;
              if (newTranslate.length === fransStr.length) {
                this.transferMapping={};
                newTranslate.forEach(item => {
                  this.transferMapping[item.sourceText] = item.text;
                });
                applyTranslation(this.saveForm.title,this.saveForm, 'title', this.transferMapping);
                applyTranslation(this.saveForm.comment,this.saveForm, 'comment', this.transferMapping);
                this.saveOrderDetailList.forEach(item => {
                  applyTranslation(item.partsName,item, 'partsName', this.transferMapping);
                  applyTranslation(item.optionComment,item, 'optionComment', this.transferMapping);
                });
              }
            }
            this.loading=false;
          });
        }else{
          this.saveForm=this.FormData;
          this.saveOrderDetailList=this.OrderDetailList;
          this.loading=false;
        }
      },
      immediate: true
    },
    'dict.type.t_res_type': {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.resTypeDictList=newVal;
        }
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        this.totalCountAmount=0.00;
        this.saveForm=JSON.parse(JSON.stringify(this.FormData))
        this.saveForm.resTypeName=getDictDataLabelByValue(this.resTypeDictList,this.saveForm.resType);
        this.saveOrderDetailList= JSON.parse(JSON.stringify(this.OrderDetailList))
        this.saveOrderDetailList.map(item=>{
          this.totalCountAmount=this.totalCountAmount+parseFloat(item.amount);
        })
        return this.OpenPrint;
      },
      set(value) {  console.log(4)
        this.$emit('update:OpenPrint', value);
      }
    }
  },
  methods: {
    formatWesternDate,
    formatDate,
    strConvert,
    trimPunctuation,
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/workorder/export', {
        ...this.queryParams
      }, `workorder_${new Date().getTime()}.xlsx`)
    },
    async handlePrint() {
      this.OpenPrint = true; // 确保对话框打开
      await this.$nextTick(); // 等待DOM更新
      await this.$refs.printComponent.print(); // 调用打印
    },
    handleExportPDF() {
      this.$refs.printComponent.exportPDF() // 调用生成PDF方法
    },
    onDialogOpened() {
      // 对话框完全打开后的回调
    },
    /** 工单详情序号 */
    rowTWorkOrderDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    handleClose() {
      this.dialogVisible = false; // 通过 computed 修改，自动触发 emit
    },
   /*  addPrintClass({ row, column, rowIndex, columnIndex }) {
      console.log(row)
      return 'print-cell'; // 所有单元格添加此类
    } */
  }
}
</script>


<style lang="scss" scoped>

::v-deep .print-table {
    border: 1px solid #000 !important;
    .el-table__body{
      border: 1px solid #000 !important;

      .el-table__cell {
        border: 1px solid #a6a4a4 !important;
      }
    }

    .el-table__header{
       border-collapse: collapse !important;
        border: 2px solid #000 !important;
      .el-table__cell {
        border: 2px solid #000 !important;
      }
    }

  /**/
  /*padding: 5px !important;*/
}

.file-header{
  text-align: center;
  font-size: 15px;
  .file-logo{
    width: 267px;
    height: 44px
  }
}



/*::v-deep  .el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th{
  background-color: #b4b4f1;
}*/





/*
::v-deep .el-table, .el-table__header, .el-table__body {
  border: 2px solid #000 !important;
}
*/


</style>
