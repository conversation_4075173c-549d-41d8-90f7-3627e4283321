import XLSX from "xlsx-js-style";
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
/**
 * 导出功能混入
 * 提供通用的导出功能，包括PDF、Excel和打印
 */

export const exportMixin = {
  data() {
    return {
      exportLoading: {
        pdf: false,
        excel: false,
        print: false
      }
    }
  },

  methods: {
    /**
     * 获取优化的html2canvas配置参数
     * 根据设备像素比动态调整scale值，提升不同设备的渲染质量
     * @param {Object} baseOptions 基础配置选项
     * @returns {Object} 优化后的html2canvas配置
     */
    getOptimalHtml2CanvasOptions(baseOptions = {}) {
      const devicePixelRatio = window.devicePixelRatio || 1;
      // 限制最大scale为3，避免性能问题
      const optimalScale = Math.min(devicePixelRatio, 3);

      return {
        scale: optimalScale,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        // 高DPI设备额外优化
        ...(devicePixelRatio > 1 && {
          removeContainer: true,
          logging: false
        }),
        ...baseOptions
      };
    },

    /**
     * 导出为PDF
     * @param {object} options 导出选项
     */
    async exportToPDF(options = {}) {
      const {
        element = null,
        fileName = null,
        orientation = 'landscape',
        format = 'a4',
        quality = 1.0,
        margin = { top: 10, right: 10, bottom: 10, left: 10 }
      } = options

      this.exportLoading.pdf = true

      try {
        // 确定要导出的元素
        const targetElement = element || this.getExportElement()
        if (!targetElement) {
          throw new Error('未找到要导出的元素')
        }

        // 生成canvas
        const canvas = await html2canvas(targetElement, this.getOptimalHtml2CanvasOptions({
          scale: quality,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff'
        }))

        // 创建PDF
        const pdf = new jsPDF(orientation, 'mm', format)

        // 计算尺寸
        const pageWidth = pdf.internal.pageSize.getWidth()
        const pageHeight = pdf.internal.pageSize.getHeight()
        const imgWidth = pageWidth - margin.left - margin.right
        const imgHeight = (canvas.height * imgWidth) / canvas.width

        let heightLeft = imgHeight
        let position = margin.top

        // 添加图片到PDF
        const imgData = canvas.toDataURL('image/png')
        pdf.addImage(imgData, 'PNG', margin.left, position, imgWidth, imgHeight)
        heightLeft -= (pageHeight - margin.top - margin.bottom)

        // 处理多页
        while (heightLeft >= 0) {
          position = heightLeft - imgHeight + margin.top
          pdf.addPage()
          pdf.addImage(imgData, 'PNG', margin.left, position, imgWidth, imgHeight)
          heightLeft -= (pageHeight - margin.top - margin.bottom)
        }

        // 保存文件
        const finalFileName = fileName || this.generateFileName('pdf')
        pdf.save(finalFileName)

        this.$message?.success('PDF导出成功')
        this.$emit?.('exported', { type: 'pdf', fileName: finalFileName })

        return { success: true, fileName: finalFileName }
      } catch (error) {
        console.error('PDF导出失败:', error)
        this.$message?.error('PDF导出失败: ' + error.message)
        this.$emit?.('export-error', { type: 'pdf', error })
        return { success: false, error }
      } finally {
        this.exportLoading.pdf = false
      }
    },

    /**
     * 导出为Excel
     * @param {object} options 导出选项
     */
    async exportToExcel(options = {}) {
      const {
        data = null,
        fileName = null,
        sheetName = 'Sheet1',
        headers = null,
        cellData = null, // 新增：支持原始单元格数据（包含样式）
        converter = null // 新增：支持传递转换器实例
      } = options

      this.exportLoading.excel = true

      try {

        let worksheet

        if (cellData && Array.isArray(cellData)) {
          // 使用包含样式信息的单元格数据
          worksheet = this.createWorksheetFromCellData(XLSX, cellData, converter)
        } else if (data) {
          // 使用提供的数据
          if (Array.isArray(data) && Array.isArray(data[0])) {
            // 二维数组格式
            worksheet = XLSX.utils.aoa_to_sheet(data)
          } else if (Array.isArray(data)) {
            // 对象数组格式
            worksheet = XLSX.utils.json_to_sheet(data)
          } else {
            throw new Error('不支持的数据格式')
          }
        } else {
          // 从DOM元素获取数据
          const tableData = this.getTableDataForExport()
          worksheet = XLSX.utils.aoa_to_sheet(tableData)
        }

        // 创建工作簿
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

        // 保存文件
        const finalFileName = fileName || this.generateFileName('xlsx')
        XLSX.writeFile(workbook, finalFileName)

        this.$message?.success('Excel导出成功')
        this.$emit?.('exported', { type: 'excel', fileName: finalFileName })

        return { success: true, fileName: finalFileName }
      } catch (error) {
        console.error('Excel导出失败:', error)
        this.$message?.error('Excel导出失败: ' + error.message)
        this.$emit?.('export-error', { type: 'excel', error })
        return { success: false, error }
      } finally {
        this.exportLoading.excel = false
      }
    },

    /**
     * 从单元格数据创建工作表（包含样式）
     * @param {object} XLSX XLSX库实例
     * @param {array} cellData 单元格数据数组
     * @returns {object} 工作表对象
     */
    createWorksheetFromCellData(XLSX, cellData, converter = null) {
      // 首先创建基础的工作表（只包含数据）
      const dataArray = this.convertCellDataToArray(cellData)
      const worksheet = XLSX.utils.aoa_to_sheet(dataArray)

      // 添加样式信息
      this.applyCellStyles(worksheet, cellData, XLSX, converter)

      return worksheet
    },

    /**
     * 将单元格数据转换为二维数组（仅提取值）
     * @param {array} cellData 单元格数据
     * @returns {array} 二维数组
     */
    convertCellDataToArray(cellData) {
      if (!cellData || cellData.length === 0) return [[]]

      const maxRow = Math.max(...cellData.map(cell => cell.r))
      const maxCol = Math.max(...cellData.map(cell => cell.c))

      const array = Array(maxRow + 1).fill().map(() => Array(maxCol + 1).fill(''))

      cellData.forEach(cell => {
        if (cell.r >= 0 && cell.r <= maxRow && cell.c >= 0 && cell.c <= maxCol) {
          array[cell.r][cell.c] = cell.v?.v ?? cell.v ?? ''
        }
      })

      return array
    },

    /**
     * 应用单元格样式
     * @param {object} worksheet 工作表对象
     * @param {array} cellData 单元格数据
     * @param {object} XLSX XLSX库实例
     */
    applyCellStyles(worksheet, cellData, XLSX, converter = null) {
      // XLSX库对样式的支持有限，这里实现基础样式映射
      cellData.forEach(cell => {
        const cellAddress = XLSX.utils.encode_cell({ r: cell.r, c: cell.c })

        if (worksheet[cellAddress] && cell.v) {
          // 根据单元格样式信息设置Excel单元格属性
          const excelCell = worksheet[cellAddress]
          const styleProps = {}

          // 设置水平对齐
          if (cell.v.ht !== undefined) {
            const horizontalAlign = cell.v.ht === 1 ? 'center' :
                                  cell.v.ht === 2 ? 'right' : 'left'
            styleProps.alignment = { ...(styleProps.alignment || {}), horizontal: horizontalAlign }
          }

          // 设置垂直对齐
          if (cell.v.vt !== undefined) {
            const verticalAlign = cell.v.vt === 1 ? 'center' :
                                 cell.v.vt === 2 ? 'bottom' : 'top'
            styleProps.alignment = { ...(styleProps.alignment || {}), vertical: verticalAlign }
          }

          // 设置字体加粗
          if (cell.v.bl === 1) {
            styleProps.font = { ...(styleProps.font || {}), bold: true }
          }

          // 设置字体大小
          if (cell.v.fs) {
            styleProps.font = { ...(styleProps.font || {}), sz: cell.v.fs }
          }

          // 设置字体颜色
          if (cell.v.fc && cell.v.fc !== 'rgb(0, 0, 0)') {
            styleProps.font = { ...(styleProps.font || {}), color: { rgb: this.convertColorToExcel(cell.v.fc) } }
          }

          // 设置背景颜色
          if (cell.v.bg) {
            styleProps.fill = { fgColor: { rgb: this.convertColorToExcel(cell.v.bg) } }
          }

          // 应用样式
          if (Object.keys(styleProps).length > 0) {
            excelCell.s = { ...(excelCell.s || {}), ...styleProps }
          }
        }
      })

      // 处理合并单元格
      this.applyMergedCells(worksheet, cellData, converter, XLSX)

      // 处理列宽和行高
      this.applyColumnWidthsAndRowHeights(worksheet, converter)

      // 硬编码第1、2、3行的居中样式（用户要求）
      // 必须在合并单元格处理之后执行，以确保样式不被覆盖
      console.log('🔍 开始应用硬编码居中样式')
      this.hardcodeRowAlignment(worksheet, XLSX)
      console.log('✅ 硬编码居中样式应用完成')
    },

    /**
     * 硬编码第1、2、3行的居中样式
     * 用户要求：第1、2、3行都是跨整行，需要强制居中
     * @param {object} worksheet 工作表对象
     * @param {object} XLSX XLSX库实例
     */
    hardcodeRowAlignment(worksheet, XLSX) {
      try {
        console.log('🔍 开始硬编码第0、1、2行居中样式')
        console.log('🔍 开始硬编码第0、1、2行居中样式',worksheet)

        // 第1行（格鲁吉亚语标题）- 跨整行居中
        for (let c = 0; c < 8; c++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c })
          if (worksheet[cellAddress]) {
            // 保留原有样式，只修改对齐方式
            worksheet[cellAddress].s = {
              ...worksheet[cellAddress].s, // 保留原有样式
              alignment: {
                ...(worksheet[cellAddress].s?.alignment || {}), // 保留原有对齐设置
                horizontal: 'center',
                vertical: 'center'
              }
            }
            console.log(`✅ 设置单元格 ${cellAddress} 居中样式`)
          }
        }

        // 第1行（格鲁吉亚语标题）- 跨整行居中
        for (let c = 0; c < 8; c++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 1, c })
          if (worksheet[cellAddress]) {
            // 保留原有样式，只修改对齐方式
            worksheet[cellAddress].s = {
              ...worksheet[cellAddress].s, // 保留原有样式
              alignment: {
                ...(worksheet[cellAddress].s?.alignment || {}), // 保留原有对齐设置
                horizontal: 'center',
                vertical: 'center'
              }
            }
            console.log(`✅ 设置单元格 ${cellAddress} 居中样式`)
          }
        }

        // 第2行（中文标题）- 跨整行居中
        for (let c = 0; c < 8; c++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 2, c })
          if (worksheet[cellAddress]) {
            // 保留原有样式，只修改对齐方式
            worksheet[cellAddress].s = {
              ...worksheet[cellAddress].s, // 保留原有样式
              alignment: {
                ...(worksheet[cellAddress].s?.alignment || {}), // 保留原有对齐设置
                horizontal: 'center',
                vertical: 'center'
              }
            }
            console.log(`✅ 设置单元格 ${cellAddress} 居中样式`)
          }
        }

        console.log('✅ 硬编码第0、1、2行居中样式完成（保留原有样式）')
      } catch (error) {
        console.warn('硬编码居中样式失败:', error)
      }
    },

    /**
     * 应用列宽和行高配置
     * @param {object} worksheet 工作表对象
     * @param {object} converter 转换器实例
     */
    applyColumnWidthsAndRowHeights(worksheet, converter = null) {
      try {
        // 确定要使用的转换器实例
        const targetConverter = converter || this.converter

        if (targetConverter && typeof targetConverter.getSheetConfig === 'function') {
          const sheetConfig = targetConverter.getSheetConfig()

          // 应用列宽配置
          if (sheetConfig.columnlen) {
            worksheet['!cols'] = Object.entries(sheetConfig.columnlen).map(([col, width]) => ({
              width: width / 7, // 转换像素为Excel单位（大约1像素=1/7个字符宽度）
              hidden: false
            }))
          }

          // 应用行高配置
          if (sheetConfig.rowlen) {
            worksheet['!rows'] = Object.entries(sheetConfig.rowlen).map(([row, height]) => ({
              hpt: height * 0.75, // 转换像素为点（大约1像素=0.75点）
              hidden: false
            }))
          }
        }
      } catch (error) {
        console.warn('应用列宽和行高配置失败:', error)
      }
    },

    /**
     * 应用合并单元格
     * @param {object} worksheet 工作表对象
     * @param {array} cellData 单元格数据
     */
    applyMergedCells(worksheet, cellData, converter = null, XLSX = null) {
      // 尝试从转换器获取合并配置
      // 不同的单据转换器有不同的合并配置
      try {
        let mergeConfig = {}

        // 确定要使用的转换器实例
        const targetConverter = converter || this.converter

        // 尝试从不同的来源获取合并配置
        if (targetConverter && typeof targetConverter.getMergeConfig === 'function') {
          // 直接从转换器获取合并配置
          mergeConfig = targetConverter.getMergeConfig()
        } else if (targetConverter && typeof targetConverter.getSheetConfig === 'function') {
          // 从表格配置中获取合并配置
          const sheetConfig = targetConverter.getSheetConfig()
          mergeConfig = sheetConfig.merge || {}
        } else {
          console.warn('无法获取合并配置：转换器缺少相关方法')
          return
        }

        const merges = []

        // 转换合并配置为XLSX格式
        Object.values(mergeConfig).forEach(merge => {
          if (merge.rs > 1 || merge.cs > 1) {
            merges.push({
              s: { r: merge.r, c: merge.c },
              e: { r: merge.r + merge.rs - 1, c: merge.c + merge.cs - 1 }
            })

            // 为合并区域的所有单元格应用相同的样式（解决Excel导出对齐问题）
            this.applyMergedCellStyles(worksheet, cellData, merge, XLSX)
          }
        })

        if (merges.length > 0) {
          worksheet['!merges'] = merges
        }
      } catch (error) {
        console.warn('获取合并配置失败:', error)
      }
    },

    /**
     * 为合并单元格区域应用统一的样式
     * @param {object} worksheet 工作表对象
     * @param {array} cellData 单元格数据
     * @param {object} merge 合并配置
     * @param {object} XLSX XLSX库实例
     */
    applyMergedCellStyles(worksheet, cellData, merge, XLSX) {
      try {
        // 查找合并区域第一个单元格的样式
        const firstCell = cellData.find(cell =>
          cell.r === merge.r && cell.c === merge.c
        )

        if (!firstCell || !firstCell.v) return

        // 为合并区域的所有单元格应用相同的样式
        for (let r = merge.r; r <= merge.r + merge.rs - 1; r++) {
          for (let c = merge.c; c <= merge.c + merge.cs - 1; c++) {
            const cellAddress = XLSX.utils.encode_cell({ r, c })

            if (worksheet[cellAddress]) {
              // 应用水平对齐
              if (firstCell.v.ht !== undefined) {
                const horizontalAlign = firstCell.v.ht === 1 ? 'center' :
                                      firstCell.v.ht === 2 ? 'right' : 'left'
                worksheet[cellAddress].s = {
                  ...(worksheet[cellAddress].s || {}),
                  alignment: {
                    ...(worksheet[cellAddress].s?.alignment || {}),
                    horizontal: horizontalAlign
                  }
                }
              }

              // 应用垂直对齐
              if (firstCell.v.vt !== undefined) {
                const verticalAlign = firstCell.v.vt === 1 ? 'center' :
                                     firstCell.v.vt === 2 ? 'bottom' : 'top'
                worksheet[cellAddress].s = {
                  ...(worksheet[cellAddress].s || {}),
                  alignment: {
                    ...(worksheet[cellAddress].s?.alignment || {}),
                    vertical: verticalAlign
                  }
                }
              }
            }
          }
        }
      } catch (error) {
        console.warn('应用合并单元格样式失败:', error)
      }
    },

    /**
     * 转换颜色格式为Excel格式
     * @param {string} color 颜色值
     * @returns {string} Excel颜色格式
     */
    convertColorToExcel(color) {
      if (!color) return 'FFFFFFFF'

      // 处理RGB格式
      if (color.startsWith('rgb(')) {
        const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/)
        if (rgbMatch) {
          const r = parseInt(rgbMatch[1]).toString(16).padStart(2, '0')
          const g = parseInt(rgbMatch[2]).toString(16).padStart(2, '0')
          const b = parseInt(rgbMatch[3]).toString(16).padStart(2, '0')
          return `FF${r}${g}${b}`.toUpperCase()
        }
      }

      // 处理十六进制格式
      if (color.startsWith('#')) {
        let hex = color.slice(1)
        if (hex.length === 3) {
          hex = hex.split('').map(c => c + c).join('')
        }
        if (hex.length === 6) {
          return `FF${hex}`.toUpperCase()
        }
      }

      // 简单的颜色映射
      const colorMap = {
        '#f0f0f0': 'FFD3D3D3',
        '#e6f7ff': 'FFE6F7FF',
        '#f6ffed': 'FFF6FFED',
        '#fff2e8': 'FFFFF2E8',
        '#f9f0ff': 'FFF9F0FF',
        '#4472C4': 'FF4472C4', // 表格标题蓝色
        '#FFFFFF': 'FFFFFFFF', // 白色
        '#000000': 'FF000000'  // 黑色
      }

      return colorMap[color] || 'FFFFFFFF'
    },

    /**
     * 打印
     * @param {object} options 打印选项
     */
    printDocument(options = {}) {
      const {
        element = null,
        title = '打印文档',
        styles = '',
        beforePrint = null,
        afterPrint = null
      } = options

      this.exportLoading.print = true

      try {
        // 确定要打印的元素
        const targetElement = element || this.getExportElement()
        if (!targetElement) {
          throw new Error('未找到要打印的元素')
        }

        // 执行打印前回调
        if (beforePrint && typeof beforePrint === 'function') {
          beforePrint()
        }

        // 创建打印窗口
        const printWindow = window.open('', '_blank')
        const content = targetElement.innerHTML

        // 构建打印页面
        const printContent = `
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <title>${title}</title>
              <style>
                body {
                  margin: 0;
                  padding: 20px;
                  font-family: 'Microsoft YaHei', Arial, sans-serif;
                }
                table {
                  border-collapse: collapse;
                  width: 100%;
                  margin-bottom: 20px;
                }
                td, th {
                  border: 1px solid #ddd;
                  padding: 8px;
                  text-align: left;
                  font-size: 12px;
                }
                th {
                  background-color: #f5f5f5;
                  font-weight: bold;
                }
                .no-print {
                  display: none !important;
                }
                @media print {
                  body { margin: 0; }
                  .no-print { display: none !important; }
                }
                ${styles}
              </style>
            </head>
            <body>
              ${content}
            </body>
          </html>
        `

        printWindow.document.write(printContent)
        printWindow.document.close()

        // 等待内容加载完成后打印
        printWindow.onload = () => {
          printWindow.print()

          // 执行打印后回调
          if (afterPrint && typeof afterPrint === 'function') {
            afterPrint()
          }
        }

        this.$emit?.('printed', { title })
        return { success: true }
      } catch (error) {
        console.error('打印失败:', error)
        this.$message?.error('打印失败: ' + error.message)
        this.$emit?.('print-error', { error })
        return { success: false, error }
      } finally {
        this.exportLoading.print = false
      }
    },

    /**
     * 批量导出
     * @param {array} types 导出类型数组 ['pdf', 'excel']
     * @param {object} options 选项
     */
    async batchExport(types = ['pdf', 'excel'], options = {}) {
      const results = []

      for (const type of types) {
        try {
          let result
          switch (type) {
            case 'pdf':
              result = await this.exportToPDF(options.pdf || {})
              break
            case 'excel':
              result = await this.exportToExcel(options.excel || {})
              break
            case 'print':
              result = this.printDocument(options.print || {})
              break
            default:
              throw new Error(`不支持的导出类型: ${type}`)
          }
          results.push({ type, ...result })
        } catch (error) {
          results.push({ type, success: false, error })
        }
      }

      return results
    },

    /**
     * 获取要导出的元素
     * 子组件可以重写此方法来指定特定的导出元素
     */
    getExportElement() {
      // 尝试查找常见的导出目标
      const selectors = [
        '.spreadsheet-container',
        '.export-target',
        '.univer-sheet-content',
        '.main-content'
      ]

      for (const selector of selectors) {
        const element = this.$el?.querySelector(selector)
        if (element) {
          return element
        }
      }

      // 默认返回组件根元素
      return this.$el
    },

    /**
     * 获取表格数据用于Excel导出
     * 子组件可以重写此方法来提供特定的数据格式
     */
    getTableDataForExport() {
      // 默认实现：尝试从DOM中提取表格数据
      const tables = this.$el?.querySelectorAll('table')
      if (!tables || tables.length === 0) {
        throw new Error('未找到表格数据')
      }

      const table = tables[0]
      const rows = table.querySelectorAll('tr')
      const data = []

      rows.forEach(row => {
        const cells = row.querySelectorAll('td, th')
        const rowData = Array.from(cells).map(cell => cell.textContent.trim())
        data.push(rowData)
      })

      return data
    },

    /**
     * 生成文件名
     * @param {string} extension 文件扩展名
     */
    generateFileName(extension) {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
      const baseName = this.exportFileName || this.$options.name || 'document'
      return `${baseName}_${timestamp}.${extension}`
    },

    /**
     * 格式化日期
     * @param {Date|string} date 日期
     */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    },

    /**
     * 格式化时间
     * @param {Date|string} date 日期时间
     */
    formatDateTime(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${this.formatDate(d)} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}:${String(d.getSeconds()).padStart(2, '0')}`
    }
  }
}

export default exportMixin
