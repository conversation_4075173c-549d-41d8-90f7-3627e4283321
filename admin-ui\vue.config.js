'use strict'
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const CompressionPlugin = require('compression-webpack-plugin')
const webpack = require('webpack')

const name = process.env.VUE_APP_TITLE || 'EURO维保管理平台' // 网页标题

const baseUrl = 'http://localhost:8080' // 后端接口

const port = process.env.port || process.env.npm_config_port || 80 // 端口

// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.XXX.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.usedCar.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "/" : "/",
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: 'dist',
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: 'static',
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === 'development',
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  transpileDependencies: ['quill'],
  // webpack-dev-server 相关配置
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    proxy: {
      // detail: https://cli.vuejs.org/config/#devserver-proxy
      [process.env.VUE_APP_BASE_API]: {
        target: baseUrl,
        changeOrigin: true,
        ws: true,  //启用 WebSocket 代理
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      },
      // springdoc proxy
      '^/v3/api-docs/(.*)': {
        target: baseUrl,
        changeOrigin: true
      }
    },
    allowedHosts: 'all'
  },
  css: {
    loaderOptions: {
      sass: {
        sassOptions: { outputStyle: "expanded" }
      }
    }
  },
  configureWebpack: {
    name: name,
    devtool: 'source-map', // 确保开发环境生成 sourcemap
    resolve: {
        alias: {
          '@': resolve('src')
        },
        fallback: {
          "path": require.resolve("path-browserify"),
          "util": require.resolve("util/"),
          "buffer": require.resolve("buffer/")
        }
      },
    plugins: [
      // http://doc.usedCar.vip/usedCar-vue/other/faq.html#使用gzip解压缩静态文件
      new CompressionPlugin({
        cache: false,                                  // 不启用文件缓存
        test: /\.(js|css|html|jpe?g|png|gif|svg)?$/i,  // 压缩文件格式
        filename: '[path][base].gz[query]',            // 压缩后的文件名
        algorithm: 'gzip',                             // 使用gzip压缩
        minRatio: 0.8,                                 // 压缩比例，小于 80% 的文件不会被压缩
        deleteOriginalAssets: false                    // 压缩后删除原文件
      }),
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
        process: 'process/browser'
      })
    ],
  },
  chainWebpack(config) {
    // 配置SCSS模块支持:export语法
    config.module
      .rule('scss')
      .test(/\.scss$/)
      .oneOf('vue-modules')
      .resourceQuery(/module/)
      .use('css-loader')
      .tap(options => {
        return {
          ...options,
          modules: {
            localIdentName: '[name]_[local]_[hash:base64:5]'
          }
        }
      })

    // config.plugins.delete('preload') // TODO: need test
    // config.plugins.delete('prefetch') // TODO: need test

    // 添加处理ico文件的loader
    // ico 专用 loader
    // config.module
    //   .rule('ico')
    //   .test(/\.ico$/)
    //   .use('file-loader')
    //   .loader('file-loader')
    //   .options({
    //     name: 'static/icons/[name].[hash:8].[ext]'
    //   })
    //   .end()


    // 完全重写字体处理规则
   // config.module.rules.delete('fonts')

    // Element UI字体文件特殊处理
    // config.module
    //   .rule('element-fonts')
    //   .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/)
    //   .include.add(/element-ui/)
    //   .end()
    //   .use('url-loader')
    //   .loader('url-loader')
    //   .options({
    //     limit: 10000,
    //     name: 'static/fonts/[name].[hash:8].[ext]',
    //     publicPath: '/',
    //     esModule: false
    //   })
    //   .end()

    // // 其他字体文件处理
    // config.module
    //   .rule('fonts')
    //   .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/)
    //   .exclude.add(/element-ui/)
    //   .end()
    //   .use('file-loader')
    //   .loader('file-loader')
    //   .options({
    //     name: 'static/fonts/[name].[hash:8].[ext]',
    //     publicPath: '/static/fonts/',
    //     esModule: false
    //   })
    //   .end()

    // 图片文件处理 - 删除并重新配置images规则
    // config.module.rules.delete('images')
    // config.module
    //   .rule('images')
    //   .test(/\.(png|jpe?g|gif|webp)(\?.*)?$/)
    //   .use('url-loader')
    //   .loader('url-loader')
    //   .options({
    //     limit: 4096, // 小于4KB的图片转为base64
    //     fallback: 'file-loader',
    //     name: 'static/img/[name].[hash:8].[ext]',
    //     publicPath: '/'
    //   })
    //   .end()

    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/assets/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config.when(process.env.NODE_ENV !== 'development', config => {
          config.optimization.splitChunks({
            chunks: 'all',
            cacheGroups: {
              // 单独打包Univer相关库，优先级最高
              univer: {
                name: 'chunk-univer',
                test: /[\\/]node_modules[\\/]@univerjs/,
                priority: 30,
                chunks: 'all',
                enforce: true
              },
              // Element UI 单独打包
              elementUI: {
                name: 'chunk-elementUI', // split elementUI into a single package
                test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
                priority: 20 // the weight needs to be larger than libs and app or it will be packaged into libs or app
              },
              // 其他第三方库
              libs: {
                name: 'chunk-libs',
                test: /[\\/]node_modules[\\/]/,
                priority: 10,
                chunks: 'initial' // only package third parties that are initially dependent
              },
              // 公共组件
              commons: {
                name: 'chunk-commons',
                test: resolve('src/components'), // can customize your rules
                minChunks: 3, //  minimum common number
                priority: 5,
                reuseExistingChunk: true
              }
            }
          })
          config.optimization.runtimeChunk('single')
    })
  }
}
