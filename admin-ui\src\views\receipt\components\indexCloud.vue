<template>
  <!-- 云表格结算单据模板 -->
  <div>
    <!-- 添加或修改工单记录对话框 -->
    <el-dialog  :visible.sync="dialogVisible"  @opened="onDialogOpened" :close-on-click-modal="false" title=""  width="900px" height="900px" >
        <h2 class="receipt-title" style="text-align: center; margin: 20px 0;">{{ dynamicReceiptTitle }}</h2>
        <div class="receipt-info" v-if="dynamicReceiptInfo" style="display: flex; flex-wrap: wrap; gap: 15px; margin: 10px 0; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
          <span class="info-item" v-for="(value, key) in dynamicReceiptInfo" :key="key" style="margin-right: 20px; color: #606266; font-size: 14px;">
            <strong>{{ key }}:</strong> {{ value }}
          </span>
        </div>

      <accept-receipt-cloud
        ref="acceptReceiptComponent"
        v-if="show==='accept'||show==='index'"
        :OrderDetailList="saveOrderDetailList"
        :FormData="saveForm"
        :open="printType==='acceptReceipt'"
        :show-actions="false"
        @data-updated="handleDataUpdated"
      >
        <template #header-actions>
          <!-- 操作按钮组 -->
          <div class="header-actions" style="margin: 15px 0; text-align: center;">
            <el-button
              type="primary"
              icon="el-icon-printer"
              @click="printReceipt"
              :disabled="!saveForm.serviceId"
              size="small">
              打印收据
            </el-button>

            <el-button
              type="success"
              icon="el-icon-download"
              @click="exportPDF"
              :disabled="!saveForm.serviceId"
              size="small">
              导出PDF
            </el-button>

            <el-button
              type="warning"
              icon="el-icon-download"
              @click="exportExcel"
              :disabled="!saveForm.serviceId"
              size="small">
              导出Excel
            </el-button>

            <el-button
              type="info"
              icon="el-icon-refresh"
              @click="refreshSpreadsheet"
              size="small">
              刷新表格
            </el-button>

            <el-button
              type="default"
              :icon="optionPrint ? 'el-icon-lock' : 'el-icon-unlock'"
              @click="toggleEditMode"
              size="small">
              {{ optionPrint ? '锁定编辑' : '解锁编辑' }}
            </el-button>
          </div>
        </template>
      </accept-receipt-cloud>

      <worker-receipt
        v-if="show==='worker'||show==='index'"
        :OrderDetailList="saveOrderDetailList"
        :OrderQuestionDetailList="tWorkOrderQuestionDetailList"
        :FormData="saveForm"
        :open="printType==='workerReceipt'"
      />

      <settlement-receipt
        v-if="show==='settlement'||show==='index'"
        :OrderDetailList="saveOrderDetailList"
        :OrderPartsDetailList="tWorkPartsDetailList"
        :FormData="saveForm"
        :open="printType==='settlementReceipt'"
      />

      <div slot="footer" class="dialog-footer">
        <el-row :gutter="12" class="mb8">
          <el-col :span="14" >
            <el-radio-group v-model="$i18n.locale" size="small">
              <el-radio-button label="zh" v-if="isShowOption">
                <span class="flag-icon flag-icon-cn"  style="margin-right: 6px;"></span>
                中文
              </el-radio-button>
              <el-radio-button label="en">
                <span class="flag-icon flag-icon-us" style="margin-right: 6px;"></span>
                English
              </el-radio-button>
              <el-radio-button label="ru">
                <span class="flag-icon flag-icon-ru" style="margin-right: 6px;"></span>
                Русский
              </el-radio-button>
              <el-radio-button label="ka">
                <span class="flag-icon flag-icon-ge" style="margin-right: 6px;"></span>
                ქართული
              </el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="10" >
            <el-button @click="handleClose">取 消</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">

</style>
<script>
import acceptReceiptCloud from '@/views/receipt/acceptReceiptCloud.vue'
import settlementReceipt from '@/views/receipt/settlementReceipt.vue'
import WorkerReceipt from '@/views/receipt/workerReceipt.vue'
import { receiptConfigFactory, RECEIPT_TYPES } from '@/utils/spreadsheet/ReceiptConfigFactory.js'
import { getWorkorder } from '@/api/manage/workorder'
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import { ensureLocaleLoaded } from '@/i18n/i18nMix'
import store from '@/store'
import { applyTranslation, isValidString, trimPunctuation } from '@/utils'
import Cookies from 'js-cookie'
Vue.use(VueI18n);
export const printI18n = new VueI18n({
  locale: 'zh', // 默认语言
  messages: {}, // 独立的消息存储
});

export default {
  i18n: printI18n,
  name:"receiptCloud",
  components: { WorkerReceipt, settlementReceipt, acceptReceiptCloud },
  data() {
    return {
    loading:false,
    isShowOption:true,
    transferMapping: {},
    transferPartsMapping: {},
    currentLanguage:'zh',
    // 工单详情表格数据
    tWorkOrderQuestionDetailList: [],
    // 工单详情表格数据
    tWorkOrderDetailList: [],
    // 工单详情表格数据
    tWorkPartsDetailList: [],
    FormData: {
      serviceId: null,
      title: null,
      businessType: "0",
      operatorType: "0",
      operProgress: 0,
      customerId: null,
      customerNickName: null,
      status: null,
      carPlateNumber:null,
      mainTotalAmount:0.00,
      taxRate:0.00,
      phonenumber:null,
      advancePayment:0,//预收金额
      discountAmount: 0,//折后金额
      totalAmount: 0,//总金额
      balancePayment: 0,//再付金额
      balance:0,//余额
      comment:null,
      images:null,
      operTime: null,
      costTime: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      remark: null,
      brand: null,
      color: null,
      expectedTime: null
      },
      saveForm:{},
      saveOrderDetailList:[],
      // 新增数据
      receiptTitle: '云表格预览',
      receiptInfo: null,
      optionPrint: false
    }
  },
  props: {
    serviceId:{
      type:String,
      default:""
    },
    show:{
      type:String,
      default:""
    },
    printType:{
      type:String,
      default:""
    },
    OpenPrint: {
      type: Boolean,
      default: false
    },
  },
  watch: {
    '$i18n.locale': {
      handler: async function (val) {
        if (this._inactive) return // 避免被keep-alive组件重复触发


        this.loading=true;
        /* 加载语言文件 */
        await ensureLocaleLoaded(val)
        /*  const messages = this.$store.getters['i18nStore/getMessages'](val) */

        if (isValidString(this.FormData.title)) {
          const fransStr = [];
        /*   if(isValidString(this.FormData.title)){
            fransStr.push(this.FormData.title);
          } */

          this.saveOrderDetailList.forEach(item => {
            if(isValidString(item.oldQuestionName)){
              fransStr.push(item.oldQuestionName);
            }
            if(isValidString(item.oldQuestionComment)){
              fransStr.push(item.oldQuestionComment);
            }
          });

          if(isValidString(this.FormData.comment)){
            fransStr.push(this.FormData.comment);
          }

          this.$nextTick(async () => {
            //去重
            const finalFransStr = [...new Set(fransStr)];
            if(finalFransStr.length===0||this.printType==='acceptReceipt'){
             /*  this.loading=false; */
              this.isShowOption=false;
             /*  return */
            }
            if(val===this.currentLanguage){
              this.saveOrderDetailList.forEach(item => {
                item.questionName=item.oldQuestionName
                item.questionComment=item.oldQuestionComment
              });
              this.saveForm.title=this.FormData.title
              this.saveForm.comment=this.FormData.comment
              this.loading=false;
            }else{
              store.dispatch('customerTranslation/translateBatchGoogle', {
                texts: finalFransStr,
                targetLang: val,
                sourceLang:this.currentLanguage,
              }).then(res => {
                if (res.code===200) {
                  const translateList = res.data;
                  //构建映射表
                  this.transferMapping={};
                  translateList.forEach(item => {
                    this.transferMapping[item.sourceText] = item.text;
                  });
                  console.log(this.transferMapping,finalFransStr,this.transferMapping)
                  applyTranslation(this.FormData.title,this.saveForm, 'title', this.transferMapping);
                  applyTranslation(this.FormData.comment,this.saveForm, 'comment', this.transferMapping);
                  this.saveOrderDetailList.forEach(item => {
                    console.log(item.oldQuestionName,item.oldQuestionComment)
                    applyTranslation(item.oldQuestionName,item, 'questionName', this.transferMapping);
                    applyTranslation(item.oldQuestionComment,item, 'questionComment', this.transferMapping);
                  });
                }
                this.loading=false;
              })
            }
          });

          const partsStr = [];
          if(this.printType==='settlementReceipt'&&this.tWorkPartsDetailList.length>0){
            this.tWorkPartsDetailList.forEach(item => {
              if(isValidString(item.oldpartsName)) {
                partsStr.push(item.oldpartsName);
              }
              if(isValidString(item.optionComment)) {
                partsStr.push(item.optionComment);
              }
            });
            //去重
            const finalPartsStr = [...new Set(partsStr)];
            if(finalPartsStr.length===0){
              return
            }
            store.dispatch('customerTranslation/translateBatchGoogle', {
              texts: finalPartsStr,
              targetLang: val,
              sourceLang:this.currentLanguage,
            }).then(res => {
              if (res.code===200) {
                const partsTranslate = res.data;
                //构建映射表
                this.transferPartsMapping={};
                partsTranslate.forEach(item => {
                  this.transferPartsMapping[item.sourceText] = item.text;
                });
                console.log(this.transferPartsMapping,finalPartsStr)
                this.tWorkPartsDetailList.forEach(item => {
                  applyTranslation(item.oldpartsName,item, 'partsName', this.transferPartsMapping);
                  applyTranslation(item.optionComment,item, 'optionComment', this.transferPartsMapping);
                });
              }
              this.loading=false;
            })
          }


        }else{
          this.saveForm=this.FormData;
          this.loading=false;
        }
      },
      immediate: true
    }
  },
  created() {
    this.currentLanguage = Cookies.get("language")
  /*   this.$i18n.locale="zh"; */
    console.log(this.$i18n.locale)
  },
  computed: {
    dialogVisible: {
      get() {
        return this.OpenPrint;
      },
      set(value) {
        this.$emit('update:OpenPrint', value);
      }
    },
    // 动态获取单据标题
    dynamicReceiptTitle() {
      // 根据printType获取对应的配置标题
      if (this.printType === 'acceptReceipt') {
        try {
          const config = receiptConfigFactory.getReceiptConfig(RECEIPT_TYPES.CAR_DAMAGE);
          return config.title || '汽车定损收车明细单';
        } catch (error) {
          console.warn('获取单据配置失败:', error);
          return '汽车定损收车明细单';
        }
      }
      return this.FormData?.title || '单据详情';
    },
    // 动态获取单据信息
    dynamicReceiptInfo() {
      if (!this.FormData) return null;

      const info = {};
      if (this.FormData.serviceId) info['服务单号'] = this.FormData.serviceId;
      if (this.FormData.customerName) info['客户姓名'] = this.FormData.customerName;
      if (this.FormData.customerPhone) info['联系电话'] = this.FormData.customerPhone;
      if (this.FormData.carNumber) info['车牌号'] = this.FormData.carNumber;
      if (this.FormData.createTime) info['创建时间'] = this.FormData.createTime;


      return Object.keys(info).length > 0 ? info : null;
    }
  },
  methods:{
    getInfo(){
      console.log('indexCloud getInfo - serviceId:', this.serviceId, 'printType:', this.printType)
      getWorkorder(this.serviceId).then(response => {
        console.log('indexCloud getInfo - API响应:', response.data)
        this.FormData = response.data
        this.saveForm=JSON.parse(JSON.stringify(this.FormData))
        let orderList=[];
        if(this.printType==='acceptReceipt'){
          console.log('indexCloud - 处理acceptReceipt类型')
          if(response.data.tWorkOrderQuestionDetailList!==undefined){
            this.tWorkOrderQuestionDetailList = response.data.tWorkOrderQuestionDetailList
            orderList= JSON.parse(JSON.stringify(this.tWorkOrderQuestionDetailList));
            console.log('indexCloud - tWorkOrderQuestionDetailList:', this.tWorkOrderQuestionDetailList)
          } else {
            console.log('indexCloud - tWorkOrderQuestionDetailList为undefined')
          }
        }else if(this.printType==='workerReceipt'){
          this.tWorkOrderQuestionDetailList = response.data.tWorkOrderQuestionDetailList
          if(response.data.tWorkOrderDetailList!==undefined){
            this.tWorkOrderDetailList = response.data.tWorkOrderDetailList
            orderList= JSON.parse(JSON.stringify(this.tWorkOrderDetailList));
          }
        }else if(this.printType==='settlementReceipt'){
          this.tWorkOrderQuestionDetailList = response.data.tWorkOrderQuestionDetailList
          orderList= JSON.parse(JSON.stringify(this.tWorkOrderQuestionDetailList));
          if(response.data.tWorkPartsDetailList!==undefined){
            this.tWorkPartsDetailList = response.data.tWorkPartsDetailList
            this.tWorkPartsDetailList.forEach(item=>{
              item.oldpartsName=item.partsName;
            })
          }
        }

        console.log('indexCloud - orderList处理前:', orderList)
        orderList.forEach(item=>{
          item.oldQuestionName=item.questionName;
          item.oldQuestionComment=item.questionComment;
        })
        //备份原文，防止全部翻译
        this.saveOrderDetailList=orderList;
        console.log('indexCloud - 最终saveOrderDetailList:', this.saveOrderDetailList)
        console.log('indexCloud - 最终saveForm:', this.saveForm)
        // 移除自动语言切换，避免触发watch监听器导致循环更新
      /*   if(this.saveOrderDetailList.length>0){
          this.$i18n.locale="en"
        } */
      }).catch(error => {
        console.error('indexCloud getInfo - API调用失败:', error)
      })
    },
    onDialogOpened(item) {
      console.log(this.$i18n.locale)
      // 对话框完全打开后的回调
      if (this.serviceId!==""){
        this.getInfo();
      }
    },
    handleClose() {
      this.saveForm={};
      this.tWorkOrderQuestionDetailList=[];
      this.saveOrderDetailList=[];
      this.tWorkPartsDetailList=[];
      this.tWorkOrderDetailList=[];
      this.$i18n.locale="zh";
      this.dialogVisible = false; // 通过 computed 修改，自动触发 emit
    },
    // 处理云表格数据更新
    handleDataUpdated(data) {
      console.log('云表格数据已更新:', data)
      this.$emit('data-updated', data)
    },

    /**
     * 打印收据
     */
    printReceipt() {
      // 调用子组件的打印方法
      const acceptReceiptComponent = this.$refs.acceptReceiptComponent || this.$children.find(child => child.$options.name === 'AcceptReceiptCloud')
      if (acceptReceiptComponent && acceptReceiptComponent.printReceipt) {
        acceptReceiptComponent.printReceipt()
      }
    },

    /**
     * 导出PDF
     */
    exportPDF() {
      const acceptReceiptComponent = this.$refs.acceptReceiptComponent || this.$children.find(child => child.$options.name === 'AcceptReceiptCloud')
      if (acceptReceiptComponent && acceptReceiptComponent.exportPDF) {
        acceptReceiptComponent.exportPDF()
      }
    },

    /**
     * 导出Excel
     */
    exportExcel() {
      const acceptReceiptComponent = this.$refs.acceptReceiptComponent || this.$children.find(child => child.$options.name === 'AcceptReceiptCloud')
      if (acceptReceiptComponent && acceptReceiptComponent.exportExcel) {
        acceptReceiptComponent.exportExcel()
      }
    },

    /**
     * 刷新表格
     */
    refreshSpreadsheet() {
      const acceptReceiptComponent = this.$refs.acceptReceiptComponent || this.$children.find(child => child.$options.name === 'AcceptReceiptCloud')
      if (acceptReceiptComponent && acceptReceiptComponent.refreshSpreadsheet) {
        acceptReceiptComponent.refreshSpreadsheet()
      }
    },

    /**
     * 切换编辑模式
     */
    toggleEditMode() {
      this.optionPrint = !this.optionPrint
      const acceptReceiptComponent = this.$refs.acceptReceiptComponent || this.$children.find(child => child.$options.name === 'AcceptReceiptCloud')
      if (acceptReceiptComponent && acceptReceiptComponent.toggleEditMode) {
        acceptReceiptComponent.toggleEditMode()
      }
    }
  }
}
</script>
