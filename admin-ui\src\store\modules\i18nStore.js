import Vue from 'vue'
import i18n from '@/i18n/i18n'
import { isValidString, mergeAndSwap } from '@/utils'
export default {
  namespaced: true,  // 确保启用命名空间
  state: {
    loadedLocales: {},
    loadingStates: {},
    errors: {}
  },
  mutations: {
    markLocaleLoaded(state, { messages,textToIdCache }) {
      Vue.set(state.loadedLocales, 'global', {  // 使用 Vue.set 确保响应式
        loadedAt: new Date(),
        messages,
        textToIdCache
      })
    },
    setLoading(state, { loading }) {
      Vue.set(state.loadingStates,'global', loading)  // 使用 Vue.set
    },
    setError(state, {  error }) {
      state.errors = error
    }
  },
  getters: {
    isLocaleLoaded: (state) =>{
      return !!state.loadedLocales['global']
    },
    isLoading: (state) => {
      return !!state.loadingStates['global']
    },
    getMessages: (state) => {
      return state.loadedLocales['global']?.messages || {}
    },
    getMessage: (state) => (zhText) => {
      let textToIdCache = state.loadedLocales['global']?.textToIdCache || {};
      if(Object.keys(textToIdCache).length===0){
        textToIdCache= mergeAndSwap(i18n.messages.zh, null);
      }
      const matchedId = textToIdCache[zhText];
      return isValidString(matchedId) ? i18n.t(matchedId) : zhText;
    },
    setError(state, {  error }) {
      state.errors = error
    }
  }
}
