<template>
  <div class="app-container">
    <el-form :model="queryParams"
             ref="queryForm"
             size="small"
             :inline="!isMobile"
             v-show="showSearch"
             :label-width="isMobile ? '100%' : '140px'"
             class="mobile-form-adapt"
    >
      <el-form-item :label="strConvert('工单编号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('工单标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入工单标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户编号')" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          :placeholder="strConvert('请输入客户编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户姓名')" prop="customerNickName">
        <el-input
          v-model="queryParams.customerNickName"
          :placeholder="strConvert('请输入客户姓名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('车牌号')" prop="carPlateNumber">
        <el-input
          v-model="queryParams.carPlateNumber"
          :placeholder="strConvert('请输入车牌号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          :style="{width: isMobile ? '100%' : '240px'}"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="strConvert('开始日期')"
          :end-placeholder="strConvert('结束日期')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{strConvert('搜索')}}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{strConvert('重置')}}</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="workorderList"
              :style="{width: '100%', height: isMobile ? mobileMaxHeight + 'px' : 'auto'}"
              @expand-change="openExpandRow"
              row-key="serviceId"
              :expand-row-keys="selectRow"
              class="mobile-table-adapt">
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div class="tableChild">
            <el-table
              ref="childTable"
              :ref="'childTable' + scope.row.serviceId"
                      v-loading="tableLoading"  :data="tWorkOrderDetailList[scope.row.serviceId]"
                       border
                      :span-method="(params) => objectSpanMethod(params, tWorkOrderDetailList[scope.row.serviceId])"
                      :style="{width: '100%', height: isMobile ? mobileMaxHeight + 'px' : 'auto'}"
                      class="mobile-table-adapt">
<!--              <el-table-column :label="strConvert('服务编号')" align="center" prop="questionId" width="80" />-->
              <el-table-column :label="strConvert('维修服务类型')" align="center" prop="questionType" width="280">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.t_work_order_detail_service_type" :value="scope.row.questionType"/>
                </template>
              </el-table-column>
              <el-table-column width="200" :label="strConvert('服务')" prop="questionName" />
              <el-table-column width="90" :label="strConvert('数量')" prop="num" />
<!--              <el-table-column width="90" :label="strConvert('单价')" prop="price" align="right"/>
              <el-table-column width="90" :label="strConvert('金额')" prop="amount" align="right"/>-->
              <el-table-column :label="strConvert('进度')" prop="operProgress" :width="isMobile ? '120' : '150'" align="center"  >
                <template slot-scope="scope">
                  <el-progress :text-inside="true" :stroke-width="26"
                               status="success"
                               text-color="#ffff"
                               :percentage="isValidString(scope.row.operProgress)?parseInt(scope.row.operProgress):0"></el-progress>
                </template>
              </el-table-column>
              <el-table-column width="250" :label="strConvert('备注')" prop="questionComment" />
              <el-table-column width="150" :label="strConvert('操作')" align="center"  class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <div class="mobile-action-buttons">
                    <el-button
                      v-if="parseInt(scope.row.operProgress)<100"
                      size="small"
                      type="warning"
                      icon="el-icon-s-tools"
                      @click.native.prevent="handleProgress(scope.row)"
                      v-hasPermi="['manage:workorder:edit']"
                    >{{strConvert('更新进度')}}</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('工单编号')"  :width="isMobile ? '120' : '155'" align="center" prop="serviceId" />
      <el-table-column :label="strConvert('工单标题')" align="center" prop="title" />
      <el-table-column :label="strConvert('车牌号')" width="90" align="center" prop="carPlateNumber" />
      <el-table-column :label="strConvert('总进度')" :width="isMobile ? '120' : '150'" align="center" prop="operProgress" >
        <template slot-scope="scope">
          <el-progress :text-inside="true" :stroke-width="26"
                       status="success"
                       text-color="#ffff"
                       :percentage="parseInt(scope.row.operProgress)"></el-progress>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('消耗时间')" align="center" prop="costTime" />-->
<!--      <el-table-column :label="strConvert('图片')" align="center" width="180" prop="images">
        <template slot-scope="scope">
          <image-preview :src="scope.row.images" :width="50" :height="50"/>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('问题描述')" align="center" prop="comment" width="180"/>
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('操作')" align="center"  :width="isMobile ? '100%' : '300'" class-name="small-padding fixed-width">
        <template slot-scope="scope">
            <el-button
              type="warning"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['manage:workorder:edit']"
            >{{strConvert('添加配件')}}</el-button>
            <el-button
              type="text"
              icon="el-icon-info"
              v-hasPermi="['manage:workorder:edit']"
              @click="$router.push(`/manage/progressForm/receive/${scope.row.serviceId}`)" >
              {{strConvert('汇总信息')}}
            </el-button>
            <el-button
              size="small"
              icon="el-icon-edit"
              v-hasPermi="['manage:workorder:edit']"
              type="text"
              @click="recall(scope.row.serviceId)"
            >
              {{strConvert('回撤')}}
            </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
      :small="isMobile"
    />

    <!-- 添加或修改工单记录对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" size="mini" label-width="80px">
        <el-row v-if="parseInt(form.operProgress)!==100" :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus"  @click="handleAddTWorkOrderDetail">{{strConvert('添加')}}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete"  @click="handleDeleteTWorkOrderDetail">{{strConvert('删除')}}</el-button>
          </el-col>
        </el-row>
        <el-table :data="tWorkPartsDetailList" :row-class-name="rowTWorkOrderDetailIndex"
                  size="mini"
                  @selection-change="handleTWorkOrderDetailSelectionChange" ref="tWorkOrderDetail">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column :label="strConvert('序号')" align="center" prop="index" width="150"/>
          <el-table-column :label="strConvert('配件编号')" prop="partsId" width="115">
            <template slot-scope="scope">
              <el-input disabled v-model="scope.row.partsId"  />
            </template>
          </el-table-column>
          <el-table-column :label="strConvert('配件名称')" prop="partsName" width="160">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.partsName"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入配件名称')"
                :remote-method="querySearchParts"
                @change="(val) => handleSelectParts(val, scope.row)"
                :loading="loading">
                <el-option
                  v-for="item in queryListParts"
                  :key="item.id"
                  :style="item.num>0?'':'color: red'"
                  :label="item.value+' '+strConvert('库存')+item.num"
                  :value="item.id">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column :label="strConvert('规格')" prop="norm" width="90"/>
          <el-table-column :label="strConvert('数量')" prop="num" width="80">
            <template slot-scope="scope">
              <el-input v-model="scope.row.num" @blur="countPartsAmount(scope.row)" type="number" min="1" />
            </template>
          </el-table-column>
          <el-table-column :label="strConvert('单价')" prop="price" width="90"/>
          <el-table-column :label="strConvert('金额')" prop="amount" width="90" />
          <el-table-column :label="strConvert('操作描述')" prop="optionComment" width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.optionComment" type="textarea" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <el-row :gutter="10">
        <el-col :span="5" :offset="19"><div style="font-weight: bold;line-height: 25px;margin: 10px">{{strConvert('合计')}}：{{partsTotalAmount}}</div></el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button v-if="parseInt(form.operProgress)!==100" type="primary" @click="submitFormParts">{{strConvert('确 定')}}</el-button>
        <el-button @click="cancel">{{strConvert('取 消')}}</el-button>
      </div>
    </el-dialog>


    <!-- 添加或修改工单记录对话框 -->
    <el-dialog :close-on-click-modal="false" :title="strConvert('更新 ')+(tWorkOrderDetail?tWorkOrderDetail.questionName:'')+strConvert(' 进度')" :visible.sync="progressOpen" width="350px" append-to-body>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-card style="padding: 10px;margin: 10px;text-align:center">
            <el-progress type="circle" :percentage="percentage" :color="colors"></el-progress>
              <el-button-group>
                <el-button icon="el-icon-minus" @click="decrease"></el-button>
                <el-button icon="el-icon-plus" @click="increase"></el-button>
              </el-button-group>
            </el-card>
          </el-col>
        </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormProgress">{{strConvert('更 新')}}</el-button>
        <el-button @click="cancelProgress">{{strConvert('取 消')}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'
import { sprintf } from 'sprintf-js'
import {
  listWorkorder,
  getWorkorder,
  delWorkorder,
  addWorkorder,
  updateWorkorder,
  tWorkOrderProgress, getOrderDetailInfo, updateTWorkPartsDetail
} from '@/api/manage/workorder'
import { listCustomer, addCustomer, getInfoByNickName } from '@/api/manage/customer'
import { getInfo } from '@/api/login'
import { listParts, listPartsJoinPurchase } from '@/api/manage/parts'
import QuestionTable from '@/views/manage/workorder/components/quesitonTable.vue'
import { isValidString } from '@/utils'
import { parseTime } from '../../../utils/usedcar'
export default {
  name: "Workorder",
  components: { QuestionTable },
  dicts: ['t_work_order_detail_service_type'],
  data() {
    return {
      isMobile: false,
      mobileMaxHeight: 500,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedTWorkOrderDetail: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      restCustomer: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单记录表格数据
      workorderList: [],
      // 工单配件详情表格数据
      tWorkPartsDetailList: [],
      // 派工单详情表格数据
      tWorkOrderDetailList: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      fastOpen:false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceId: null,
        title: null,
        businessType: null,
        carPlateNumber:null,
        customerId: null,
        customerNickName: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        customerNickName: [
          { required: true, message: this.strConvert('客户姓名不能为空'), trigger: "blur" }
        ],
        title: [
          { required: true, message: this.strConvert('标题不能为空'), trigger: "blur" }
        ],
        comment: [
          { required: true, message: this.strConvert('问题描述不能为空'), trigger: "blur" }
        ],
        phonenumber: [
          { required: true, message: this.strConvert('客户电话不能为空'), trigger: "blur" }
        ],
        carPlateNumber: [
          { required: true, message: this.strConvert('车牌号不能为空'), trigger: "blur" }
        ]
      },
      tableLoading:true,
      queryListParts:null,
      progressOpen:false,
      percentage: 10,
      tWorkOrderDetail:null,
      selectRow:[],
      restParts:{},
      partsTotalAmount:0.00,
      colors: [
        {color: '#f56c6c', percentage: 20},
        {color: '#e6a23c', percentage: 40},
        {color: '#a15cb8', percentage: 60},
        {color: '#44d2a7', percentage: 80},
        {color: '#76d36f', percentage: 100}
      ]
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.checkMobile();
    window.addEventListener('resize', this.checkMobile);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkMobile);
  },
  methods: {
    strConvert,
    parseTime,
    isValidString,
    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
      this.mobileMaxHeight = window.innerHeight - 300; // 根据实际布局调整
    },
    increase() {
      this.percentage += 10;
      if (this.percentage > 100) {
        this.percentage = 100;
      }
    },
    decrease() {
      this.percentage -= 10;
      if (this.percentage < 0) {
        this.percentage = 0;
      }
    },
    /** 查询工单记录列表 */
    getList() {
      this.loading = true
      this.selectRow=[];
      this.queryParams.params = {}
      this.workorderList =[];
      if (null != this.daterangeCreateTime && '' !== this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      this.queryParams.businessType="2"
      /**此处添加角色权限过滤*/
      if(this.$store.getters.roles.indexOf('common')>=0){
        this.queryParams.operName=this.$store.getters.name;
      }
      listWorkorder(this.queryParams).then(response => {
        if(response.rows.length>0){
          this.workorderList = response.rows
          this.total = response.total;
          this.openExpandRow(this.workorderList[0],[this.workorderList[0]])
          this.$nextTick(() => {
            // 强制刷新当前子表格
            const childTableRef = this.$refs['childTable' + this.queryParams.serviceId];
            if (childTableRef && childTableRef.doLayout) {
              childTableRef.doLayout();
            }
          });

        }
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
    },
    // 表单重置
    reset() {
      this.form = {
        serviceId: null,
        title: null,
        businessType: "0",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        phonenumber:null,
        expectedTime:null,
        comment:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        fastRemark:null
      }
      this.form.title= new Date()+this.strConvert('接收车辆');
      this.tWorkPartsDetailList = []
      this.tWorkOrderDetailList = {}
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    openExpandRow(row, expandedRows) {
      const serviceId = row.serviceId;
      this.selectRow = expandedRows.map(item => item.serviceId);

      if (this.selectRow.includes(serviceId)) {
        this.tableLoading = true;
        getWorkorder(serviceId).then(response => {
          this.form = response.data;
          if (response.data.tWorkPartsDetailList) {
            this.tWorkPartsDetailList = response.data.tWorkPartsDetailList;
          }
          if (response.data.tWorkOrderDetailList) {
            // 排序，确保相同类型连续排列
            const sortedList = response.data.tWorkOrderDetailList.sort((a, b) => {
              return String(a.questionType).localeCompare(String(b.questionType));
            });
            // 响应式赋值
            this.$set(this.tWorkOrderDetailList, serviceId, sortedList);
          }
          this.tableLoading = false;

          // 强制更新表格布局（可选）
          this.$nextTick(() => {
            this.$refs.childTable?.doLayout();  // 如果有 ref="childTable"
          });
        });
      }
    },
    // 生成合并策略
    objectSpanMethod({ row, column, rowIndex }, data) {
      if (column.property === 'questionType') {
        const prevRow = data[rowIndex - 1];
        const currentType = row.questionType;

        if (!prevRow || prevRow.questionType !== currentType) {
          let spanCount = 1;
          for (let i = rowIndex + 1; i < data.length; i++) {
            if (data[i].questionType === currentType) {
              spanCount++;
            } else {
              break;
            }
          }
          return { rowspan: spanCount, colspan: 1 };
        } else {
          return { rowspan: 0, colspan: 0 };
        }
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.serviceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.strConvert('添加工单')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const serviceId = row.serviceId || this.ids
      getWorkorder(serviceId).then(response => {
        this.form = response.data
        if(response.data.tWorkPartsDetailList!==undefined){
          this.tWorkPartsDetailList = response.data.tWorkPartsDetailList
        }
        this.open = true
        this.title = this.strConvert('工单：')+this.form.serviceId+this.strConvert(' 配件信息添加');
      })
    },
    /** 修改进度操作 */
    handleProgress(row) {
      this.tWorkOrderDetail =row;
      getOrderDetailInfo(row.id).then(response => {
        if(response.data!==undefined){
          this.tWorkOrderDetail =response.data
          this.percentage=parseInt(this.tWorkOrderDetail.operProgress)
        }
        this.progressOpen = true
      })
    },
    /** 提交按钮 */
    submitFormProgress() {
      if(this.percentage===0){
        this.$modal.msgError(this.strConvert('进度更新不能为0%'));
        return;
      }
       this.tWorkOrderDetail.operProgress=this.percentage
      tWorkOrderProgress(this.tWorkOrderDetail).then(response => {
          if(response.code===200){
            this.$modal.msgSuccess(this.strConvert('更新成功'))
            this.progressOpen = false
            this.getList()
          }
      })
    },
    cancelProgress(){
      this.progressOpen = false
    },
    /** 提交按钮 */
    submitFormParts() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.tWorkPartsDetailList = this.tWorkPartsDetailList
          if (this.form.serviceId != null) {
            updateTWorkPartsDetail(this.form).then(response => {
              this.$modal.msgSuccess(this.strConvert('修改成功'))
              this.open = false
              this.getList()
            })
          } else {
            addWorkorder(this.form).then(response => {
              this.$modal.msgSuccess(this.strConvert('新增成功'))
              this.open = false
              this.reset()
              this.getList()
            })
          }
        }
      })
    },

	/** 工单详情序号 */
    rowTWorkOrderDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 工单详情添加按钮操作 */
    handleAddTWorkOrderDetail() {
      let obj = {}
      obj.partsType = ""
      obj.num = 1
      obj.resType = ""
      obj.materialId = ""
      obj.materialName = ""
      obj.partsId = ""
      obj.partsName = ""
      obj.norm = ""
      obj.price=""
      obj.amount=""
      obj.optionType = ""
      obj.purchaseId = ""
      obj.cost = ""
      obj.currencyType = ""
      obj.sort = ""
      obj.remark = ""
      console.log(this.tWorkPartsDetailList)
      this.tWorkPartsDetailList.push(obj)
    },
    /** 工单详情删除按钮操作 */
    handleDeleteTWorkOrderDetail() {
      if (this.checkedTWorkOrderDetail.length == 0) {
        this.$modal.msgError(this.strConvert('请先选择要删除的工单详情数据'))
      } else {
        const tWorkPartsDetailList = this.tWorkPartsDetailList
        const checkedTWorkOrderDetail = this.checkedTWorkOrderDetail
        this.tWorkPartsDetailList = tWorkPartsDetailList.filter(function(item) {
          return checkedTWorkOrderDetail.indexOf(item.index) == -1
        })
      }
    },
    /** 复选框选中数据 */
    handleTWorkOrderDetailSelectionChange(selection) {
      this.checkedTWorkOrderDetail = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/workorder/export', {
        ...this.queryParams
      }, `workorder_${new Date().getTime()}.xlsx`)
    },
    handleSelectParts(value, row) {
      console.log(value,row)
      row.partsId=value;
      row.partsName=this.restParts[value].partsName;
      row.norm=this.restParts[value].norm;
      row.price=this.restParts[value].sellingPrice;
      this.countPartsAmount(row);
    },
    /* 查找配件信息 */
    querySearchParts(queryString) {
      if(isValidString(queryString)) {
        this.restParts={};
        listPartsJoinPurchase({ "partsName": queryString,"serviceId":this.form.serviceId }).then(response => {
          if (response.rows.length > 0) {
            this.queryListParts = response.rows.map(item => {
            this.restParts[item.partsId]=item;
              return {
                value: item.partsName,
                id: String(item.partsId),
                partsType: item.partsType,
                num: item.num,
              }
            });
          }
        })
      }else {
        this.queryListParts = []; // 输入为空时清空历史数据
      }
    },
    /*统计配件总金额*/
    countPartsAmount(row){
      this.partsTotalAmount=0.00;
      row.amount=parseFloat(parseFloat(row.price||"0")*row.num).toFixed(2);
      this.tWorkPartsDetailList.map(item=>{
        this.partsTotalAmount=parseFloat(this.partsTotalAmount +item.amount).toFixed(2);
      })
    },
    recall(serviceId){
      if(serviceId!==undefined){
        this.$modal.confirm(this.strConvert('操作后工单将退回至《受理登记》中，确认是否继续？')).then(() => {
          getWorkorder(serviceId).then(response => {
            let work = response.data
            work.businessType=0;
            updateWorkorder(work).then(response => {
              if(response.code===200){
                this.$modal.msgSuccess(this.strConvert('回撤成功'))
                this.getList();
              }
            });
          })
        }).catch(() => {})
      }
    }
  }
}
</script>
<style scoped lang="scss">
.tableChild{
  box-shadow: inset 0 2px 4px rgb(6, 152, 29);
  padding: 2px;
  margin: 0 auto;     /* 水平居中 */
  width: fit-content; /* 宽度自适应内容 */
  text-align: center; /* 内部文本居中（可选） */
}
</style>
