<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item :label="strConvert('采购单号')" prop="purchaseId">
        <el-input
          v-model="queryParams.purchaseId"
          :placeholder="strConvert('请输入采购单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建者')" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          :placeholder="strConvert('请输入创建者')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="strConvert('物件类型')" prop="resType">
        <el-select v-model="queryParams.resType" :placeholder="strConvert('请选择物件类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_res_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('工单号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('物件申请单号')" prop="resRequestId">
        <el-input
          v-model="queryParams.resRequestId"
          :placeholder="strConvert('请输入工单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item :label="strConvert('审批状态')" prop="approvalStatus">
        <el-select v-model="queryParams.approvalStatus" :placeholder="strConvert('请选择审批状态')" clearable>
          <el-option
            v-for="dict in dict.type.t_approval_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('审批时间')" prop="approvalTime">
        <el-date-picker clearable
          v-model="queryParams.approvalTime"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="strConvert('请选择审批时间')">
        </el-date-picker>
      </el-form-item>-->

      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"

          icon="el-icon-plus"
          size="medium"
          @click="handleAdd"
          v-hasPermi="['manage:purchase:add']"
        >{{ strConvert('采购登记') }}</el-button>
      </el-col>
      <!--    <el-col :span="1.5">
           <el-button
             type="success"

             icon="el-icon-edit"
             size="medium"
             :disabled="single"
             @click="handleUpdate"
             v-hasPermi="['manage:purchase:edit']"
           >{{ strConvert('修改') }}</el-button>
         </el-col>
        <el-col :span="1.5">
           <el-button
             type="danger"

             icon="el-icon-delete"

             :disabled="multiple"
             @click="handleDelete"
             v-hasPermi="['manage:purchase:remove']"
           >{{ strConvert('删除') }}</el-button>
         </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"
          size="medium"
          @click="handleExport"
          v-hasPermi="['manage:purchase:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <el-col :span="10">
        <span style="color: red">{{ strConvert('注意：报销确认，同时生成《财务流水》出帐记录') }}</span>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purchaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('采购单号')" width="125" align="center" prop="purchaseId" />
      <el-table-column :label="strConvert('标题')" align="center" width="150" prop="title" />
      <el-table-column :label="strConvert('物件类型')" align="center" prop="resType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_res_type" :value="scope.row.resType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('图像集')" align="center" prop="images" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.images" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('工单号')" align="center" width="155" prop="serviceId" />
      <el-table-column :label="strConvert('物件申请单号')" align="center" width="130" prop="resRequestId" />
<!--      <el-table-column :label="strConvert('审批意见')" align="center" prop="approvalComments" />
      <el-table-column :label="strConvert('审批状态')" align="center" prop="approvalStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_approval_status" :value="scope.row.approvalStatus"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('审批时间')" align="center" prop="approvalTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('总费用')" align="center" width="80" prop="amount" />
      <el-table-column :label="strConvert('同步库存')" align="center" width="80" prop="isStore">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_is_store" :value="scope.row.isStore"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('创建者')" align="center" prop="createBy" />
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('操作')" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div v-if="scope.row.isStore=='0'">

            <el-button
              v-if="scope.row.amount>0"
              type="warning"
              size="small"
              icon="el-icon-plus"
              @click="handleAddObjectStore(scope.row)"
              v-hasPermi="['manage:objectStorage:add']"
            >{{ strConvert('生成入库单') }}</el-button>
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['manage:purchase:edit']"
            >{{ strConvert('修改') }}</el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['manage:purchase:remove']"
            >{{ strConvert('删除') }}</el-button>
          </div>
          <div v-if="scope.row.isStore=='1'">
            <el-button
              v-if="scope.row.amount>0"
              type="warning"
              size="small"
              icon="el-icon-edit"
              @click="handleUpdateFlow(scope.row)"
              v-hasPermi="['manage:flow:add']"
            >{{ strConvert('报销确认') }}</el-button>
          </div>

            <el-button
              type="info"
              icon="el-icon-info"
              @click="handleInfo(scope.row)"
            >{{ strConvert('查看详情') }}</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购单对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('标题')" prop="title">
              <el-input v-model="form.title"   :disabled="!isEdit"   :placeholder="strConvert('请输入标题')" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item :label="strConvert('总费用')" prop="amount">
              <el-input v-model="form.amount" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('物件类型')" prop="resType">
              <el-select v-model="form.resType"
                         :disabled="!isEdit"
                         :placeholder="strConvert('请选择物件类型')">
                <el-option
                  v-for="dict in dict.type.t_res_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item v-if="form.resType === '1'" :label="strConvert('物件申请单号')" prop="serviceId">
              <el-select
                :disabled="!isEdit"
                style="width: 200px"
                v-model="form.resRequestId"
                filterable remote  reserve-keyword
                :placeholder="strConvert('请输入物件申请单号')"
                :remote-method="querySearchResRequest"
                @change="handleResRequest"
                :loading="loading">
                <el-option
                  v-for="item in queryList"
                  :key="item.id"
                  :label="item.id"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="form.resType === '2'" :label="strConvert('工单号')" prop="serviceId">
              <el-select
                style="width: 200px"
                :disabled="!isEdit"
                v-model="form.serviceId"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入工单号')"
                :remote-method="querySearchWorkOrder"
                @change="handleWorkOrder"
                :loading="loading">
                <el-option
                  v-for="item in queryList"
                  :key="item.id"
                  :label="item.id"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('图像集')" prop="images">
              <image-upload v-if="isEdit"
                :enable-image-compress="true"
                :max-image-size="100"
                :image-quality="0.7"
                :max-image-width="800"
                :max-image-height="800"
                v-model="form.images"
              />
              <image-preview v-else :src="form.images" :width="50" :height="50"/>
            </el-form-item>

          </el-col>
          <el-col :span="10">
            <el-form-item :label="strConvert('描述')" prop="comment">
              <el-input v-model="form.comment"   :disabled="!isEdit" rows="6" type="textarea" :placeholder="strConvert('请输入内容')" />
            </el-form-item>
          </el-col>
          <el-col :span="1.5">
          </el-col>
        </el-row>

        <el-divider content-position="center">采购单详情信息</el-divider>
        <el-row v-if="form.isStore!=='1'" :gutter="10" class="mb8">
          <el-col :span="1.5" >
            <el-button type="primary" icon="el-icon-plus"  @click="handleAddTPurchaseDetail">{{ strConvert('添加') }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete"  @click="handleDeleteTPurchaseDetail">{{ strConvert('删除') }}</el-button>
          </el-col>
        </el-row>
        <el-table :data="tPurchaseDetailList" :row-class-name="rowTPurchaseDetailIndex" @selection-change="handleTPurchaseDetailSelectionChange" ref="tPurchaseDetail">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column :label="strConvert('序号')" align="center" prop="index" width="50"/>
          <el-table-column v-if="form.resType === '1'" :label="strConvert('物料名称')" prop="materialName" width="150">
            <template slot-scope="scope">
              <el-select
                style="width: 300px"
                v-model="scope.row.materialName"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入物料名称')"
                :remote-method="querySearchMaterial"
                @change="cleanData"
                @blur="() => handleSelectMaterial(scope.row.materialName, scope.row)"
                :loading="loading">
                <el-option
                  v-for="item in queryListMaterial"
                  :key="item.value"
                  :style="item.num>0?'':'color: red'"
                  :label="item.value+' 库存'+item.num"
                  :value="item.value">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column v-if="form.resType === '2'" :label="strConvert('配件名称')" prop="partsName" width="150">
            <template slot-scope="scope">
              <el-select
                :disabled="!isEdit"
                style="width: 200px"
                v-model="scope.row.partsName"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入配件名称')"
                :remote-method="querySearchParts"
                @change="(val) => handleSelectParts(val, scope.row)"
                :loading="loading">
                <el-option
                  v-for="item in queryListParts"
                  :key="item.id"
                  :style="item.num>0?'':'color: red'"
                  :label="item.value+' 库存'+item.num"
                  :value="item.id">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column  :label="strConvert('数量')" prop="num" width="100">
            <template slot-scope="scope">
              <el-input  v-model="scope.row.num" :disabled="!isEdit" :placeholder="strConvert('请输入数量')" @blur="countAmount(scope.row)" type="number" min="1" />
            </template>
          </el-table-column>
          <el-table-column  :label="strConvert('规格')" prop="norm" width="120" />
          <el-table-column :label="strConvert('单价')" prop="price" width="100">
            <template slot-scope="scope">
              <el-input type="number" :disabled="!isEdit" v-model="scope.row.price" @blur="countAmount(scope.row)" :placeholder="strConvert('请输入单价')" />
            </template>
          </el-table-column>
          <el-table-column :label="strConvert('费用')" prop="amount" width="100" />
          <el-table-column :label="strConvert('说明')" prop="comment" width="180">
            <template slot-scope="scope">
              <el-input v-model="scope.row.comment" rows="2" :disabled="!isEdit" type="textarea" :placeholder="strConvert('请输入说明')" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- 库存已同步的单据不得再次修改 -->
        <el-button v-if="isEdit" type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import {
  listPurchase,
  getPurchase,
  delPurchase,
  addPurchase,
  updatePurchase,
  updatePurchaseStore, updatePurchaseFlow
} from '@/api/manage/purchase'
import { listCustomer } from '@/api/manage/customer'
import { listWorkorder, getWorkorder, delWorkorder, addWorkorder, updateWorkorder } from "@/api/manage/workorder"
import { getRequest, listRequest } from '@/api/manage/request'
import { listParts, listPartsJoinPurchase } from '@/api/manage/parts'
import { listMaterial } from '@/api/manage/material'
import { getInfo } from '@/api/login'
import { formatDate, isValidString } from '@/utils'
import { parseTime } from '../../../utils/usedcar'
import { addObjectStorageByPurchaseId } from '@/api/manage/objectStorage'
export default {
  name: "Purchase",
  dicts: ['t_approval_status', 't_res_type','t_currency_type','t_is_store'],
  data() {
    return {
      // 遮罩层
      loading: true,
      isEdit:false,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedTPurchaseDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购单表格数据
      purchaseList: [],
      // 采购单详情表格数据
      tPurchaseDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        purchaseId: null,
        title: null,
        resType: null,
        resRequestId: null,
        serviceId: null,
        approvalStatus: null,
        approvalTime: null,
        createBy: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        resType: [
          { required: true, message: "物件类型不能为空", trigger: "change" }
        ],
      },

      queryList:null,
      queryListParts:null,
      queryListMaterial:null,
      restParts:{},
    }
  },
  created() {
    this.getList()
    getInfo().then(res => {
      this.user = res.user;
    })
  },
  methods: {
    strConvert,

    parseTime,
    /** 查询采购单列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' !== this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      listPurchase(this.queryParams).then(response => {
        this.purchaseList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        purchaseId: null,
        title: null,
        resType: '2',
        operatorType: null,
        images: null,
        serviceId: null,
        approvalName: null,
        approvalComments: null,
        approvalStatus: null,
        approvalTime: null,
        isStore:null,
        amount: 0.00,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        comment: null
      }
      this.form.title= formatDate(new Date())+'采购';
      this.tPurchaseDetailList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.purchaseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.isEdit=true;
      this.title = "添加采购单"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const purchaseId = row.purchaseId || this.ids
      getPurchase(purchaseId).then(response => {
        this.isEdit=true;
        this.form = response.data
        this.tPurchaseDetailList = response.data.tPurchaseDetailList
        this.open = true
        this.title = "修改采购单"
      })
    },
    /** 查看按钮操作 */
    handleInfo(row) {
      this.reset()
      const purchaseId = row.purchaseId || this.ids
      getPurchase(purchaseId).then(response => {
        this.isEdit=false;
        this.form = response.data
        this.tPurchaseDetailList = response.data.tPurchaseDetailList
        this.open = true
        this.title = "查看采购单"
      })
    },
    handleUpdateFlow(row){
      const purchaseIds = row.purchaseId || this.ids
      this.$modal.confirm('单号为"' + purchaseIds + '"的采购单，确认是否确认报销？报销后将生成出帐流水。').then(() => {
        const rowData=row;
        if (rowData.purchaseId != null) {
          getPurchase(rowData.purchaseId).then(response => {
            this.form = response.data
            this.tPurchaseDetailList = response.data.tPurchaseDetailList
            if (this.tPurchaseDetailList != null) {
              rowData.isStore='1';//是否出帐
              rowData.updateBy=this.$store.getters.name;
              rowData.updateTime=formatDate(new Date());
              updatePurchaseFlow(rowData).then(response => {
                this.getList()
                this.$modal.msgSuccess("出帐流水生成成功")
                this.open = false
              })
            } else {
              this.$modal.msgError("请重新检查采购清单后，确保数据完整后再试")
            }
          });
        }
      }).catch(() => {})
    },
    handleAddObjectStore(row){
      const purchaseIds = row.purchaseId || this.ids
      this.$modal.confirm('单号为"' + purchaseIds + '"的采购单，确认是否生成入库单？').then(() => {
        const rowData=row;
        if (rowData.purchaseId != null) {
          addObjectStorageByPurchaseId(rowData.purchaseId).then(response => {
              if(response.code===200){
                this.getList()
                this.$modal.msgSuccess("出帐流水生成成功")
                this.open = false
              }
          });
        }
      }).catch(() => {})
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.tPurchaseDetailList = this.tPurchaseDetailList
          this.form.amount=0.00;
          if(this.tPurchaseDetailList.length>0){
            this.tPurchaseDetailList.map(item=>{
              this.form.amount=this.form.amount+parseFloat(item.amount);
            })
          }
          if (this.form.purchaseId != null) {
            updatePurchase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addPurchase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const purchaseIds = row.purchaseId || this.ids
      this.$modal.confirm('是否确认删除采购单编号为"' + purchaseIds + '"的数据项？').then(function() {
        return delPurchase(purchaseIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
	/** 采购单详情序号 */
    rowTPurchaseDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 采购单详情添加按钮操作 */
    handleAddTPurchaseDetail() {
      let obj = {}
      obj.resType = ""
      obj.materialId = ""
      obj.materialName = ""
      obj.partsId = ""
      obj.partsName = ""
      obj.partsType = ""
      obj.price = ""
      obj.comment = ""
      obj.images = ""
      obj.num=1
      obj.amount =1.00
      obj.currencyType = "GEL"
      this.tPurchaseDetailList.push(obj)
    },
    /** 采购单详情删除按钮操作 */
    handleDeleteTPurchaseDetail() {
      if (this.checkedTPurchaseDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的采购单详情数据")
      } else {
        const tPurchaseDetailList = this.tPurchaseDetailList
        const checkedTPurchaseDetail = this.checkedTPurchaseDetail
        this.tPurchaseDetailList = tPurchaseDetailList.filter(function(item) {
          return checkedTPurchaseDetail.indexOf(item.index) == -1
        })
      }
    },
    /** 复选框选中数据 */
    handleTPurchaseDetailSelectionChange(selection) {
      this.checkedTPurchaseDetail = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/purchase/export', {
        ...this.queryParams
      }, `purchase_${new Date().getTime()}.xlsx`)
    },
    cleanData(data){
      console.log(data)
      this.queryList=[]
    },
    handleWorkOrder(serviceId){
      if(isValidString(serviceId)){
        getWorkorder(serviceId).then(response => {
          this.tPurchaseDetailList = response.data.tWorkPartsDetailList
        });
      }
      this.queryList=[]
    },
    handleResRequest(resRequestId){
      if(isValidString(resRequestId)){
        getRequest(resRequestId).then(response => {
          this.tPurchaseDetailList = response.data.tResRequestDetailList

        });
      }
      this.queryList=[]
    },
    /* 查找工单信息 */
    querySearchWorkOrder(queryString) {
      if (queryString === '') {
        this.queryList = []; // 输入为空时清空历史数据
        return;
      }
      if(queryString!==""){
        listWorkorder({"serviceId":queryString }).then(response => {
          if(response.rows.length>0){
            this.queryList = response.rows.map(item=>{
              return {"value":item.title,"id":item.serviceId};
            });
          }
        })
      }
    },
    /* 查找申请单信息 */
    querySearchResRequest(queryString) {
      if (queryString === '') {
        this.queryList = []; // 输入为空时清空历史数据
        return;
      }
      if(queryString!==""){
        listRequest({"resRequestId":queryString }).then(response => {
          if(response.rows.length>0){
            this.queryList = response.rows.map(item=>{
              return {"value":item.title,"id":item.resRequestId}
            });
          }
        })
      }
    },
    handleSelectMaterial(currentValue, select) {
      if (currentValue !== ""&&this.queryListMaterial!=null) {
        const foundItem = this.queryListMaterial.find(item => item.value === currentValue);
        if (foundItem) {
          select.materialId = foundItem.id;
        }
      }
    },
    /* 查找物料信息 */
    querySearchMaterial(queryString) {
      if (queryString === '') {
        this.queryListMaterial = []; // 输入为空时清空历史数据
        return;
      }
      if(queryString!=='') {
        listMaterial({ "materialName": queryString }).then(response => {
          if(response.rows.length>0){
            this.queryListMaterial = response.rows.map(item=>{
              return {
                value: item.materialName,
                num: item.num,
                id: String(item.materialId)
              }
            });
          }
        })
      }
    },
    handleSelectParts(value, row) {
      row.partsId=value;
      row.partsName=this.restParts[value].partsName;
      row.norm=this.restParts[value].norm;
      row.price=this.restParts[value].price;
      this.countAmount(row);
    },
    /* 查找配件信息 */
    querySearchParts(queryString) {
      if(isValidString(queryString)) {
        this.restParts={};
        listPartsJoinPurchase({ "partsName": queryString,"serviceId":this.form.serviceId }).then(response => {
          if (response.rows.length > 0) {
            this.queryListParts = response.rows.map(item => {
              this.restParts[item.partsId]=item;
              return {
                value: item.partsName,
                id: String(item.partsId),
                partsType: item.partsType,
                num: item.num,
              }
            });
          }
        })
      }else {
        this.queryListParts = []; // 输入为空时清空历史数据
      }
    },
  //统计价格
    countAmount(row){
      if(isValidString(row.num)&&isValidString(row.price)){
        row.amount=row.num*row.price;
        let totalAmount=0;
        this.tPurchaseDetailList.map(item=>{
          totalAmount=totalAmount+item.amount;
        })
        this.form.amount=totalAmount;
      }
    }
  }
}
</script>
