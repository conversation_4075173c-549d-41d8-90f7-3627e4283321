<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('关联单号')" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          :placeholder="strConvert('请输入关联单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('流水号')" prop="flowId">
        <el-input
          v-model="queryParams.flowId"
          :placeholder="strConvert('请输入流水号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('变动类型')" prop="flowType">
        <el-select v-model="queryParams.flowType" :placeholder="strConvert('请选择变动类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_flow_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('帐变类型')" prop="accountType">
        <el-select v-model="queryParams.accountType" :placeholder="strConvert('请选择帐变类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_account_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('单据类型')" prop="orderType">
        <el-select v-model="queryParams.orderType" :placeholder="strConvert('请选择单据类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_order_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
<!--      <el-form-item :label="strConvert('用户编号')" prop="userId">
        <el-input
          v-model="queryParams.userId"
          :placeholder="strConvert('请输入用户编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item :label="strConvert('用户名')" prop="userName">
        <el-input
          v-model="queryParams.userName"
          :placeholder="strConvert('请输入用户名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">
        <el-button
          type="primary"

          icon="el-icon-plus"
          size="medium"
          @click="handleAdd"
          v-hasPermi="['manage:flow:add']"
        >{{ strConvert('新增流水') }}</el-button>
      </el-col>-->
<!--      <el-col :span="1.5">
        <el-button
          type="success"

          icon="el-icon-edit"

          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:flow:edit']"
        >{{ strConvert('修改') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"

          icon="el-icon-delete"

          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:flow:remove']"
        >{{ strConvert('删除') }}</el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"
          size="medium"
          @click="handleExport"
          v-hasPermi="['manage:flow:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover"  style="font-weight: bold;border: 1px solid #409EFF;padding:10px">
         {{ strConvert('今日金额总计') }}： {{ todayAmount.toFixed(2) }} GEL
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card shadow="hover" style="font-weight: bold;border: 1px solid #f5dab1;padding:10px">
          {{ strConvert('月度金额总计') }}： {{ monthAmount.toFixed(2) }} GEL
        </el-card>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="flowList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('流水号')" align="center" width="120"  prop="flowId" />
      <el-table-column :label="strConvert('变动类型')" align="center" width="100"  prop="flowType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_flow_type" :value="scope.row.flowType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('帐变类型')" width="120"  align="center" prop="accountType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_account_type" :value="scope.row.accountType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('金额')" width="100" align="center" prop="amount" />
      <el-table-column :label="strConvert('关联单号')" width="180" align="center" prop="orderId" />
      <el-table-column :label="strConvert('单据类型')" width="120"  align="center" prop="orderType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_order_type" :value="scope.row.orderType"/>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('用户编号')" align="center" prop="userId" />-->
      <el-table-column :label="strConvert('用户名')" width="70"  align="center" prop="userName" />
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('备注')" width="250" align="center" prop="remark" />
<!--      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="medium"
            type="danger"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['manage:flow:edit']"
          >{{ strConvert('作废') }}</el-button>
&lt;!&ndash;          <el-button

            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['manage:flow:remove']"
          >{{ strConvert('删除') }}</el-button>&ndash;&gt;
        </template>
      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资金流水对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="strConvert('变动类型')" prop="flowType">
          <el-radio-group v-model="form.flowType">
            <el-radio
              v-for="dict in dict.type.t_flow_type"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="strConvert('帐变类型')" prop="accountType">
          <el-select v-model="form.accountType" :placeholder="strConvert('请选择帐变类型')">
            <el-option
              v-for="dict in dict.type.t_account_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="strConvert('金额')" prop="amount">
          <el-input v-model="form.amount" :placeholder="strConvert('请输入金额')" />
        </el-form-item>
        <el-form-item :label="strConvert('关联单号')" prop="orderId">
          <el-input v-model="form.orderId" :placeholder="strConvert('请输入关联单号')" />
        </el-form-item>
        <el-form-item :label="strConvert('单据类型')" prop="orderType">
          <el-select v-model="form.orderType" :placeholder="strConvert('请选择单据类型')">
            <el-option
              v-for="dict in dict.type.t_order_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item :label="strConvert('用户编号')" prop="userId">
          <el-input v-model="form.userId" :placeholder="strConvert('请输入用户编号')" />
        </el-form-item>-->
        <el-form-item :label="strConvert('用户名')" prop="userName">
          <el-input v-model="form.userName" :placeholder="strConvert('请输入用户名')" />
        </el-form-item>
        <el-form-item :label="strConvert('删除标志')" prop="delFlag">
          <el-input v-model="form.delFlag" :placeholder="strConvert('请输入删除标志')" />
        </el-form-item>
        <el-form-item :label="strConvert('备注')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { listFlow, getFlow, delFlow, addFlow, updateFlow,countAmountDayAndMonth } from "@/api/manage/flow"
import { isNumber } from 'pdfmake/src/helpers'

export default {
  name: "Flow",
  dicts: ['t_order_type', 't_flow_type', 't_account_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      //
      todayAmount:0.00,
      monthAmount:0.00,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资金流水表格数据
      flowList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        flowId: null,
        flowType: null,
        accountType: null,
        orderId: null,
        orderType: null,
        userId: null,
        userName: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        flowType: [
          { required: true, message: "变动类型不能为空", trigger: "change" }
        ],
        accountType: [
          { required: true, message: "帐变类型不能为空", trigger: "change" }
        ],
        orderType: [
          { required: true, message: "单据类型不能为空", trigger: "change" }
        ],
        userId: [
          { required: true, message: "用户编号不能为空", trigger: "blur" }
        ],
        userName: [
          { required: true, message: "用户名不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getCountAmountDayAndMonth()
  },
  methods: {
    strConvert,

    /** 统计资金流水 */
    getCountAmountDayAndMonth(){
      countAmountDayAndMonth().then(response => {
        this.todayAmount = response.data.dailyAmount
        this.monthAmount = response.data.monthlyAmount
      })
    },
    /** 查询资金流水列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if(this.queryParams.params.flowId!=null&&!isNumber(this.queryParams.params.flowId)){
        this.$message.error("流水号不能含有字母")
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      listFlow(this.queryParams).then(response => {
        this.flowList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        flowId: null,
        flowType: null,
        accountType: null,
        amount: null,
        orderId: null,
        orderType: null,
        userId: null,
        userName: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.flowId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加资金流水"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const flowId = row.flowId || this.ids
      getFlow(flowId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改资金流水"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.flowId != null) {
            updateFlow(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addFlow(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const flowIds = row.flowId || this.ids
      this.$modal.confirm('是否确认删除资金流水编号为"' + flowIds + '"的数据项？').then(function() {
        return delFlow(flowIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/flow/export', {
        ...this.queryParams
      }, `flow_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
