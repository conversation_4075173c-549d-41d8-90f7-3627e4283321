<template>
  <div class="upload-file">
    <el-upload
      multiple
      :action="uploadFileUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :data="data"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      class="upload-file-uploader"
      ref="fileUpload"
      v-if="!disabled"
    >
      <!-- 上传按钮 -->
      <el-button size="mini" type="primary">选取文件</el-button>
      <!-- 上传提示 -->
      <div class="el-upload__tip" slot="tip" v-if="showTip">
        请上传
        <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
        <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
        的文件
        <template v-if="enableImageCompress"> (图片将自动压缩到{{ maxImageSize / 1024 }}KB以内) </template>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li :key="file.url" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :href="`${baseUrl}${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
          <span v-if="file.compressed" class="compressed-tag">(已压缩)</span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger" v-if="!disabled">删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth"
import Sortable from 'sortablejs'

// 支持的图片类型
const SUPPORTED_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'webp', 'gif']

export default {
  name: "FileUpload",
  props: {
    // 值
    value: [String, Object, Array],
    // 上传接口地址
    action: {
      type: String,
      default: "/common/upload"
    },
    // 上传携带的参数
    data: {
      type: Object
    },
    // 数量限制
    limit: {
      type: Number,
      default: 5
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "pdf"]
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true
    },
    // 禁用组件（仅查看文件）
    disabled: {
      type: Boolean,
      default: false
    },
    // 拖动排序
    drag: {
      type: Boolean,
      default: true
    },
    // 是否启用图片压缩
    enableImageCompress: {
      type: Boolean,
      default: true
    },
    // 图片最大大小（KB）
    maxImageSize: {
      type: Number,
      default: 500 // 500KB
    },
    // 图片压缩质量（0-1）
    imageQuality: {
      type: Number,
      default: 0.8
    },
    // 图片最大宽度（像素）
    maxImageWidth: {
      type: Number,
      default: 1920
    },
    // 图片最大高度（像素）
    maxImageHeight: {
      type: Number,
      default: 1080
    }
  },
  data() {
    return {
      number: 0,
      uploadList: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      uploadFileUrl: process.env.VUE_APP_BASE_API + this.action, // 上传文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      isCompressing: false
    }
  },
  mounted() {
    if (this.drag) {
      this.$nextTick(() => {
        const element = document.querySelector('.upload-file-list')
        if (element) {
          Sortable.create(element, {
            ghostClass: 'file-upload-darg',
            onEnd: (evt) => {
              const movedItem = this.fileList.splice(evt.oldIndex, 1)[0]
              this.fileList.splice(evt.newIndex, 0, movedItem)
              this.$emit("input", this.listToString(this.fileList))
            }
          })
        }
      })
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let temp = 1
          // 首先将值转为数组
          const list = Array.isArray(val) ? val : this.value.split(',')
          // 然后将数组转为对象数组
          this.fileList = list.map(item => {
            if (typeof item === "string") {
              item = { name: item, url: item }
            }
            item.uid = item.uid || new Date().getTime() + temp++
            return item
          })
        } else {
          this.fileList = []
          return []
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize)
    },
    // 是否是图片类型
    isImageType() {
      return this.fileType.some(type => SUPPORTED_IMAGE_TYPES.includes(type.toLowerCase()))
    }
  },
  methods: {
    // 检查是否是图片
    isImageFile(file) {
      if (!file) return false
      const fileName = file.name.split('.')
      const fileExt = fileName[fileName.length - 1].toLowerCase()
      return SUPPORTED_IMAGE_TYPES.includes(fileExt)
    },

    // 压缩图片
    async compressImage(file) {
      if (!this.enableImageCompress || !this.isImageFile(file)) {
        return file
      }

      try {
        this.isCompressing = true
        const compressedFile = await this.compressImageFile(file)
        this.isCompressing = false
        return compressedFile
      } catch (error) {
        console.error('图片压缩失败:', error)
        this.isCompressing = false
        return file
      }
    },

    // 实际的图片压缩逻辑
    compressImageFile(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (event) => {
          const img = new Image()
          img.onload = () => {
            // 计算压缩后的尺寸
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')

            let width = img.width
            let height = img.height

            // 如果图片尺寸超过最大尺寸，则按比例缩放
            if (width > this.maxImageWidth || height > this.maxImageHeight) {
              const ratio = Math.min(
                this.maxImageWidth / width,
                this.maxImageHeight / height
              )
              width = width * ratio
              height = height * ratio
            }

            canvas.width = width
            canvas.height = height

            // 绘制图片到canvas
            ctx.drawImage(img, 0, 0, width, height)

            // 转换为Blob
            canvas.toBlob((blob) => {
              if (!blob) {
                reject(new Error('图片压缩失败'))
                return
              }

              // 检查压缩后的大小
              if (blob.size <= this.maxImageSize * 1024) {
                const compressedFile = new File([blob], file.name, {
                  type: blob.type,
                  lastModified: Date.now()
                })
                compressedFile.compressed = true
                resolve(compressedFile)
              } else {
                // 如果仍然太大，降低质量再次压缩
                let quality = this.imageQuality
                let attempts = 0
                const maxAttempts = 5

                const tryCompress = () => {
                  canvas.toBlob((smallerBlob) => {
                    if (!smallerBlob) {
                      reject(new Error('图片压缩失败'))
                      return
                    }

                    if (smallerBlob.size <= this.maxImageSize * 1024 || attempts >= maxAttempts) {
                      const compressedFile = new File([smallerBlob], file.name, {
                        type: smallerBlob.type,
                        lastModified: Date.now()
                      })
                      compressedFile.compressed = true
                      resolve(compressedFile)
                    } else {
                      quality = Math.max(0.1, quality - 0.1)
                      attempts++
                      setTimeout(tryCompress, 0)
                    }
                  }, file.type, quality)
                }

                tryCompress()
              }
            }, file.type, this.imageQuality)
          }
          img.onerror = () => reject(new Error('图片加载失败'))
          img.src = event.target.result
        }
        reader.onerror = () => reject(new Error('文件读取失败'))
        reader.readAsDataURL(file)
      })
    },

    // 上传前校检格式和大小
    async handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        const fileName = file.name.split('.')
        const fileExt = fileName[fileName.length - 1].toLowerCase()
        const isTypeOk = this.fileType.some(type => type.toLowerCase() === fileExt)
        if (!isTypeOk) {
          this.$modal.msgError(`文件格式不正确，请上传${this.fileType.join("/")}格式文件!`)
          return false
        }
      }

      // 校检文件名是否包含特殊字符
      if (file.name.includes(',')) {
        this.$modal.msgError('文件名不正确，不能包含英文逗号!')
        return false
      }

      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize
        if (!isLt) {
          this.$modal.msgError(`上传文件大小不能超过 ${this.fileSize} MB!`)
          return false
        }
      }

      // 如果是图片且需要压缩
      if (this.enableImageCompress && this.isImageFile(file) && file.size > this.maxImageSize * 1024) {
        try {
          this.$modal.loading("正在压缩图片，请稍候...")
          const compressedFile = await this.compressImage(file)
          this.$modal.closeLoading()

          if (compressedFile.compressed) {
            this.$modal.msgSuccess(`图片已从 ${(file.size / 1024).toFixed(2)}KB 压缩到 ${(compressedFile.size / 1024).toFixed(2)}KB`)
          }

          // 替换原始文件
          Object.defineProperty(compressedFile, 'name', {
            value: file.name,
            writable: false
          })

          this.$modal.loading("正在上传文件，请稍候...")
          this.number++
          return compressedFile
        } catch (error) {
          this.$modal.closeLoading()
          this.$modal.msgError("图片压缩失败，将上传原图")
          console.error(error)
        }
      }

      this.$modal.loading("正在上传文件，请稍候...")
      this.number++
      return true
    },

    // 文件个数超出
    handleExceed() {
      this.$modal.msgError(`上传文件数量不能超过 ${this.limit} 个!`)
    },

    // 上传失败
    handleUploadError(err) {
      this.$modal.msgError("上传文件失败，请重试")
      this.$modal.closeLoading()
      this.number--
    },

    // 上传成功回调
    handleUploadSuccess(res, file) {
      if (res.code === 200) {
        const uploadedFile = {
          name: res.fileName,
          url: res.fileName,
          compressed: file.compressed || false
        }
        this.uploadList.push(uploadedFile)
        this.uploadedSuccessfully()
      } else {
        this.number--
        this.$modal.closeLoading()
        this.$modal.msgError(res.msg)
        this.$refs.fileUpload.handleRemove(file)
        this.uploadedSuccessfully()
      }
    },

    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1)
      this.$emit("input", this.listToString(this.fileList))
    },

    // 上传结束处理
    uploadedSuccessfully() {
      if (this.number > 0 && this.uploadList.length === this.number) {
        this.fileList = this.fileList.concat(this.uploadList)
        this.uploadList = []
        this.number = 0
        this.$emit("input", this.listToString(this.fileList))
        this.$modal.closeLoading()
      }
    },

    // 获取文件名称
    getFileName(name) {
      // 如果是url那么取最后的名字 如果不是直接返回
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1)
      } else {
        return name
      }
    },

    // 对象转成指定字符串分隔
    listToString(list, separator) {
      let strs = ""
      separator = separator || ","
      for (let i in list) {
        strs += list[i].url + separator
      }
      return strs != '' ? strs.substr(0, strs.length - 1) : ''
    }
  }
}
</script>

<style scoped lang="scss">
.file-upload-darg {
  opacity: 0.5;
  background: #c8ebfb;
}

.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}

.compressed-tag {
  font-size: 12px;
  color: #67c23a;
  margin-left: 5px;
}
</style>
