<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item :label="strConvert('入库单号')" prop="objectStorageId">
        <el-input
          v-model="queryParams.objectStorageId"
          :placeholder="strConvert('请输入入库单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建者')" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          :placeholder="strConvert('请输入创建者')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="strConvert('物件类型')" prop="resType">
        <el-select v-model="queryParams.resType" :placeholder="strConvert('请选择物件类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_res_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('采购单号')" prop="objectStorageId">
        <el-input
          v-model="queryParams.purchaseId"
          :placeholder="strConvert('请输入采购单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="info"
          :disabled="single"
          icon="el-icon-s-check"
          @click="goPrint"
        >{{ strConvert('入库单打印') }}</el-button>
      </el-col>
<!--      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"
          size="medium"
          @click="handleExport"
          v-hasPermi="['manage:objectStorage:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>-->
      <el-col :span="4" :offset="1">
       <div style="color: red">{{strConvert("采购价同步至配件库")}}: <el-switch v-model="isPriceConfigData"  @change="handleStatusChange()"></el-switch></div>
      </el-col>
      <el-col :span="11">
        <span style="color: red;font-size: 14px">1、{{strConvert("开启开关后，同步库存时，默认会将采购单采购价同步配件管理中的相应的“参考采购价”")}} <br>2、{{strConvert("同步库存时，会根据入库单中相应的明细对库存进行增加")}}</span>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="purchaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('入库单号')" width="150" align="center" prop="objectStorageId" />
      <el-table-column :label="strConvert('物件类型')" align="center" prop="resType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_res_type" :value="scope.row.resType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('采购单号')" align="center" prop="purchaseId" />
      <el-table-column :label="strConvert('总费用')" align="center" prop="amount" />
      <el-table-column :label="strConvert('同步库存')" align="center" prop="isStore">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_is_store" :value="scope.row.isStore"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('创建者')" align="center" prop="createBy" />
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div v-if="scope.row.isStore=='0'">
            <el-button
              v-if="scope.row.amount>0"
              type="warning"
              icon="el-icon-edit"
              @click="handleUpdateStore(scope.row)"
              v-hasPermi="['manage:objectStorage:sync']"
            >{{ strConvert('同步库存') }}</el-button>
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['manage:objectStorage:edit']"
            >{{ strConvert('添加备注') }}</el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['manage:objectStorage:remove']"
            >{{ strConvert('删除') }}</el-button>
          </div>
          <el-button
            type="text"
            icon="el-icon-s-opportunity"
            @click="handleInfo(scope.row)"
          >{{ strConvert('查看') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改入库单对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('物件类型')" prop="resType">
              <el-select v-model="form.resType"
                         disabled
                         :placeholder="strConvert('请选择物件类型')">
                <el-option
                  v-for="dict in dict.type.t_res_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item :label="strConvert('采购单号')" prop="purchaseId">
              <el-select
                disabled
                style="width: 200px"
                v-model="form.purchaseId"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入采购单号')"
                :remote-method="querySearchPurchase"
                @change="handlePurchase"
                :loading="loading">
                <el-option
                  v-for="item in queryList"
                  :key="item.id"
                  :label="item.id"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="18">
            <el-form-item :label="strConvert('描述')" prop="comment">
              <el-input v-model="form.comment" rows="6" type="textarea" :placeholder="strConvert('请输入内容')" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">入库单详情信息</el-divider>
<!--        <el-row v-if="form.isStore!=='1'" :gutter="10" class="mb8">
          <el-col :span="1.5" >
            <el-button type="primary" icon="el-icon-plus"  @click="handleAddTObjectStorageDetail">{{ strConvert('添加') }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete"  @click="handleDeleteTObjectStorageDetail">{{ strConvert('删除') }}</el-button>
          </el-col>
        </el-row>-->
        <el-table :data="tObjectStorageDetailList" :row-class-name="rowTObjectStorageDetailIndex" @selection-change="handleTObjectStorageDetailSelectionChange" ref="tObjectStorageDetail">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column :label="strConvert('序号')" align="center" prop="index" width="50"/>
          <el-table-column v-if="form.resType === '1'" :label="strConvert('物料编号')" prop="materialId" width="150"/>
          <el-table-column v-if="form.resType === '2'" :label="strConvert('配件编号')" prop="partsId" width="150"/>
          <el-table-column v-if="form.resType === '1'" :label="strConvert('物料名称')" prop="materialName" >
            <template slot-scope="scope">
              <el-select
                disabled
                style="width: 300px"
                v-model="scope.row.materialName"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入物料名称')"
                :remote-method="querySearchMaterial"
                @change="cleanData"
                @blur="() => handleSelectMaterial(scope.row.materialName, scope.row)"
                :loading="loading">
                <el-option
                  v-for="item in queryListMaterial"
                  :key="item.value"
                  :style="item.num>0?'':'color: red'"
                  :label="item.value+' 库存'+item.num"
                  :value="item.value">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column v-if="form.resType === '2'" :label="strConvert('配件名称')" prop="partsName" >
            <template slot-scope="scope">
              <el-select
                disabled
                style="width: 300px"
                v-model="scope.row.partsName"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入配件名称')"
                :remote-method="querySearchParts"
                @change="cleanData"
                @blur="() => handleSelectParts(scope.row.partsName, scope.row)"
                :loading="loading">
                <el-option
                  v-for="item in queryListParts"
                  :key="item.value"
                  :style="item.num>0?'':'color: red'"
                  :label="item.value+' 库存'+item.num"
                  :value="item.value">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column  :label="strConvert('数量')" prop="num" width="100">
            <template slot-scope="scope">
              <el-input disabled v-model="scope.row.num" :placeholder="strConvert('请输入数量')"  type="number" min="1" />
            </template>
          </el-table-column>
          <el-table-column :label="strConvert('说明')" prop="comment" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.comment" rows="2" type="textarea" :placeholder="strConvert('请输入说明')" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <!-- 库存已同步的单据不得再次修改 -->
        <el-button v-if="isEdit" type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>

    <input-store-receipt
      :OrderDetailList="tObjectStorageDetailList"
      :FormData="form"
      :OpenPrint.sync="printOpen"
    />
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import {
  listObjectStorage,
  getObjectStorage,
  delObjectStorage,
  addObjectStorage,
  updateObjectStorage,
  updateObjectStorageStore, updatePurchaseStore
} from '@/api/manage/objectStorage'
import { listParts } from '@/api/manage/parts'
import { listMaterial } from '@/api/manage/material'
import { getInfo } from '@/api/login'
import { formatDate, getDictDataLabelByValue, isValidString } from '@/utils'
import { parseTime } from '@/utils/usedcar'
import { getPurchase, listPurchase } from '@/api/manage/purchase'
import InputStoreReceipt from '@/views/receipt/inputStoreReceipt.vue'
import { getConfigKey, updateConfig } from '@/api/system/config'
export default {
  name: "ObjectStorage",
  components: { InputStoreReceipt},
  dicts: ['t_approval_status', 't_res_type','t_currency_type','t_is_store'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedTObjectStorageDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 入库单表格数据
      purchaseList: [],
      // 入库单详情表格数据
      tObjectStorageDetailList: [],
      tPurchaseDetailList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        objectStorageId: null,
        title: null,
        resType: null,
        resRequestId: null,
        purchaseId: null,
        approvalStatus: null,
        approvalTime: null,
        createBy: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        resType: [
          { required: true, message: "物件类型不能为空", trigger: "change" }
        ],
      },
      printOpen:false,
      queryList:null,
      queryListParts:null,
      queryListMaterial:null,
      isEdit:false,
      isPriceConfigData:null
    }
  },
  created() {
    this.getList()
    getInfo().then(res => {
      this.user = res.user;
    })
    getConfigKey("isPurchasePrice").then(res => {
      this.isPriceConfigData = JSON.parse(res.msg);
    })
  },
  watch:{

  },
  methods: {
    strConvert,

    parseTime,
    /** 查询入库单列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' !== this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      listObjectStorage(this.queryParams).then(response => {
        this.purchaseList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        objectStorageId: null,
        title: null,
        resType: '2',
        operatorType: null,
        images: null,
        purchaseId: null,
        approvalName: null,
        approvalComments: null,
        isPrice:this.isPriceConfigData,
        approvalStatus: null,
        approvalTime: null,
        isStore:null,
        amount: 0.00,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        comment: null
      }
      this.form.title= formatDate(new Date())+'入库';
      this.tObjectStorageDetailList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.objectStorageId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加入库单"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const objectStorageId = row.objectStorageId || this.ids
      getObjectStorage(objectStorageId).then(response => {
        this.form = response.data
        this.isEdit=true;
        this.tObjectStorageDetailList = response.data.tObjectStorageDetailList
        this.open = true
        this.title = "修改入库单"
      })
    },
    /** 修改按钮操作 */
    handleInfo(row) {
      this.reset()
      const objectStorageId = row.objectStorageId || this.ids
      getObjectStorage(objectStorageId).then(response => {
        this.isEdit=false;
        this.form = response.data
        this.tObjectStorageDetailList = response.data.tObjectStorageDetailList
        this.open = true
        this.title = "查看入库单"
      })
    },
    handleUpdateStore(row){
      const objectStorageIds = row.objectStorageId || this.ids
      this.$modal.confirm('单号为"' + objectStorageIds + '"的入库单，确认是否同步库并生成支出流水？').then(() => {
        const rowData=row;
        if (rowData.objectStorageId != null) {
          getObjectStorage(rowData.objectStorageId).then(response => {
            this.form = response.data
            this.tObjectStorageDetailList = response.data.tObjectStorageDetailList
            if (this.tObjectStorageDetailList != null) {
              rowData.isStore='1';
              rowData.updateBy=this.$store.getters.name;
              rowData.updateTime=formatDate(new Date());
              rowData.isPrice=this.isPriceConfigData;
              updatePurchaseStore(rowData).then(response => {
                if (response.code===200){
                  this.getList()
                  this.$modal.msgSuccess("库存同步成功")
                  this.open = false
                }
              })
            } else {
              this.$modal.msgError("请重新检查入库清单后，确保数据完整后再试")
            }
          });
        }
      }).catch(() => {})
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.tObjectStorageDetailList = this.tObjectStorageDetailList
          this.form.amount=0.00;
          if(this.tObjectStorageDetailList.length>0){
            this.tObjectStorageDetailList.map(item=>{
              this.form.amount=this.form.amount+parseFloat(item.amount);
            })
          }
          if (this.form.objectStorageId != null) {
            updateObjectStorage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addObjectStorage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const objectStorageIds = row.objectStorageId || this.ids
      this.$modal.confirm('是否确认删除入库单编号为"' + objectStorageIds + '"的数据项？').then(function() {
        return delObjectStorage(objectStorageIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
	/** 入库单详情序号 */
    rowTObjectStorageDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 入库单详情添加按钮操作 */
    handleAddTObjectStorageDetail() {
      let obj = {}
      obj.resType = ""
      obj.materialId = ""
      obj.materialName = ""
      obj.partsId = ""
      obj.partsName = ""
      obj.partsType = ""
      obj.price = ""
      obj.comment = ""
      obj.images = ""
      obj.num=1
      obj.amount =1.00
      obj.currencyType = "GEL"
      this.tObjectStorageDetailList.push(obj)
    },
    /** 入库单详情删除按钮操作 */
    handleDeleteTObjectStorageDetail() {
      if (this.checkedTObjectStorageDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的入库单详情数据")
      } else {
        const tObjectStorageDetailList = this.tObjectStorageDetailList
        const checkedTObjectStorageDetail = this.checkedTObjectStorageDetail
        this.tObjectStorageDetailList = tObjectStorageDetailList.filter(function(item) {
          return checkedTObjectStorageDetail.indexOf(item.index) == -1
        })
      }
    },
    /** 复选框选中数据 */
    handleTObjectStorageDetailSelectionChange(selection) {
      this.checkedTObjectStorageDetail = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/objectStorage/export', {
        ...this.queryParams
      }, `objectStorage_${new Date().getTime()}.xlsx`)
    },
    cleanData(data){
      console.log(data)
      this.queryList=[]
    },
    handlePurchase(objectStorageId){
      if(isValidString(objectStorageId)){
        getPurchase(objectStorageId).then(response => {
          this.queryList = response.data.tPurchaseDetailList
        });
      }
      this.queryList=[]
    },

    /* 查找工单信息 */
    querySearchPurchase(queryString) {
      if (queryString === '') {
        this.queryList = []; // 输入为空时清空历史数据
        return;
      }
      if(queryString!==""){
        listPurchase({"purchaseId":queryString }).then(response => {
          if(response.rows.length>0){
            this.queryList = response.rows.map(item=>{
              return {"value":item.title,"id":item.purchaseId};
            });
          }
        })
      }
    },
    handleSelectMaterial(currentValue, select) {
      if (currentValue !== ""&&this.queryListMaterial!=null) {
        const foundItem = this.queryListMaterial.find(item => item.value === currentValue);
        if (foundItem) {
          select.materialId = foundItem.id;
        }
      }
    },
    /* 查找物料信息 */
    querySearchMaterial(queryString) {
      if (queryString === '') {
        this.queryListMaterial = []; // 输入为空时清空历史数据
        return;
      }
      if(queryString!=='') {
        listMaterial({ "materialName": queryString }).then(response => {
          if(response.rows.length>0){
            this.queryListMaterial = response.rows.map(item=>{
              return {
                value: item.materialName,
                num: item.num,
                id: String(item.materialId)
              }
            });
          }
        })
      }
    },
    handleSelectParts(currentValue, select) {
      console.log(currentValue,select)
      if (currentValue !== ""&&this.queryListParts!=null) {
        const foundItem = this.queryListParts.find(item => item.value === currentValue);
        if (foundItem) {
          select.partsId = foundItem.id;
        }
      }
    },
    /* 查找配件信息 */
    querySearchParts(queryString) {
      if (queryString === '') {
        this.queryListParts = []; // 输入为空时清空历史数据
        return;
      }
      if(queryString!=='') {
        listParts({ "partsName": queryString }).then(response => {
          if (response.rows.length > 0) {
            this.queryListParts = response.rows.map(item => {
              return {
                value: item.partsName,
                id: String(item.partsId),
                num: item.num,
                partsType: item.partsType
              }
            });
          }
        })
      }
    },
    // 采购价同步开关
    handleStatusChange() {
      let text = this.isPriceConfigData ? "启用" : "停用"
      this.$modal.confirm('确认要"' + text + '将采购价同步至配件库中吗？').then(()=> {
        return updateConfig({
          configId:100,
          configName:"是否同步采购价",
          configKey:"isPurchasePrice",
          configValue:this.isPriceConfigData
        })
      }).then(() => {
        window.location.reload();
        this.$modal.msgSuccess(text + "成功")
      }).catch(function() {
        this.isPriceConfigData = !this.isPriceConfigData
      })
    },
    //打印单据
    goPrint(row){
      const objectStoreId = row.objectStorageId || this.ids
      getObjectStorage(objectStoreId).then(response => {
        this.form = response.data;
        if(response.data.tObjectStorageDetailList!==undefined){
          this.tObjectStorageDetailList = response.data.tObjectStorageDetailList
        }
        this.printOpen= true
      })
    },
  }
}
</script>
