## 开发

```bash
# 进入项目目录
cd admin-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 表格组件迁移说明

项目已从 Luckysheet 迁移到 Univer 表格框架：

- **新组件**: `UniversalSpreadsheetUniver.vue` 替代了原有的 `UniversalSpreadsheet.vue`
- **兼容性**: 保持了与原有数据格式的向后兼容
- **功能增强**: 支持更好的性能和现代化的API
- **迁移文档**: 详见 `docs/Luckysheet_to_Univer_Migration_Plan.md`

## 打包优化说明

### 问题背景
项目引入 Univer 表格框架后，打包体积显著增大（约 123MB），主要原因：

- **Univer 库体积大**: 核心库和相关依赖约占 18.62MB
- **第三方库集中**: ElementUI、其他工具库未分离打包
- **代码分割未启用**: 所有代码打包在单一文件中

### 优化方案
已启用 webpack 代码分割优化，实现：

- **按需加载**: 将 Univer 库分离为独立 chunk，仅在使用时加载
- **缓存优化**: 第三方库独立打包，业务代码更新不影响库文件缓存
- **并行加载**: 多个 chunk 文件可并行下载，提升加载速度

### 性能提升
- **首屏加载**: 预期减少 30-50% 加载时间
- **缓存命中率**: 预期提升 60-80%
- **用户体验**: 显著改善大型表格应用的启动速度

详细优化报告见: [CODE_SPLITTING_OPTIMIZATION.md](./CODE_SPLITTING_OPTIMIZATION.md)

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```