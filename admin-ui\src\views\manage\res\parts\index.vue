<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('配件编号')" prop="partsId">
        <el-input
          v-model="queryParams.partsId"
          :placeholder="strConvert('请输入配件编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('配件名称')" prop="partsName">
        <el-input
          v-model="queryParams.partsName"
          :placeholder="strConvert('请输入配件名称')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('配件部位')" prop="partsPosition">
        <el-select v-model="queryParams.partsPosition"
                   clearable
                   @change="loadPartsTypeData(queryParams)"
                   :placeholder="strConvert('请选择')">
          <el-option
            v-for="dict in dict.type.t_parts_position"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
        <el-form-item :label="strConvert('配件类型')" prop="partsType">
          <el-select
            :disabled="disabledPartsType"
            clearable
            v-model="queryParams.partsType"
              @change="setPartsTypName(queryParams)" :placeholder="strConvert('请选择')">
            <el-option
              v-for="dict in partsTypeList"
              :key="dict.partsTypeId"
              :label="dict.partsTypeId+' '+dict.partsTypeName"
              :value="dict.partsTypeId"
            ></el-option>
          </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('预警库存')" prop="num">
        <el-select v-model="queryParams.num" clearable @change="loadPartsTypeData(queryParams)"
                   :placeholder="strConvert('请选择')">
          <el-option value='5' >{{ '预警<5' }}</el-option>
          <el-option value='0' >{{ '危险<=0' }}</el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('售价比')" prop="sellingPriceRatio">
        <el-input
          v-model="queryParams.sellingPriceRatio"
          :placeholder="strConvert('请输入售价百分比')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item :label="strConvert('库存数量')" prop="num">
        <el-input
          v-model="queryParams.num"
          :placeholder="strConvert('请输入库存数量')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          @click="addRow"
          v-hasPermi="['manage:parts:add']"
        >{{ strConvert('新增') }}</el-button>
      </el-col>
      <el-col :span="1.5">
          <el-button
            type="primary"
            :disabled="addShow"
            @click="batchSubmitForm"
          v-hasPermi="['manage:parts:edit']"
        >{{ strConvert('批量保存') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:parts:remove']"
        >{{ strConvert('批量删除') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"

          @click="handleExport"
          v-hasPermi="['manage:parts:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="partsList"
              :row-class-name="tableRowClassName"
              @selection-change="handleSelectionChange">
      <el-table-column  type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('配件编号')" width="120" align="center" prop="partsId" />
      <el-table-column :label="strConvert('配件部位')" width="120" align="center" prop="partsPosition">
        <template slot-scope="scope">
          <el-select v-if="scope.row.isEditing"
                     v-model="scope.row.partsPosition"
                     @change="loadPartsTypeData(scope.row)"
                     :placeholder="strConvert('请选择')">
            <el-option
              v-for="dict in dict.type.t_parts_position"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
          <dict-tag  v-else :options="dict.type.t_parts_position" :value="scope.row.partsPosition"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('类型编码')"  align="center" prop="partsType" width="100">
        <template slot-scope="scope">
          <el-select v-if="scope.row.isEditing"
                     v-model="scope.row.partsType"
                     :disabled="disabledPartsType"
                     clearable
                     @change="setPartsTypName(scope.row)"
                     :placeholder="strConvert('请选择')">
            <el-option
              v-for="dict in partsTypeList"
              :key="dict.partsTypeId"
              :label="dict.partsTypeId+' '+dict.partsTypeName"
              :value="dict.partsTypeId"
            ></el-option>
          </el-select>
          <span v-else >
            {{scope.row.partsType}}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('类型名称')" align="center" prop="partsTypeName" width="100"/>
      <el-table-column :label="strConvert('配件名称')" align="center" prop="partsName" width="190">
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing"
                    v-model="scope.row.partsName"
                    tabindex="0"
          /><!--tabindex="0"  输入框可聚焦 -->
          <span v-else>{{ scope.row.partsName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('配件规格')" align="center" width="120" prop="norm" >
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing"
                    v-model="scope.row.norm"
                    tabindex="0"
          />
          <span v-else>{{ scope.row.norm }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('配件单位')" align="center" width="120" prop="unit" >
        <template slot-scope="scope">
          <el-select v-if="scope.row.isEditing"
                     v-model="scope.row.unit"
                     :placeholder="strConvert('请选择')">
            <el-option
              v-for="dict in dict.type.t_parts_unit"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
          <dict-tag  v-else :options="dict.type.t_parts_unit" :value="scope.row.unit"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('库存数量')" align="center" width="90" prop="num" >
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing"  v-model="scope.row.num" type="number" min="1"
                    tabindex="0"
          /><!--tabindex="0"  输入框可聚焦 -->
          <span v-else>{{ scope.row.num }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('售价比')+'%'" align="center" width="100" prop="sellingPriceRatio" >
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing"  v-model="scope.row.sellingPriceRatio" type="number" min="0.01"
                    tabindex="0"
          />
          <span v-else>{{ scope.row.sellingPriceRatio }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('参考采购价')" align="center" width="100" prop="price" >
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing"  v-model="scope.row.price" type="number" min="1"
                    tabindex="0"
          />
          <span v-else>{{ scope.row.price }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('描述')" align="center" prop="comment" >
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing" type="textarea" v-model="scope.row.comment"
                    tabindex="0"
          /><!--tabindex="0"  输入框可聚焦 -->
          <span v-else>{{ scope.row.comment }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('备注')" align="center" prop="remark" >
        <template slot-scope="scope">
          <el-input v-if="scope.row.isEditing" type="textarea" v-model="scope.row.remark" />
          <span v-else>{{ scope.row.remark }}</span>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width" width="160">
        <template slot-scope="scope">
          <template v-if="scope.row.isEditing">
            <el-button size="small"  type="primary" @click="saveRow(scope.$index, scope.row)"  tabindex="-1"
            ><!-- 按钮不可聚焦 -->
              保存
            </el-button>
            <el-button size="small"   @click="cancelEdit(scope.$index, scope.row)" tabindex="-1"
            ><!-- 按钮不可聚焦 -->
              取消
            </el-button>
          </template>
          <template v-else>
            <el-button size="small"  v-hasPermi="['manage:parts:edit']" @click="editRowFn(scope.$index, scope.row)">{{ strConvert('编辑') }}</el-button>
            <el-button size="small"  v-hasPermi="['manage:parts:edit']" type="danger" @click="handleDelete(scope.row)">{{ strConvert('删除') }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

<!--    &lt;!&ndash; 添加或修改配件对话框 &ndash;&gt;
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="strConvert('配件名称')" prop="partsName">
          <el-input v-model="form.partsName" :placeholder="strConvert('请输入配件名称')" />
        </el-form-item>
        <el-form-item :label="strConvert('配件类型')" prop="partsType">
          <el-select v-model="form.partsType" :placeholder="strConvert('请选择配件类型')">
            <el-option
              v-for="dict in dict.type.t_parts_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="strConvert('描述')" prop="comment">
          <el-input v-model="form.comment" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
        <el-form-item :label="strConvert('图像集')" prop="images">
          <el-input v-model="form.images" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
        <el-form-item :label="strConvert('库存数量')" prop="num">
          <el-input v-model="form.num" :placeholder="strConvert('请输入库存数量')" />
        </el-form-item>
        <el-form-item :label="strConvert('排序')" prop="sort">
          <el-input v-model="form.sort" :placeholder="strConvert('请输入排序')" />
        </el-form-item>
        <el-form-item :label="strConvert('备注')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>-->
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { listParts, batchAddParts, delParts, addParts, updateParts } from "@/api/manage/parts"
import { getPartsTypeByPartsPosition } from '@/api/manage/partsType'
import { isValidString } from '@/utils'

export default {
  name: "Parts",
  dicts: ['t_parts_position','t_parts_unit'],
  data() {
    return {
      // 遮罩层
      loading: true,
      disabledPartsType:true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 配件表格数据
      partsList: [],
      // 配件类型数据
      partsTypeList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        partsId: null,
        partsName: null,
        sellingPriceRatio:null,
        partsPosition:null,
        partsType: null,
        num: null,
      },
      editIndex: null, // 当前编辑行索引
      editRow: {},     // 编辑中的行数据
      addShow: true, // 是否为新增
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询配件列表 */
    getList() {
      this.loading = true
      listParts(this.queryParams).then(response => {
        this.partsList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        partsId: null,
        partsName: null,
        partsType: null,
        partsTypeName: null,
        partsPosition:null,
        unit: null,
        norm: null,
        comment: null,
        images: null,
        num: null,
        sort: null,
        sellingPriceRatio: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.partsId)
      this.single = selection.length!==1
      if(this.addShow){
        this.multiple = !selection.length
      }
    },
    loadPartsTypeData(row){
      this.partsTypeList=[];
      row.partsType=null;
      let partsPosition=row.partsPosition;
      getPartsTypeByPartsPosition(partsPosition).then((res)=>{
          if(res.code===200){
            this.partsTypeList=res.data;
            this.disabledPartsType=false;
          }
      })
    },
    setPartsTypName(row){
      console.log(row)
      if(isValidString(row.partsType)){
        let partsType=this.partsTypeList.filter(item=>item.partsTypeId===row.partsType);
        if(partsType.length>0){

          console.log(partsType)
          row.partsTypeName=partsType[0].partsTypeName;
        }
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const partsIds = row.partsId || this.ids
      this.$modal.confirm('是否确认删除配件编号为"' + partsIds + '"的数据项？').then(function() {
        return delParts(partsIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/parts/export', {
        ...this.queryParams
      }, `parts_${new Date().getTime()}.xlsx`)
    },
    /** 新增按钮操作 */
    addRow() {
      this.addShow=false;
      this.partsList.unshift({
        partsName: null,
        partsType: null,
        partsTypeName: null,
        partsPosition:null,
        sellingPriceRatio:null,
        unit: null,
        norm: null,
        comment: null,
        num: 1,
        isEditing: true, // 新增行直接进入编辑状态
        isNew: true      // 标记为新增
      });
    },
    /** 批量提交按钮 */
    batchSubmitForm() {
      var that=this;
      if(this.partsList.length>0){
        var submitData=this.partsList.filter(function(item) {
          return item.isNew!==undefined&&item.isNew&&that.validateData(item)
        })
        if(submitData.length>0){
          batchAddParts(submitData).then(response => {
            this.$modal.msgSuccess("新增成功")
            this.getList()
            this.editIndex = null;
            this.editRow = {};
          })
        }else {
          this.$message.warning('没有要提交的数据');
        }
      }
    },
    /** 单条数据提交按钮 */
    saveRow(index, row) {
      if(this.validateData(row)){
        if (row.isNew) {
        // 新增
        batchAddParts([row]).then(() => {
          this.$message.success('新增成功');
          this.getList();
        });
      } else {
        // 编辑
        updateParts(row).then(() => {
          this.$message.success('修改成功');
          this.getList();
        });
      }
      this.addShow=true;
      this.reBackEdit(index);
      }
    },
    // 校验
    validateData(material){
      if (!material.partsName) {
        this.$message.error('配件名称不能为空');
        return false;
      }else if (!material.num) {
        this.$message.error('数量不能为空');
        return false;
      }else if (!material.partsType) {
        this.$message.error('配件类型不能为空');
        return false;
      }
      return true;
    },
    // 退出编辑状态
    reBackEdit(index){
      this.$set(this.partsList[index], 'isEditing', false);
      delete this.partsList[index].isNew;
      delete this.partsList[index]._backup;
    },
    cancelEdit(index, row) {
      if (row.isNew) {
        // 新增未保存，直接移除
        this.partsList.splice(index, 1);
      } else {
        // 编辑还原
        Object.assign(row, row._backup);
        this.$set(this.partsList[index], 'isEditing', false);
        delete this.partsList[index]._backup;
      }
     /*  this.addShow=true; */
    },
    editRowFn(index, row) {
      // 进入编辑状态，备份原始数据以便取消时还原
      this.$set(this.partsList[index], 'isEditing', true);
      this.$set(this.partsList[index], '_backup', { ...row });
    },
    tableRowClassName({row, rowIndex}) {
      if (row.num<=5) {
        return 'warning-row';
      }else if(row.num<=0) {
        return 'error-row';
      }
      return '';
    }
  }
}
</script>

<style scoped lang="scss">


  ::v-deep .el-table  {
    /* 1. 警示黄色（待处理/需注意） */
    .warning-row {
      background: #FFF8E6;
    }

    /* 2. 成功绿色（已完成/成功状态） */
    .success-row {
      background: #F0F9EB;
    }

    /* 3. 高亮蓝色（当前选中/活跃行） */
    .active-row {
      background: #E6F7FF;
    }

    /* 4. 错误红色（异常/失败行） */
    .error-row {
      background: #FFECE6;
    }

    /* 5. 中性灰色（禁用/次要行） */
    .disabled-row {
      background: #eafdfb;
    }

    /* 6. 柔和紫色（特殊标记行） */
    .mark-row {
      background: #F9F0FF;
    }

    /* 7. 活力橙色（高优先级行） */
    .urgent-row {
      background: #FFF2E6;
    }

    /* 8. 清新青色（新增/更新行） */
    .new-row {
      background: #E6FFFB;
    }

    /* 9. 清新青色（新增/更新行） */
    .nine-row {
      background: #dbd6fd;
    }
    /* 10. 清新青色（新增/更新行） */
    .ten-row {
      background: #faffe6;
    }
    /* 11. 清新青色（新增/更新行） */
    .elen-row {
      background: #e6e6ff;
    }

    .el-table--mini .el-table__cell {
      padding: 5px 0;
    }
}
</style>
