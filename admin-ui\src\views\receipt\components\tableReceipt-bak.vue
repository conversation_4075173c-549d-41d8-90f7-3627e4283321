<template>
  <div class="table-data">
    <el-table
      size="mini"
      :data="OrderQuestionDetailList"
      border
      :summary-method="getSummaries"
      show-summary
      style="width: 100%"
      ref="customTable"
    >
      <el-table-column :label="title" type="index" width="60" align="center" />
      <el-table-column label="服务" prop="questionName" />
      <el-table-column label="费用" prop="amount" align="right"/>
      <el-table-column label="备注" prop="questionComment" />
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'tableReceipt',
  props: {
    OrderQuestionDetailList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: "详情" // 保持您原来的默认值或根据需要修改
    }
  },
  mounted() {
    this.adjustHeaderColspan();
  },
  watch: {
    // 如果 title 或 OrderQuestionDetailList 变化可能导致表头需要重新调整
    // 通常列结构固定时，title 变化需要调整，列表数据变化不直接影响表头 colspan
    title() {
      this.adjustHeaderColspan();
    },
    OrderQuestionDetailList() {
      // 数据变化可能会触发表格重新渲染，也可能需要调整，视情况而定
      // 但 getSummaries 里的 DOM 操作会在数据更新后执行，通常更适合页脚
      // 对于表头，如果只是数据变，结构不变，可能不需要每次都调
      // 为保险起见，或如果发现重绘问题，可以启用
      // this.$nextTick(() => this.adjustHeaderColspan());
    }
  },
  methods: {
    adjustHeaderColspan() {
      this.$nextTick(() => {
        const tableComponent = this.$refs.customTable;
        if (!tableComponent || !tableComponent.$el) return;

        const headerWrapper = tableComponent.$el.querySelector('.el-table__header-wrapper');
        if (headerWrapper) {
          const headerTable = headerWrapper.querySelector('table');
          if (headerTable) {
            const headerRow = headerTable.querySelector('thead tr');
            if (headerRow && headerRow.cells.length >= 2) {
              const firstHeaderCell = headerRow.cells[0];
              const secondHeaderCell = headerRow.cells[1];
              firstHeaderCell.colSpan = 2;
              if (secondHeaderCell) {
                secondHeaderCell.style.display = 'none';
              }
            }
          }
        }
      });
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];

      const totalAmount = data.reduce((sum, item) => {
        return sum + (parseFloat(item.amount) || 0);
      }, 0);

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '费用合计';
          this.$nextTick(() => {
            const tableComponent = this.$refs.customTable;
            if (!tableComponent || !tableComponent.$el) return;
            const footerWrapper = tableComponent.$el.querySelector('.el-table__footer-wrapper');
            if (footerWrapper) {
              const footerTable = footerWrapper.querySelector('table');
              if (footerTable) {
                const cells = footerTable.querySelectorAll('td');
                if (cells.length >= 2) {
                  cells[0].colSpan = 2;
                  if (cells[1]) {
                    cells[1].style.display = 'none';
                  }
                }
              }
            }
          });
        }
        else if (index === 1) {
          sums[index] = '';
        }
        else if (column.property === 'amount') {
          sums[index] = totalAmount.toFixed(2) + ' GEL';
        }
        else {
          sums[index] = '';
        }
      });
      return sums;
    }
  }
}
</script>

<style scoped>
.el-table__footer .el-table__cell {
  background-color: #f5f7fa;
}

.el-table__footer td:first-child {
  text-align: center !important;
  font-weight: bold;
}

.table-data{
  border: 1px solid #1e1e1e;
}
</style>
