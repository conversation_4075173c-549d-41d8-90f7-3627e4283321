{"name": "usedCar", "version": "3.8.9", "description": "EURO维保管理平台", "author": "若依", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": ""}, "dependencies": {"@babel/runtime": "^7.28.4", "@babel/runtime-corejs3": "^7.28.4", "@fortawesome/fontawesome-free": "^7.0.1", "@riophae/vue-treeselect": "0.4.0", "@univerjs/docs": "^0.10.7", "@univerjs/docs-ui": "^0.10.7", "@univerjs/drawing": "^0.10.7", "@univerjs/drawing-ui": "^0.10.7", "@univerjs/preset-sheets-core": "^0.10.7", "@univerjs/presets": "^0.10.7", "@univerjs/sheets-drawing": "^0.10.7", "@univerjs/sheets-drawing-ui": "^0.10.7", "axios": "0.28.1", "clipboard": "2.0.8", "echarts": "5.4.0", "element-ui": "^2.15.14", "file-saver": "2.0.5", "flag-icon-css": "^4.1.7", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.9.2", "jquery": "^3.7.1", "jquery-mousewheel": "^3.2.2", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "jspdf": "^1.5.3", "nprogress": "0.2.0", "pdfmake": "^0.2.20", "pdfmake-cn": "^0.2.11", "print-js": "^1.6.0", "quill": "^1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "splitpanes": "2.4.1", "usedCar": "file:", "v-viewer": "^1.5.1", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-i18n": "^8.28.2", "vue-meta": "2.4.0", "vue-print-nb": "^1.7.5", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@babel/core": "^7.28.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-proposal-private-methods": "^7.18.6", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-runtime": "^7.28.3", "@babel/preset-env": "^7.28.3", "@playwright/test": "^1.55.0", "@vue/cli-plugin-babel": "^5.0.9", "@vue/cli-plugin-eslint": "^5.0.9", "@vue/cli-service": "^5.0.9", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "buffer": "^6.0.3", "chalk": "4.1.0", "compression-webpack-plugin": "6.1.2", "connect": "3.6.6", "core-js": "^3.28.0", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.4", "lint-staged": "10.5.3", "path-browserify": "^1.0.1", "process": "^0.11.10", "sass": "1.32.13", "sass-loader": "10.1.1", "svg-sprite-loader": "5.1.1", "url-loader": "^4.1.1", "util": "^0.12.5", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}