<script >
import { copyValidValues, formatDate, isValidString } from '@/utils'
import { getCustomer, listCustomer } from '@/api/manage/customer'
import { listParts } from '@/api/manage/parts'
import { addWorkorder, getWorkorder, updateWorkorder } from '@/api/manage/workorder'
import CustomizeTable from '@/views/manage/workorder/components/quesitonTable.vue'
import { listQuestion } from '@/api/system/question'
import cache from '@/plugins/cache'
import QuestionTable from '@/views/manage/workorder/components/quesitonTable.vue'
import { getConfigKey, updateConfig } from '@/api/system/config'
import { strConvert, strConvertLangeTitle1, strConvertLangeTitle2 } from '@/i18n/i18nMix'

export default {
  name: "questionForm",
  components: { QuestionTable, CustomizeTable },
  dicts: ['t_work_question_service_type'],
  data() {
    return {
      serviceId: null,
      totalText: "",//统计数据文本
      isMobile: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      T_QUESTION_ORDER_SERVICE_TYPE:[],
      checkedTWorkOrderDetail: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      isUpdateDiscountAmount:false,
      isTaxRate:false,//是否含税结算
      taxRate:0,//税率
      restCustomer: {},
      customerDiscountLevel:'0',//用户折扣
      restQuestion: {},
      // 总条数
      total: 0,
      // 工单详情表格数据
      tWorkOrderDetailList: [],
      // 工单定损详情表格数据
      tWorkOrderQuestionDetailList: [],
      tWorkOrderQuestionIndex: [],
      checkRadioQuestion:'1',
      // 手动添加新服务项
      newQuestionDetailList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      checkboxQuestion:[],
      // 表单参数
      form: {
      },
      // 表单校验
      // 表单校验
      rules: {
        customerNickName: [
          { required: true, message: this.strConvert('客户姓名不能为空'), trigger: "blur" }
        ],
        title: [
          { required: true, message: this.strConvert('标题不能为空'), trigger: "blur" }
        ],
       /*  comment: [
          { required: true, message: "问题描述不能为空", trigger: "blur" }
        ], */
        phonenumber: [
          { required: true, message: this.strConvert('客户电话不能为空'), trigger: "blur" }
        ],
        advancePayment: [
          { required: true, message: this.strConvert('预收款不能为空'), trigger: "blur" }
        ],
        carPlateNumber: [
          { required: true, message: this.strConvert('车牌号不能为空'), trigger: "blur" }
        ],
      },
      queryListParts:null,
      printOpen:false,

    }
  },
  created() {
    this.initData();
    getConfigKey("taxRate").then(res => {
      this.taxRate = parseInt(JSON.parse(res.msg));
    })
  },
  computed:{
    // 过滤掉不需要显示的行
    filterServiceTableData(){
      return this.T_QUESTION_ORDER_SERVICE_TYPE.filter(item => item[item.value]!==undefined&&item[item.value].length>0 );
    },
  },
  mounted() {
  },
  watch:{
    'dict.type.t_work_question_service_type': {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          newVal.map(item=>{
            this.T_QUESTION_ORDER_SERVICE_TYPE.push({
              isShowCount:item.raw.remark,
              value:item.value,
              label:item.label,
              num:0,
              [item.value]:[],
              amount:0
            })
          });
        }
      }
    }
  },
  methods: {
    strConvertLangeTitle1,
    strConvertLangeTitle2,
    strConvert,
    initData(){
      this.reset();
      const fast = cache.session.getJSON('fastFrom')
      console.log(fast);
      if(fast!=null){
        this.form= copyValidValues(this.form, fast)
      }
      this.serviceId = this.$route.params && this.$route.params.serviceId;
      getWorkorder(this.serviceId).then(response => {
        if(response.data!==undefined){
          this.form = response.data;
          if(this.form.taxRate!=null){
            this.isTaxRate=true;
          }
          this.form.totalAmount=0;
          this.checkboxQuestion=[];
          if(response.data.tWorkOrderQuestionDetailList!==undefined){
            this.tWorkOrderQuestionDetailList=response.data.tWorkOrderQuestionDetailList;
            //获取已有工单中客户相关分成比
            getCustomer(this.form.customerId).then(res=>{
              if(res.data!=null){
                this.customerDiscountLevel=res.data.discountLevel;
                this.publicCount(false);
              }
            })
          }
        }
      })
      this.handleAddTWorkOrderDetail();
    },
    // 取消按钮
    cancel() {
      const obj = { path: "/workorder/register" }
      this.$tab.closeOpenPage(obj)
    },

    // 表单重置
    reset() {
      this.form = {
        serviceId:  this.serviceId,
        title: null,
        businessType: "0",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        phonenumber:null,
        comment:null,
        expectedTime:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: formatDate(new Date()),
        updateBy: null,
        updateTime: null,
        remark: null,
        fastRemark:null,
        brand: null,//品牌
        color: null,//颜色
        advancePayment:0,//预收金额
        discountAmount: 0,//折后金额
        totalAmount: 0,//总金额
        balancePayment: 0,//折扣后含税金额
        balance:0,//余额
        mainTotalAmount: 0,//主项合计金额（用于工人提成计算）
      }
      this.form.title= formatDate(new Date())+this.strConvert('受理');
      this.tWorkOrderQuestionDetailList = []
      this.tWorkOrderQuestionDetailList=[]
      this.newQuestionDetailList=[]
      this.checkboxQuestion=["1"];
      this.resetForm("form")
    },
    handleServiceChange(val) {
      this.checkRadioQuestion=val;
      this.newQuestionDetailList=[];
      this.handleAddTWorkOrderDetail();
    },
    handleStatusChange(){
      let text = this.isUpdateDiscountAmount ? this.strConvert('开启') : this.strConvert('关闭')
      this.$modal.confirm(this.strConvert('确认要"') + text + this.strConvert('手动修改折扣金额')).then(()=> {
        return null;
      }).catch(() => {
        this.isUpdateDiscountAmount = !this.isUpdateDiscountAmount
      })
    },
    handleTaxRateChange(){
      let text = this.isTaxRate ? this.strConvert('开启') : this.strConvert('关闭')
      this.$modal.confirm(this.strConvert('确认要"') + text + this.strConvert('含税算计价，如有折扣，建议开启含税前，先进行折扣优惠，否则将影响最终结算')).then(()=> {
        this.publicCount(true);
      }).catch(() => {
        this.isTaxRate = !this.isTaxRate
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.checkboxQuestion.length===0){
            this.$modal.msgError(this.strConvert('至少应选择一项服务'))
            return;
          }
          this.form.tWorkOrderQuestionDetailList = this.tWorkOrderQuestionDetailList
          this.form.tWorkOrderQuestionDetailList = this.tWorkOrderQuestionDetailList
          if (this.form.serviceId != null) {
            updateWorkorder(this.form).then(response => {
              if(response.code===200){
                this.$modal.msgSuccess(this.strConvert('修改成功'))
                this.cancel()
              }else {
                this.$message.error(response.msg);
              }
            })
          } else {
            addWorkorder(this.form).then(response => {
              if(response.code===200){
              this.$modal.msgSuccess(this.strConvert('添加成功'))
              this.cancel()
              }else {
                this.$message.error(response.msg);
              }
            })
          }
        }
      })
    },
    handleSelect(item) {
      this.form.customerId=item.id;
      this.form.customerNickName=this.restCustomer[item.id].customerNickName;
      this.form.phonenumber=this.restCustomer[item.id].phonenumber;
      this.form.carPlateNumber=this.restCustomer[item.id].carPlateNumber;
      this.form.color=this.restCustomer[item.id].color;
      this.form.brand=this.restCustomer[item.id].brand;
      this.customerDiscountLevel=this.restCustomer[item.id].discountLevel;
    },
    /* 查找用户信息 */
    querySearchCustomer(queryString, cb) {
      if(isValidString(queryString)){
        listCustomer({"customerNickName":queryString }).then(response => {
          this.restCustomer = {};
          var results=[];
          if(response.rows.length>0){
            results = response.rows.map(item=>{
              this.restCustomer[item.customerId]=item;
              return {"value":item.customerNickName,"id":item.customerId}
            })
          }
          cb(results);
        })
      }
    },
    handleSelectQuestion(item,row) {
      row.questionId=item.id;
      row.questionName=this.restQuestion[item.id].questionName;
      row.amount=this.restQuestion[item.id].amount;
    },
    /* 查找用户信息 */
    querySearchQuestion(queryString, cb) {
      if(isValidString(queryString)){
        listQuestion({"entryType":"1","questionName":queryString,"questionType":this.checkRadioQuestion }).then(response => {
          this.restQuestion = {};
          var results=[];
          if(response.rows.length>0){
            results = response.rows.map(item=>{
              this.restQuestion[item.questionId]=item;
              return {"value":item.questionName,"id":item.questionId}
            })
          }
          cb(results);
        })
      }
    },
    /** 工单详情序号 */
    rowTWorkOrderDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 工单定损项添加按钮操作 */
    handleAddTWorkOrderDetail() {
      let obj = {}
      obj.questionId = ""
      obj.questionType = this.checkRadioQuestion
      obj.questionName = ""
      obj.questionComment = ""
      obj.amount = 1
      obj.currencyType = "GEL"
      obj.remark = ""
      this.newQuestionDetailList.push(obj)
    },
    /** 工单详情删除按钮操作 */
    handleDeleteTWorkOrderDetail() {
      if (this.checkedTWorkOrderDetail.length === 0) {
        this.$modal.msgError(this.strConvert('请先选择要删除的工单详情数据'))
      } else {
        const newQuestionDetailList = this.newQuestionDetailList
        const checkedTWorkOrderDetail = this.checkedTWorkOrderDetail
        this.newQuestionDetailList = newQuestionDetailList.filter(function(item) {
          return checkedTWorkOrderDetail.indexOf(item.index) === -1
        })
      }
    },
    /** 复选框选中数据 */
    handleTWorkOrderDetailSelectionChange(selection) {
      this.checkedTWorkOrderDetail = selection.map(item => item.index)
    },
    handleAddTWorkOrderPush(){
      for (const item of this.newQuestionDetailList) {
        if (!isValidString(item.questionName) || !isValidString(item.amount)) {
          this.$message.error(this.strConvert('问题信息不完整 请检查后再试'));
          return;
        }
        if(this.tWorkOrderQuestionIndex.indexOf(item.questionName+"_"+item.questionType)!==-1){
          this.$message.error(this.strConvert('添加的项目:') + item.questionName + this.strConvertLangeTitle2());
          return;
        }
      }
      this.newQuestionDetailList.map(item=>{
        this.tWorkOrderQuestionIndex.push(item.questionName+"_"+item.questionType);
        this.T_QUESTION_ORDER_SERVICE_TYPE.map(data=>{
          if(item.questionType===data.value){
            data.num=data.num+1;
            data.amount=parseFloat(data.amount)+parseFloat(item.amount);
            this.checkboxQuestion.push(item.questionType)
            data[data.value].push(item)
            this.tWorkOrderQuestionDetailList.push(item);
            this.form.totalAmount=this.form.totalAmount+parseFloat(item.amount);
          }
        })
      })
      if(this.isTaxRate){
        this.form.totalAmount=this.form.totalAmount+(this.form.totalAmount*(parseFloat(this.taxRate)/100))
        this.form.taxRate=this.taxRate
      }
      this.form.discountAmount=parseFloat(this.form.totalAmount)-(parseFloat(this.form.totalAmount)*(parseFloat(this.customerDiscountLevel)/100));
      this.form.balance=parseFloat(this.form.discountAmount)-parseFloat(this.form.advancePayment);
      // balancePayment存放折扣后含税金额
      if(this.isTaxRate && this.taxRate > 0){
        this.form.balancePayment = parseFloat(this.form.discountAmount) * (1 + parseFloat(this.taxRate) / 100);
      } else {
        this.form.balancePayment = this.form.discountAmount;
      }
      this.computeMainTotalAmount();
      this.newQuestionDetailList=[];
      this.handleAddTWorkOrderDetail();
    },
    resetCountData(questionName){
      //利用回调重置数据
      const index = this.tWorkOrderQuestionDetailList.findIndex(item => item.questionName === questionName);
      if (index !== -1) {
        this.tWorkOrderQuestionDetailList.splice(index, 1);
      }
      this.publicCount(true)
    },
    publicCount(isReset){
      //重置
      this.T_QUESTION_ORDER_SERVICE_TYPE.map(type=>{
          type.num=0;
          type.amount=0.00;
          type[type.value]=[];
      })
      if(isReset){
        this.form.totalAmount=0.00;
        this.form.balance=0.00;
        this.form.balancePayment=0.00;
        // 只有在未开启手动修改折后金额时，才重置discountAmount
        if(!this.isUpdateDiscountAmount){
          this.form.discountAmount=0.00;
        }
      }

      this.checkboxQuestion=[];

      this.tWorkOrderQuestionIndex=[];
      this.tWorkOrderQuestionDetailList.map(item=>{
        this.tWorkOrderQuestionIndex.push(item.questionName+"_"+item.questionType);
        this.T_QUESTION_ORDER_SERVICE_TYPE.map(type=>{
          if(item.questionType===type.value){
            type.num=type.num+1;
            type.amount=parseFloat(type.amount)+parseFloat(item.amount);
            type[type.value].push(item)
            this.checkboxQuestion.push(item.questionType)
            this.form.totalAmount=this.form.totalAmount+parseFloat(item.amount);
          }
        })
      })
      if(isReset){
        if(this.isTaxRate){
          this.form.totalAmount=this.form.totalAmount+(this.form.totalAmount*(parseFloat(this.taxRate)/100))
          this.form.taxRate=this.taxRate
        }
        // 只有在未开启手动修改折后金额时，才重新计算discountAmount
        if(!this.isUpdateDiscountAmount){
          this.form.discountAmount=parseFloat(this.form.totalAmount)-(parseFloat(this.form.totalAmount)*(parseFloat(this.customerDiscountLevel)/100)).toFixed(2);
        }
        // 计算余额：如果开启税率且手动修改了折扣金额，需要用含税的折扣金额计算
        let balanceDiscountAmount = parseFloat(this.form.discountAmount);
        if(this.isTaxRate && this.isUpdateDiscountAmount && this.taxRate > 0){
          // 用户手动输入的是不含税金额，需要加上税款
          balanceDiscountAmount = balanceDiscountAmount * (1 + parseFloat(this.taxRate) / 100);
        }
        this.form.balance=(balanceDiscountAmount-parseFloat(this.form.advancePayment)).toFixed(2);
        // balancePayment存放折扣后含税金额
        if(this.isTaxRate && this.taxRate > 0){
          this.form.balancePayment = parseFloat(this.form.discountAmount) * (1 + parseFloat(this.taxRate) / 100);
        } else {
          this.form.balancePayment = this.form.discountAmount;
        }
      }
      this.computeMainTotalAmount();
    },
    //统计计算主项金额相关信息
    computeMainTotalAmount(){
      let texts=[];
      
      // 计算不含税的折扣金额作为基础
      let baseDiscountAmount = parseFloat(this.form.discountAmount) || 0;
      
      // 如果开启了税率且未手动修改折扣金额，需要从含税的折扣金额中去除税款部分
      if(this.isTaxRate && this.taxRate > 0 && !this.isUpdateDiscountAmount){
        // discountAmount是含税的，需要计算出不含税的金额
        // 含税金额 = 不含税金额 * (1 + 税率/100)
        // 不含税金额 = 含税金额 / (1 + 税率/100)
        baseDiscountAmount = baseDiscountAmount / (1 + parseFloat(this.taxRate) / 100);
      }
      // 如果手动修改了折扣金额，用户输入的就是不含税金额，直接使用
      
      texts.push(" = " + this.strConvert('不含税折扣金额') + "（"+baseDiscountAmount.toFixed(2)+"）");
      
      // 初始化主项合计金额为不含税的折扣金额
      this.form.mainTotalAmount = baseDiscountAmount;
      
      // 减去各项成本（isShowCount不等于"+"的项目）
      this.T_QUESTION_ORDER_SERVICE_TYPE.forEach(question => {
        if(question.isShowCount !== "+" && question.amount > 0){
          this.form.mainTotalAmount = this.form.mainTotalAmount - parseFloat(question.amount);
          texts.push(" - " + this.strConvert('类目') + " "+question.value+"（"+question.amount+"）")
        }
      })
      
      // 确保主项合计金额不为负数
      this.form.mainTotalAmount = Math.max(0, this.form.mainTotalAmount);
      
      this.totalText = texts.join("");
    },
    computeAmount(){
      this.form.balance=parseFloat(this.form.discountAmount)-parseFloat(this.form.advancePayment);
      // balancePayment存放折扣后含税金额
      if(this.isTaxRate && this.taxRate > 0){
        this.form.balancePayment = parseFloat(this.form.discountAmount) * (1 + parseFloat(this.taxRate) / 100);
      } else {
        this.form.balancePayment = this.form.discountAmount;
      }
      this.computeMainTotalAmount();
    },
    isRequired(prop) {
      return this.rules[prop]?.some(rule => rule.required) || false;
    },
    tableRowClassName({row, rowIndex}) {
      if (row.value ==='1'&&row.num>0) {
        return 'warning-row';
      }else if(row.value ==='2'&&row.num>0) {
        return 'success-row';
      }else if(row.value ==='3'&&row.num>0) {
        return 'active-row';
      }else if(row.value ==='4'&&row.num>0) {
        return 'error-row';
      }else if(row.value ==='5'&&row.num>0) {
        return 'disabled-row';
      }else if(row.value ==='6'&&row.num>0) {
        return 'mark-row';
      }else if(row.value ==='7'&&row.num>0) {
        return 'urgent-row';
      }else if(row.value ==='8'&&row.num>0) {
        return 'new-row';
      }else if(row.value ==='9'&&row.num>0) {
        return 'nine-row';
      }else if(row.value ==='10'&&row.num>0) {
        return 'ten-row';
      }else if(row.value ==='11'&&row.num>0) {
        return 'elen-row';
      }
      return '';
    },
  }
}
</script>

<template>
  <div class="app-container">
    <el-form ref="form"  label-position='right' :model="form" :rules="rules"  size="mini" class="compact-form" >
      <el-row :gutter="10" class="mb8">
        <el-col :span="14">
          <!-- 第一行：工单标题和编号 -->
          <el-descriptions :column="2" size="mini"  border>
            <el-descriptions-item >
              <template slot="label">
                <span :class="{'is-required': isRequired('title')}">{{strConvert('工单标题')}}：</span>
              </template>
              <el-form-item prop="title" class="no-margin" label-width="0">
                <el-input v-model="form.title" :placeholder="strConvert('请输入工单标题')" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :label="strConvert('工单编号') + '：'">
              <el-form-item prop="serviceId" class="no-margin" label-width="0">
                <el-input v-model="form.serviceId" disabled />
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 第二行：客户信息 -->
          <el-descriptions  :column="3" border size="mini" class="mt-10">
            <el-descriptions-item >
              <template slot="label">
                <span :class="{'is-required': isRequired('customerNickName')}">{{strConvert('客户姓名')}}：</span>
              </template>
              <el-form-item prop="customerNickName" class="no-margin">
                <el-autocomplete
                  v-model="form.customerNickName"
                  :fetch-suggestions="querySearchCustomer"
                  :placeholder="strConvert('请输入客户姓名')"
                  @select="handleSelect"
                  style="width: 100%"
                ></el-autocomplete>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item >
              <template slot="label">
                <span :class="{'is-required': isRequired('brand')}">{{strConvert('品牌')}}：</span>
              </template>
              <el-form-item prop="brand" class="no-margin">
                <el-input v-model="form.brand" :placeholder="strConvert('请输入品牌')" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item >
              <template slot="label">
                <span :class="{'is-required': isRequired('color')}">{{strConvert('颜色')}}：</span>
              </template>
              <el-form-item prop="color" class="no-margin">
                <el-input v-model="form.color" :placeholder="strConvert('请输入颜色')" />
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 第三行：联系方式和车辆信息 -->
          <el-descriptions :column="2" size="mini" border class="mt-10">
            <el-descriptions-item  >
              <template slot="label">
                <span :class="{'is-required': isRequired('phonenumber')}">{{strConvert('客户电话')}}：</span>
              </template>
              <el-form-item prop="phonenumber" class="no-margin">
                <el-input v-model="form.phonenumber" :placeholder="strConvert('请输入客户电话')" />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item >
              <template slot="label">
                <span :class="{'is-required': isRequired('carPlateNumber')}">{{strConvert('车牌号')}}：</span>
              </template>
              <el-form-item prop="carPlateNumber" class="no-margin">
                <el-input v-model="form.carPlateNumber" :placeholder="strConvert('请输入车牌号')" />
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 第四行：日期信息 -->
          <el-descriptions :column="2" border size="mini" class="mt-10">
            <el-descriptions-item>
              <template slot="label">
                <span :class="{'is-required': isRequired('createTime')}">{{strConvert('进厂日期')}}：</span>
              </template>
              <el-form-item prop="createTime" class="no-margin">
                <el-date-picker
                  v-model="form.createTime"
                  type="date"
                  :placeholder="strConvert('选择日期')"

                />
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item >
              <template slot="label">
                <span :class="{'is-required': isRequired('expectedTime')}">{{strConvert('取车日期')}}：</span>
              </template>
              <el-form-item prop="expectedTime" class="no-margin">
                <el-date-picker
                  v-model="form.expectedTime"
                  type="date"
                  :placeholder="strConvert('选择日期')"

                />
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 第六行：支付信息 -->
          <el-descriptions :column="2" border size="mini" class="mt-10">
            <el-descriptions-item  >
              <template slot="label">
                <span :class="{'is-required': isRequired('advancePayment')}">{{strConvert('预收金额')}}：</span>
              </template>
              <el-form-item prop="advancePayment" class="no-margin">
                <el-input v-model="form.advancePayment" @blur="computeAmount" :placeholder="strConvert('请输入预收金额')">
<!--                  <template slot="append">GEL</template>-->
                </el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item  >
              <template slot="label">
                <span :class="{'is-required': isRequired('balancePayment')}">{{strConvert('折扣后含税金额')}}：</span>
              </template>
              <el-form-item prop="balancePayment" class="no-margin">
                <el-input disabled v-model="form.balancePayment" :placeholder="strConvert('请输入折扣后含税金额')">
<!--                  <template slot="append">GEL</template>-->
                </el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

          <!-- 第五行：金额信息 -->
          <el-descriptions :column="3" border size="mini" class="mt-10">
            <el-descriptions-item :label="strConvert('总额') + '：'">
              <el-form-item prop="totalAmount" class="no-margin">
                <el-input disabled v-model="form.totalAmount">
<!--                  <template slot="append">GEL</template>-->
                </el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :label="strConvert('折后金额') + '：'">
              <el-form-item prop="discountAmount" class="no-margin">
                <el-input :disabled="!isUpdateDiscountAmount" @blur="computeAmount" v-model="form.discountAmount">
<!--                  <template slot="append">GEL</template>-->
                </el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :label="strConvert('余额') + '：'">
              <el-form-item prop="balance" class="no-margin">
                <el-input disabled v-model="form.balance">
<!--                  <template slot="append">GEL</template>-->
                </el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>

            <!-- 第七行：问题描述和图片 -->
          <el-descriptions :column="1" border size="mini" class="mt-10">
            <el-descriptions-item >
              <template slot="label">
                <div style="width: 80px;text-align: right">
                  <span :class="{'is-required': isRequired('comment')}">{{strConvert('问题描述')}}：</span>
                </div>
              </template>
              <el-form-item prop="comment"  class="no-margin">
                <el-input
                  v-model="form.comment"
                  type="textarea"
                  :rows="3"
                  :placeholder="strConvert('请输入问题描述')"
                  style="width:450px"
                />
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="1" border size="mini" class="mt-10">
            <el-descriptions-item >
              <template slot="label">
                <div style="width: 80px;text-align: right">
                  <span :class="{'is-required': isRequired('images')}">{{strConvert('图片上传')}}：</span>
                </div>
              </template>
              <el-form-item prop="images"  class="no-margin"   style="width:450px" >
                <image-upload
                  :enable-image-compress="true"
                  :max-image-size="300"
                  :image-quality="0.7"
                  :max-image-width="800"
                  :max-image-height="800"
                  v-model="form.images"
                  style="text-align: left"
                />
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
          <el-table  size="mini" border :data="[form]"  :show-header="false" style="width: 100%">
            <el-table-column  prop="mainTotalAmount" >
              <template slot-scope="scope">
                <div style="font-weight: bold">{{strConvert('主项合计金额')}}：{{scope.row.mainTotalAmount}}GEL <span >{{totalText}}</span></div>
              </template>
            </el-table-column>
          </el-table>

          <div  style="text-align: center;margin: 8px ">
            <div style="font-size: 12px;line-height: 25px">
              <span>{{strConvert('手动修改折后金额')}}: <el-switch v-model="isUpdateDiscountAmount"  @change="handleStatusChange()"></el-switch></span>
              <span style="margin-left: 25px">{{strConvert('是否附加税款')}}: <el-switch v-model="isTaxRate"  @change="handleTaxRateChange()"></el-switch> {{strConvert('当前税率')}}：{{taxRate}}%</span>
<!--              <el-row>主项合计金额：{{form.mainTotalAmount}} <span style="color: #8c939d">{{totalText}}</span></el-row>-->
            </div>
            <el-button type="primary" size="small" @click="submitForm">{{strConvert('确认提交')}}</el-button>
            <el-button  type="warning" size="small" @click="cancel">{{strConvert('关闭')}}</el-button>
          </div>
        </el-col>
        <el-col :span="10">
          <el-table
            size="mini"
            :data="filterServiceTableData"
            row-key="value"
            :row-class-name="tableRowClassName"
            :expand-row-keys="checkboxQuestion"
            style="width: 100%">
            <el-table-column type="expand">
              <template slot-scope="scope">
                <div class="tableChild">
                  <question-table :table-name="scope.row.label"
                                  :save-order-detail-list="scope.row[scope.row.value]"
                                @resetCountData="resetCountData"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column width="260" :label="strConvert('服务类型')" prop="label">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.t_work_question_service_type" :value="scope.row.value"/>
              </template>
            </el-table-column>
            <el-table-column
              :label="strConvert('服务项')"
              prop="num">
            </el-table-column>
            <el-table-column
              :label="strConvert('合计')"
              prop="amount">
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-divider content-position="center">{{strConvert('定损信息录入')}}</el-divider>
      <el-row :gutter="10" class="mb8">
        <el-radio-group v-model="checkRadioQuestion"  @change="handleServiceChange" size="small">
          <el-radio-button v-for="dict in T_QUESTION_ORDER_SERVICE_TYPE" :key="dict.value" :label="dict.value"> {{ dict.label }}</el-radio-button>
        </el-radio-group>
      </el-row>
      <el-row :gutter="10" class="mb8">

        <el-row :gutter="10" class="mb8" >
          <div class="tableChild"  style="width: 95%; box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);padding: 5px">
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-plus" size="mini"  @click="handleAddTWorkOrderDetail">{{strConvert('添加')}}</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" icon="el-icon-delete" size="mini"  @click="handleDeleteTWorkOrderDetail">{{strConvert('删除')}}</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="warning" icon="el-icon-check" size="mini"  @click="handleAddTWorkOrderPush">{{strConvert('加入')}}</el-button>
              </el-col>
              <el-col :span="18">
                <span style="color: red">{{strConvertLangeTitle1()}}</span>
              </el-col>
            </el-row>
          <el-table v-if="newQuestionDetailList.length>0" :data="newQuestionDetailList"
                    :row-class-name="rowTWorkOrderDetailIndex"
                    @selection-change="handleTWorkOrderDetailSelectionChange" ref="tWorkOrderDetail"
                    row-key="index"
                    :max-height="isMobile ? '250px' : '300px'"
                    border
                    size="mini"
                    style="width: 100%" >
            <el-table-column type="selection" width="50" align="center"  />
            <el-table-column :label="strConvert('编号')" align="center" prop="index"  />
            <el-table-column :label="strConvert('问题编号')" prop="questionId" />
            <el-table-column :label="strConvert('问题名称')" prop="questionName" >
              <template slot-scope="scope">
                <el-autocomplete
                  v-model="scope.row.questionName"
                  :fetch-suggestions="querySearchQuestion"
                  :placeholder="strConvert('请输入问题名称')"
                  @select="(item) => handleSelectQuestion(item, scope.row)"
                  style="width: 100%"
                ></el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column :label="strConvert('费用')" prop="amount" >
              <template slot-scope="scope">
                <el-input v-model="scope.row.amount" type="number"  :min="1"
                          :max="999"
                          size="small"  controls-position="right"
                          style="width: 100%" />
              </template>
            </el-table-column>
            <el-table-column :label="strConvert('描述')" prop="questionComment" >
              <template slot-scope="scope">
                <el-input v-model="scope.row.questionComment"
                          size="small"/>
              </template>
            </el-table-column>
          </el-table>
          </div>
        </el-row>
      </el-row>
    </el-form>
  </div>
</template>


<style scoped lang="scss">

.compact-form {

  .el-form-item {
    display: flex;           /* 弹性布局 */
    margin-bottom: 8px;      /* 减少底部间距 */
  }

  .el-form-item__label {
    text-align: right;       /* 标签右对齐 */
    padding: 0 8px 0 0;      /* 减少右侧间距 */
    flex-shrink: 0;          /* 禁止收缩 */

    display: none;
  }

  ::v-deep .el-form-item__content{
    width: 100%;
    text-align: left;
  }

  /* 隐藏表单自带的必填星号 */
 /* .el-form-item.is-required .el-form-item__label:before {
    display: none;

  }*/

 ::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
    text-align: right ;
   /* background: #f5f7fa;*/
    width: 90px;
    color: #464545;
  }

  ::v-deep .el-descriptions--mini.is-bordered .el-descriptions-item__cell{
    padding: 3px 8px;        /* 减少内边距 */
  }


  /* 在描述项标签上显示星号 */
  .el-descriptions-item__label .is-required:before {
    content: "*";
    width: 90px;
    color: #f56c6c;
    margin-right: 4px;
  }

  .no-margin {
    margin-bottom: 0;
  }

  /* 压缩布局：减少各组件间距 */
  .mt-10 {
    margin-top: 5px !important;  /* 减少顶部间距 */
  }

  .mb8 {
    margin-bottom: 5px !important;  /* 减少底部间距 */
  }

/*  ::v-deep .el-radio-button--small .el-radio-button__inner{
    padding: 6px 8px;    !* 减少按钮内边距 *!
  }*/

  .tableChild{
    margin: 0 auto;     /* 水平居中 */
    width: fit-content; /* 宽度自适应内容 */
    text-align: center; /* 内部文本居中（可选） */
  }

  /* 压缩输入框高度 */
  ::v-deep .el-input--mini .el-input__inner {
    height: 24px;       /* 减少输入框高度 */
    line-height: 24px;
  }

  ::v-deep .el-textarea--mini .el-textarea__inner {
    padding: 3px 8px;   /* 减少文本域内边距 */
  }

  /* 压缩日期选择器高度 */
   ::v-deep .el-date-editor--date.el-input--mini .el-input__inner {
     height: 24px;
     line-height: 24px;
   }

   /* 压缩分割线间距 */
   ::v-deep .el-divider--horizontal {
     margin: 8px 0;      /* 减少分割线上下间距 */
   }

   /* 压缩表格行高 */
    ::v-deep .el-table--mini .el-table__cell {
      padding: 3px 0 !important;     /* 减少表格单元格内边距 */
    }

   /* 压缩按钮组间距 */
   ::v-deep .el-button--mini {
     padding: 4px 8px;   /* 减少按钮内边距 */
   }

   /* 压缩整体容器间距 */
   .app-container {
     padding: 10px;      /* 减少容器内边距 */
   }

  /* 全局优化：确保文字在彩色行上清晰可见 */
  ::v-deep  .el-table [class*="-row"] .cell {
    color: #333 !important;
    font-weight: normal;
  }


  ::v-deep .el-table  {
    /* 1. 警示黄色（待处理/需注意） */
    .warning-row {
      background: #FFF8E6;
    }

    /* 2. 成功绿色（已完成/成功状态） */
    .success-row {
      background: #F0F9EB;
    }

    /* 3. 高亮蓝色（当前选中/活跃行） */
    .active-row {
      background: #E6F7FF;
    }

    /* 4. 错误红色（异常/失败行） */
    .error-row {
      background: #FFECE6;
    }

    /* 5. 中性灰色（禁用/次要行） */
    .disabled-row {
      background: #eafdfb;
    }

    /* 6. 柔和紫色（特殊标记行） */
    .mark-row {
      background: #F9F0FF;
    }

    /* 7. 活力橙色（高优先级行） */
    .urgent-row {
      background: #FFF2E6;
    }

    /* 8. 清新青色（新增/更新行） */
    .new-row {
      background: #E6FFFB;
    }

    /* 9. 清新青色（新增/更新行） */
    .nine-row {
      background: #dbd6fd;
    }
    /* 10. 清新青色（新增/更新行） */
    .ten-row {
      background: #faffe6;
    }
    /* 11. 清新青色（新增/更新行） */
    .elen-row {
      background: #e6e6ff;
    }

    .el-table--mini .el-table__cell {
      padding: 3px 0 !important;  /* 统一表格行高 */
    }

  }



 /* .el-table .success-row {
    background: #f0f9eb;
  }*/
}
</style>
