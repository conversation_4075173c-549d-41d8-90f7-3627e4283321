import request from '@/utils/request'

// 查询物件申请列表
export function listRequest(query) {
  return request({
    url: '/manage/request/list',
    method: 'get',
    params: query
  })
}

// 查询物件申请详细
export function getRequest(resRequestId) {
  return request({
    url: '/manage/request/' + resRequestId,
    method: 'get'
  })
}

// 新增物件申请
export function addRequest(data) {
  return request({
    url: '/manage/request',
    method: 'post',
    data: data
  })
}

// 批量新增物件申请清单
export function batchTResRequestDetail(data) {
  return request({
    url: '/manage/request/batchTResRequestDetail',
    method: 'post',
    data: data
  })
}

// 批量删除物件申请清单
export function detailDeleteByIds(ids) {
  return request({
    url: '/manage/request/detailDeleteByIds/' + ids,
    method: 'delete'
  })
}

// 修改物件申请
export function updateRequest(data) {
  return request({
    url: '/manage/request',
    method: 'put',
    data: data
  })
}


export function updateRequestApproval(data) {
  return request({
    url: '/manage/request/updateRequestApproval',
    method: 'put',
    data: data
  })
}

// 删除物件申请
export function delRequest(resRequestId) {
  return request({
    url: '/manage/request/' + resRequestId,
    method: 'delete'
  })
}
