<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"

          icon="el-icon-plus"

          @click="handleAdd"
          v-hasPermi="['manage:language:add']"
        >{{ strConvert('新增') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"

          icon="el-icon-edit"

          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:language:edit']"
        >{{ strConvert('修改') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"

          icon="el-icon-delete"

          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:language:remove']"
        >{{ strConvert('删除') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"

          @click="handleExport"
          v-hasPermi="['manage:language:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="languageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column :label="strConvert('中文')" align="center" prop="cnName" />
      <el-table-column :label="strConvert('英文')" align="center" prop="enName" />
      <el-table-column :label="strConvert('俄文')" align="center" prop="ruName" />
      <el-table-column :label="strConvert('格文')" align="center" prop="geName" />
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button

            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['manage:language:edit']"
          >{{ strConvert('修改') }}</el-button>
          <el-button

            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['manage:language:remove']"
          >{{ strConvert('删除') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改多语言对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="strConvert('中文')" prop="cnName">
          <el-input v-model="form.cnName" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
        <el-form-item :label="strConvert('英文')" prop="enName">
          <el-input v-model="form.enName" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
        <el-form-item :label="strConvert('俄文')" prop="ruName">
          <el-input v-model="form.ruName" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
        <el-form-item :label="strConvert('格文')" prop="geName">
          <el-input v-model="form.geName" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { listLanguage, getLanguage, delLanguage, addLanguage, updateLanguage } from "@/api/manage/language"

export default {
  name: "Language",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 多语言表格数据
      languageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cnName: null,
        enName: null,
        ruName: null,
        geName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        cnName: [
          { required: true, message: "中文不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询多语言列表 */
    getList() {
      this.loading = true
      listLanguage(this.queryParams).then(response => {
        this.languageList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        cnName: null,
        enName: null,
        ruName: null,
        geName: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加多语言"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getLanguage(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改多语言"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLanguage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addLanguage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除多语言编号为"' + ids + '"的数据项？').then(function() {
        return delLanguage(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/language/export', {
        ...this.queryParams
      }, `language_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
