<template>
  <!-- 结算单据模板 -->
  <div v-if="open">

        <el-row>
          <div class="file-header">
            <img v-if="fileLog" :src="fileLog" class="file-logo" />
            <div> ცხრილი<br>技师派工单 </div>
          </div>
        </el-row>

        <div class="app-container">
          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="  grid-bottom">
                მიღების დრო时间: <span>  {{ FormData.title }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-right  grid-bottom">
                №/工单编号: <span>  {{ FormData.serviceId }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="grid-left  grid-bottom">
                მფლობელი车主: <span>  {{ FormData.customerNickName }}</span>
              </div>
            </el-col>

            <el-col :span="6">
              <div class="grid-right  grid-bottom">
                ა/მ მარკა品牌:  <span>  {{ FormData.brand }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="grid-right  grid-bottom">
                ფერი/ 颜色： <span>  {{ FormData.color }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="grid-left  grid-bottom">
                ტელეფონი电话: <span>  {{ FormData.phonenumber }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-right  grid-bottom">
                სახ. №车牌号: <span>  {{ FormData.carPlateNumber }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row type="flex" class="row-bg">
            <el-col :span="12">
              <div class="grid-left  grid-bottom">
                მიღ.თარ/进厂日期：<span>  {{ formatDate(FormData.createTime) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="grid-right  grid-bottom">
                შესრ.თარ/取车日期： <span>  {{ formatDate(FormData.expectedTime) }}</span>
              </div>
            </el-col>
          </el-row>

          <div class="app-table-list" >
            <div class="tables-border">
              <el-row type="flex"   v-for="detail in T_WORK_ORDER_SERVICE_TYPE"  :key="detail.value">
                <worker-table-receipt class="row-table"
                                      :count-amount="FormData.mainTotalAmount"
                                      :total-amount="detail.amount"
                                      :is-summary="!isValidString(detail.isShowCount)"
                                      :ratio="detail.ratio"
                                      :title="detail.label"
                                      :order-question-detail-list="detail[detail.value]"/>
              </el-row>
               <el-row type="flex" >
                <el-col :span="12">
                  <div class=" ">
                    სულ总金额:  <span>  {{FormData.totalAmount }} GEL</span>
                  </div>
                </el-col>
                 <el-col :span="12">
                  <div class="  ">
                   ფასდაკლება折后金额:  <span>  {{FormData.discountAmount }} GEL</span>
                  </div>
                </el-col>
              </el-row>
              <el-row type="flex" >
                 <el-col :span="12">
                  <div class=" ">
                    სამუშაო შეკვეთა主项合计金额:  <span>  {{FormData.mainTotalAmount }} GEL</span>
                  </div>
                </el-col>
              </el-row>
              <el-row type="flex" class="row-bg">
                <el-col :span="12">
                  <div class=" ">
                    财务审核ბუღალტერი: <span> </span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="">
                    总经理დირექტორი: <span>  </span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <el-row>
            <div style="margin-top: 25px;margin-right:45px;text-align: right;font-size: 15px">
              <p>  European Mazda Automobile Trading Co., Ltd.</p>
              <p>{{ formatWesternDate(new Date()) }}</p>
            </div>
          </el-row>
        </div>

        <!-- 强制分页 -->
        <div class="page-break"></div>



        <div v-if="isValidString(FormData.comment)&&isValidString(FormData.images)" class="app-container">
          <el-row>
            <div style="text-align: center;font-weight: bold;font-size: 18px"><p> 附件信息დანართის ინფორმაცია</p></div>
          </el-row>
          <el-descriptions   :column="1"  border>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                დამატებითი ინფორმაცია问题说明:
              </template>
              {{ FormData.comment }}
            </el-descriptions-item>
          </el-descriptions>
          <el-row  v-if="optionPrint" >
            <div style="margin: 8px">
              <ImageCustomPreview  :src="FormData.images" :width="730" :height="600"/>
            </div>
          </el-row>
        </div>
  </div>
</template>

<script>
import { formatDate, formatWesternDate, isValidString } from '@/utils'
import i18n from '@/i18n/i18n'
import { strConvert } from '@/i18n/i18nMix'
import file_log from '@/assets/logo/fileLog.png'
import TableReceipt from '@/views/receipt/components/tableReceipt.vue'
import WorkerTableReceipt from '@/views/receipt/components/workerTableReceipt.vue'
import ImageCustomPreview from '@/components/ImageCustomPreview/index.vue'
import { isNumber } from 'pdfmake/src/helpers'

export default {
  name: 'workerReceipt',
  dicts: ['t_work_order_detail_service_type','t_work_question_service_type'],
  components:{
    ImageCustomPreview,
    WorkerTableReceipt,
    TableReceipt,
    i18n
  },
  props: {
    open: {
      type: Boolean,
      default: false
    },
    OrderQuestionDetailList:{
      type: Array,
      default: []
    },
    OrderDetailList: {
      type: Array,
      default: []
    },
    // 表单参数
    FormData: {
      type: Object,
      default: () => ({
        serviceId: null,
        title: null,
        businessType: "0",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        mainTotalAmount:0.00,
        taxRate:0.00,
        phonenumber:null,
        advancePayment:0,//预收金额
        discountAmount: 0,//折后金额
        totalAmount: 0,//总金额
        balancePayment: 0,//再付金额
        balance:0,//余额
        comment:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      })
    },
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      loading:true,
      rowKey:Math.random().toFixed(5).toString(),// 强制重新渲染
      saveOrderDetailList:[],
      T_WORK_ORDER_SERVICE_TYPE:[],
      fileLog:file_log,
      totalCountAmount: 0.00,//工单总计
      optionPrint:true,
      countAmount:0.00,//可汇总计算的服务金额统计
    }
  },
  created() {
    console.log(this.FormData)
  },
  watch: {
    'OrderDetailList': {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log(15652,newVal)
          this.generateWorkOrderServiceType(newVal);
        }
      }
    },
  },
  methods: {
    isValidString,
    formatWesternDate,
    formatDate,
    strConvert,
    generateWorkOrderServiceType(detailList) {
      console.log(this.FormData)
      this.T_WORK_ORDER_SERVICE_TYPE = []; // 清空旧数据

      let sumServerCount = false;
      this.dict.type.t_work_order_detail_service_type.forEach(item => {
        const questionItems = detailList.filter(data => item.value === data.questionType);

        if (questionItems.length === 0) return;

        let totalAmount = 0.00;

        if (isValidString(item.raw.cssClass) && this.FormData.mainTotalAmount > 0) {
          // 合并计算（isShowCount 为 "+"）
          if (item.raw.cssClass === "+") {
            if (!sumServerCount) {
              totalAmount = this.FormData.mainTotalAmount * (parseInt(item.raw.remark) / 100);
              sumServerCount = true;
            }
          } else {
            // 非合并计算，每类都算一次
            totalAmount = this.FormData.mainTotalAmount * (parseInt(item.raw.remark) / 100);
          }
          // 所有明细的 amount 设为 0，只有第一项设为 totalAmount
          questionItems.forEach(q => {
            q.price = 0.00;
            q.amount = 0.00;
          });
          if (questionItems.length > 0) {
            questionItems[0].amount = totalAmount;
          }
        } else {
          // 不走合并逻辑，累加原始 amount 字段
          totalAmount = questionItems.reduce((sum, data) => {
            return sum + (parseFloat(data.amount) || 0);
          }, 0);
        }

        const printObj = {
          value: item.value,
          label: item.label,
          isShowCount: item.raw.cssClass,
          ratio: parseInt(item.raw.remark),
          amount: totalAmount,
          [item.value]: questionItems
        };

        this.T_WORK_ORDER_SERVICE_TYPE.push(printObj);
      });

    }
  }
}
</script>
<style lang="scss" scoped>
  #acceptTable .el-table__empty-block {
    display: none;
  }

  .file-header{
    text-align: center;
    font-size: 15px;
    .file-logo{
      width: 267px;
      height: 44px
    }
  }

  .grid-bottom{
    border-bottom: 1px solid #1e1e1e;
    line-height: 20px;
    font-size: 12px;
  }

  .grid-right  {
    padding-left: 10px;
  }
  .grid-left {
    border-right: 1px solid #1e1e1e;

  }

  .grid-count{
    text-align: left;
    margin-left: 2px;
    line-height: 20px;
    font-size: 13px;
    font-weight: bold;
  }

  ::v-deep .el-table--group, .el-table--border{
   /* border: 1px solid #FFFFFF;*/
  }
  ::v-deep .el-table--border .el-table__cell{
    border: 1px solid #3b3b3b;
  }

  ::v-deep .el-table--mini .el-table__cell{
    padding: 3px 0;
  }

  ::v-deep .el-descriptions--medium.is-bordered .el-descriptions-item__cell{
    width: 60px;
  }

  .app-table-list{
    .el-table__header-wrapper{
      font-weight: initial;
    }
    .tables-border{
    /*  border: 1px solid #1e1e1e;*/
      .row-table{
        width: 100%;
      }
    }
  }

  .totalHeader{
    font-size:12px;
    line-height: 18px;
  }

  .receiptFont{
    font-family: 'DengXian', sans-serif;
  }

</style>
