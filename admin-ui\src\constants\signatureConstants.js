/**
 * 签名配置常量
 * 统一管理项目中使用的签名文本，避免重复配置
 */

// 格鲁吉亚语签名文本常量
export const SIGNATURE_TEXTS = {
  // 车主签字文本
  OWNER_SIGNATURE: 'სერვისში მომსახურების ფასებს გავეცანი, რაზედაც ვაწერ ხელს __________________ 车主签字',

  // 接单人签字文本
  STAFF_SIGNATURE: 'სერვისის წარმომადგენლის ხელმოწერა ____________ 接单人签字',

  // 联系电话
  PHONE_NUMBER: '+995 551 52 00 88'
}

// 签名单元格样式配置
export const SIGNATURE_CELL_STYLE = {
  ct: { fa: 'General', t: 'g' },
  bg: null,
  bl: 0,
  it: 0,
  ff: '等线',
  fs: 9,
  fc: 'rgb(0, 0, 0)',
  ht: 0, // 左对齐
  vt: 1
}


