<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('工单编号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="!($store.getters.roles.indexOf('common')>=0)" :label="strConvert('操作人员')" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          :placeholder="strConvert('请输入操作人员名称')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item  :label="strConvert('业务名称')" prop="questionName">
        <el-input
          v-model="queryParams.questionName"
          :placeholder="strConvert('请输入业务名称')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('操作时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="workorderList"  size="mini">
      <!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column :label="strConvert('工单编号')"  width="155"  align="center" prop="serviceId" />
      <el-table-column :label="strConvert('服务类型')" align="center" prop="questionType" width="280">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_work_order_detail_service_type" :value="scope.row.questionType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('业务名称')"  align="center" prop="questionName" />
      <el-table-column :label="strConvert('金额')" width="100" align="center" prop="amount" />
      <el-table-column :label="strConvert('备注')" width="150" align="center" prop="remark" />
      <el-table-column :label="strConvert('操作人员')" align="center" prop="updateBy" />
      <el-table-column :label="strConvert('操作时间')" align="center" prop="createTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.operTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>

    </el-table>
    <el-row>
      <div style="color: #e60000;margin-top: 10px;"> 总计金额：{{totalAmount}} GEL</div>
    </el-row>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import {
  detailListWorkorder,
} from '@/api/manage/workorder'
export default {
  name: "workorderDaily",
  dicts: ['t_work_order_detail_service_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      isEdit:true,
      totalAmount:0.00,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedTWorkOrderDetail: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      restCustomer: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单记录表格数据
      workorderList: [],
      // 表单参数
      form: {},

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 备注时间范围
      daterangeCreateTime: [],
      serviceId:"",
      printType:"",//打印单据类型
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceId: null,
        title: null,
        businessType: null,
        carPlateNumber:null,
        customerId: null,
        customerNickName: null,
        createTime: null,
      },
    }
  },
  created() {
    console.log(this.$store.getters.roles)
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询工单记录列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      /**此处添加角色权限过滤*/
      if(this.$store.getters.roles.indexOf('common')>=0){
        this.queryParams.updateBy=this.$store.getters.name;
      }
      detailListWorkorder(this.queryParams).then(response => {
        this.workorderList = response.rows
        this.total = response.total
        this.totalAmount=response.msg;
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },



    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/workorder/export', {
        ...this.queryParams
      }, `workorder_${new Date().getTime()}.xlsx`)
    },
  }
}
</script>
