<template>
  <!-- 结算单据模板 -->
  <div>
    <!-- 添加或修改工单记录对话框 -->
    <el-dialog  :visible.sync="dialogVisible"  @opened="onDialogOpened" :close-on-click-modal="false" title=""  width="820px" height="900px" >
      <el-divider content-position="center">打印预览</el-divider>
      <print-component
        ref="printComponent"
        v-loading="loading"
        @resetShowImage="resetShowImage"
        :show-buttons="false"
        :print-options="{
      popTitle: '受理工单',
      extraCss: 'https://example.com/print.css'
      }"
        :pdf-options="{
        filename: 'work-report.pdf',
        margin: 5
      }"
      >

          <accept-receipt
            v-if="show==='accept'||show==='index'"
            :OrderDetailList="saveOrderDetailList"
            :FormData="saveForm"
            :open="printType==='acceptReceipt'"
          />

          <worker-receipt
            v-if="show==='worker'||show==='index'"
            :OrderDetailList="saveOrderDetailList"
            :OrderQuestionDetailList="tWorkOrderQuestionDetailList"
            :FormData="saveForm"
            :open="printType==='workerReceipt'"
          />

          <settlement-receipt
            v-if="show==='settlement'||show==='index'"
            :OrderDetailList="saveOrderDetailList"
            :OrderPartsDetailList="tWorkPartsDetailList"
            :FormData="saveForm"
            :open="printType==='settlementReceipt'"
          />

      </print-component>
      <div slot="footer" class="dialog-footer">
        <el-row :gutter="12" class="mb8">
          <el-col :span="14" >
            <el-radio-group v-model="$i18n.locale" size="small">
              <el-radio-button label="zh" v-if="isShowOption">
                <span class="flag-icon flag-icon-cn"  style="margin-right: 6px;"></span>
                中文
              </el-radio-button>
              <el-radio-button label="en">
                <span class="flag-icon flag-icon-us" style="margin-right: 6px;"></span>
                English
              </el-radio-button>
              <el-radio-button label="ru">
                <span class="flag-icon flag-icon-ru" style="margin-right: 6px;"></span>
                Русский
              </el-radio-button>
              <el-radio-button label="ka">
                <span class="flag-icon flag-icon-ge" style="margin-right: 6px;"></span>
                ქართული
              </el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="10" >
            <el-button  type="primary" icon="el-icon-printer" @click="handlePrint">打  印</el-button>
            <el-button  type="warning" icon="el-icon-download" @click="handleExportPDF">下  载</el-button>
            <el-button @click="handleClose">取 消</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">

</style>
<script>
import acceptReceipt from '@/views/receipt/acceptReceipt.vue'
import settlementReceipt from '@/views/receipt/settlementReceipt.vue'
import WorkerReceipt from '@/views/receipt/workerReceipt.vue'
import { getWorkorder } from '@/api/manage/workorder'
import Vue from 'vue';
import VueI18n from 'vue-i18n';
import { ensureLocaleLoaded } from '@/i18n/i18nMix'
import store from '@/store'
import { applyTranslation, isValidString, trimPunctuation } from '@/utils'
import Cookies from 'js-cookie'
Vue.use(VueI18n);
export const printI18n = new VueI18n({
  locale: 'zh', // 默认语言
  messages: {}, // 独立的消息存储
});

export default {
  i18n: printI18n,
  name:"receipt",
  components: { WorkerReceipt, settlementReceipt, acceptReceipt },
  data() {
    return {
    loading:false,
    isShowOption:true,
    transferMapping: {},
    transferPartsMapping: {},
    currentLanguage:'zh',
    // 工单详情表格数据
    tWorkOrderQuestionDetailList: [],
    // 工单详情表格数据
    tWorkOrderDetailList: [],
    // 工单详情表格数据
    tWorkPartsDetailList: [],
    FormData: {
      serviceId: null,
      title: null,
      businessType: "0",
      operatorType: "0",
      operProgress: 0,
      customerId: null,
      customerNickName: null,
      status: null,
      carPlateNumber:null,
      mainTotalAmount:0.00,
      taxRate:0.00,
      phonenumber:null,
      advancePayment:0,//预收金额
      discountAmount: 0,//折后金额
      totalAmount: 0,//总金额
      balancePayment: 0,//再付金额
      balance:0,//余额
      comment:null,
      images:null,
      operTime: null,
      costTime: null,
      createBy: null,
      createTime: null,
      updateBy: null,
      updateTime: null,
      remark: null
      },
      saveForm:{},
      saveOrderDetailList:[],
    }
  },
  props: {
    serviceId:{
      type:String,
      default:""
    },
    show:{
      type:String,
      default:""
    },
    printType:{
      type:String,
      default:""
    },
    OpenPrint: {
      type: Boolean,
      default: false
    },
  },
  watch: {
    '$i18n.locale': {
      handler: async function (val) {
        if (this._inactive) return // 避免被keep-alive组件重复触发


        this.loading=true;
        /* 加载语言文件 */
        await ensureLocaleLoaded(val)
        /*  const messages = this.$store.getters['i18nStore/getMessages'](val) */
        console.log(" this.isShowOption", this.FormData.title)
        if (isValidString(this.FormData.title)) {
          const fransStr = [];
         /*  if(isValidString(this.FormData.title)){
            fransStr.push(this.FormData.title);
          } */

          this.saveOrderDetailList.forEach(item => {
            if(isValidString(item.oldQuestionName)){
              fransStr.push(item.oldQuestionName);
            }
            if(isValidString(item.oldQuestionComment)){
              fransStr.push(item.oldQuestionComment);
            }
          });

          if(isValidString(this.FormData.comment)){
            fransStr.push(this.FormData.comment);
          }

          this.$nextTick(async () => {
            //去重
            const finalFransStr = [...new Set(fransStr)];

            if(finalFransStr.length===0||this.printType==='acceptReceipt'){
             /*  this.loading=false; */
              this.isShowOption=false;
             /*  return */
            }
            if(val===this.currentLanguage){
              this.saveOrderDetailList.forEach(item => {
                item.questionName=item.oldQuestionName
                item.questionComment=item.oldQuestionComment
              });
              this.saveForm.title=this.FormData.title
              this.saveForm.comment=this.FormData.comment
              this.loading=false;
            }else{
              store.dispatch('customerTranslation/translateBatchGoogle', {
                texts: finalFransStr,
                targetLang: val,
                sourceLang:this.currentLanguage,
              }).then(res => {
                if (res.code===200) {
                  const translateList = res.data;
                  //构建映射表
                  this.transferMapping={};
                  translateList.forEach(item => {
                    this.transferMapping[item.sourceText] = item.text;
                  });
                  console.log(this.transferMapping,finalFransStr,this.transferMapping)
                  applyTranslation(this.FormData.title,this.saveForm, 'title', this.transferMapping);
                  applyTranslation(this.FormData.comment,this.saveForm, 'comment', this.transferMapping);
                  this.saveOrderDetailList.forEach(item => {
                    console.log(item.oldQuestionName,item.oldQuestionComment)
                    applyTranslation(item.oldQuestionName,item, 'questionName', this.transferMapping);
                    applyTranslation(item.oldQuestionComment,item, 'questionComment', this.transferMapping);
                  });
                }
                this.loading=false;
              })
            }
          });

          const partsStr = [];
          if(this.printType==='settlementReceipt'&&this.tWorkPartsDetailList.length>0){
            this.tWorkPartsDetailList.forEach(item => {
              if(isValidString(item.oldpartsName)) {
                partsStr.push(item.oldpartsName);
              }
              if(isValidString(item.optionComment)) {
                partsStr.push(item.optionComment);
              }
            });
            //去重
            const finalPartsStr = [...new Set(partsStr)];
            if(finalPartsStr.length===0){
              return
            }
            store.dispatch('customerTranslation/translateBatchGoogle', {
              texts: finalPartsStr,
              targetLang: val,
              sourceLang:this.currentLanguage,
            }).then(res => {
              if (res.code===200) {
                const partsTranslate = res.data;
                //构建映射表
                this.transferPartsMapping={};
                partsTranslate.forEach(item => {
                  this.transferPartsMapping[item.sourceText] = item.text;
                });
                console.log(this.transferPartsMapping,finalPartsStr)
                this.tWorkPartsDetailList.forEach(item => {
                  applyTranslation(item.oldpartsName,item, 'partsName', this.transferPartsMapping);
                  applyTranslation(item.optionComment,item, 'optionComment', this.transferPartsMapping);
                });
              }
              this.loading=false;
            })
          }


        }else{
          this.saveForm=this.FormData;
          this.loading=false;
        }
      },
      immediate: true
    }
  },
  created() {
    this.currentLanguage = Cookies.get("language")
  /*   this.$i18n.locale="zh"; */
    console.log(this.$i18n.locale)
  },
  computed: {
    dialogVisible: {
      get() {
        return this.OpenPrint;
      },
      set(value) {
        this.$emit('update:OpenPrint', value);
      }
    },
  },
  methods:{
    getInfo(){
      getWorkorder(this.serviceId).then(response => {
        this.FormData = response.data
        this.saveForm=JSON.parse(JSON.stringify(this.FormData))
        let orderList=[];
        if(this.printType==='acceptReceipt'){
          if(response.data.tWorkOrderQuestionDetailList!==undefined){
            this.tWorkOrderQuestionDetailList = response.data.tWorkOrderQuestionDetailList
            orderList= JSON.parse(JSON.stringify(this.tWorkOrderQuestionDetailList));
          }
        }else if(this.printType==='workerReceipt'){
          this.tWorkOrderQuestionDetailList = response.data.tWorkOrderQuestionDetailList
          if(response.data.tWorkOrderDetailList!==undefined){
            this.tWorkOrderDetailList = response.data.tWorkOrderDetailList
            orderList= JSON.parse(JSON.stringify(this.tWorkOrderDetailList));
          }
        }else if(this.printType==='settlementReceipt'){
          this.tWorkOrderQuestionDetailList = response.data.tWorkOrderQuestionDetailList
          orderList= JSON.parse(JSON.stringify(this.tWorkOrderQuestionDetailList));
          if(response.data.tWorkPartsDetailList!==undefined){
            this.tWorkPartsDetailList = response.data.tWorkPartsDetailList
            this.tWorkPartsDetailList.forEach(item=>{
              item.oldpartsName=item.partsName;
            })
          }
        }

        orderList.forEach(item=>{
          item.oldQuestionName=item.questionName;
          item.oldQuestionComment=item.questionComment;
        })
        //备份原文，防止全部翻译
        this.saveOrderDetailList=orderList;
        if(this.saveOrderDetailList.length>0&&this.printType!=='workerReceipt'){
          this.$i18n.locale="en"
        }
      })
    },
    async handlePrint() {
      this.optionPrint = false; // 确保对话框打开
      await this.$nextTick(); // 等待DOM更新
      await this.$refs.printComponent.print(); // 调用打印
    },
    handleExportPDF() {
      this.optionPrint=true
      this.$refs.printComponent.exportPDF() // 调用生成PDF方法
    },
    onDialogOpened(item) {
      console.log(this.$i18n.locale)
      // 对话框完全打开后的回调
      if (this.serviceId!==""){
        this.getInfo();
      }
    },
    handleClose() {
      this.saveForm={};
      this.tWorkOrderQuestionDetailList=[];
      this.saveOrderDetailList=[];
      this.tWorkPartsDetailList=[];
      this.tWorkOrderDetailList=[];
      this.$i18n.locale="zh";
      this.dialogVisible = false; // 通过 computed 修改，自动触发 emit
    },
    resetShowImage(){
      this.optionPrint=true;
    }
  }
}
</script>
