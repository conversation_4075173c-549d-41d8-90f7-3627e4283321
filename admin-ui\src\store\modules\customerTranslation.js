import { googleTranslate, translate, yandexTranslate } from '@/api/tool/translate'
import { isValidString } from '@/utils'

export default {
  namespaced: true,
  actions: {
    async translateBatch({ commit }, { texts, targetLang, sourceLang = 'zh' }) {
      try {
        const response = await translate({
          text: texts.join("1111"),
          sourceLang,
          targetLang
        });

        if (isValidString(response.data.translated)) {
          return {
            success: true,
            translated: response.data.translated.split("1111")
          };
        }
        return { success: false };
      } catch (error) {
        console.error('翻译失败', error);
        return { success: false, error };
      }
    },
    async translateBatchYandex({ commit }, { texts, targetLang, sourceLang = 'zh' }) {
      try {
        const response = await yandexTranslate({
          translates: texts,
          sourceLang,
          targetLang
        });
        return response;
      } catch (error) {
        console.error('翻译失败', error);
        return { success: false, error };
      }
    },
    async translateBatchGoogle({ commit }, { texts, targetLang, sourceLang = 'zh' }) {
      try {
        const response = await googleTranslate({
          translates: texts,
          sourceLang,
          targetLang
        });
        return response;
      } catch (error) {
        console.error('翻译失败', error);
        return { success: false, error };
      }
    }
  }

}

