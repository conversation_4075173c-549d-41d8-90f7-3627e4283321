<template>
  <div class="table-data">
    <table style="width: 100%;" class="title-display-table">
      <thead style="text-align: left; line-height: 23px;">
      <tr><th>{{ title }}</th></tr>
      </thead>
      <tbody>
      <tr>
        <td>
          <table class="data-display-table">
            <thead>
            <tr>
              <th style="width: 8%">NO</th>
              <th style="width: 22%">服务</th>
              <th style="width: 22%">სამღებრო სამუშაოები</th>
              <th style="width: 18%" class="col-amount">სამ.ღირებულება费用</th>
              <th style="width: 22%">备注</th>
            </tr>
            </thead>
            <tbody>
            <tr v-if="!OrderQuestionDetailList || OrderQuestionDetailList.length === 0">
              <td :colspan="5" class="no-data-cell">No data yet</td>
            </tr>
            <tr v-for="(item, index) in OrderQuestionDetailList" :key="index">
              <td class="col-no">{{ index + 1 }}</td>
              <td>{{ item.questionName }}</td>
              <td>{{ item.questionName }}</td>
              <td class="col-amount">{{ typeof item.amount === 'number' ? item.amount.toFixed(2) : item.amount }}</td>
              <td>{{ item.questionComment }}</td>
            </tr>
            </tbody>
            <tfoot v-if="OrderQuestionDetailList && OrderQuestionDetailList.length > 0">
            <tr>
              <td colspan="2" class="summary-label">费用合计/ფასი</td>
              <td></td>
              <td class="summary-amount col-amount">{{ totalAmount.toFixed(2) }} GEL</td>
              <td></td>
            </tr>
            </tfoot>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
export default {
  name: 'tableReceipt',
  props: {
    OrderQuestionDetailList: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ""
    }
  },
  computed: {
    totalAmount() {
      if (!this.OrderQuestionDetailList) {
        return 0;
      }
      return this.OrderQuestionDetailList.reduce((sum, item) => {
        return sum + (parseFloat(item.amount) || 0);
      }, 0);
    }
  }
  // getSummaries 方法已移除
}
</script>

<style scoped>
/* 包含整个组件的外部边框 */
.table-data {
/*  border: 1px solid #1e1e1e;*/
}

/* 用于显示标题的外部表格（如果需要特定样式） */
.title-display-table th {
  padding: 5px 8px; /* 给标题一些内边距 */
  font-weight: bold;
}


/* 数据展示表格 (替换了原 el-table) */
.data-display-table {
  width: 100%;
  border-collapse: collapse; /* 使单元格边框合并 */
}

.data-display-table th,
.data-display-table td {
  border: 1px solid #3b3b3b; /* 模拟Element UI的单元格边框 */
  height: 30px;
  font-weight: normal; /* 遵从原CSS中对el-table th, td的font-weight:normal设定 */
  padding: 3px 8px; /* 原padding: 3px 0; 改为3px 8px以在有边框时提供左右空间，如果需要严格3px 0，请改回*/
  line-height: 1.4;
}

.data-display-table th {
  background-color: #f5f7fa; /* 表头背景色 */
  /* font-weight: normal; 已在上面统一定义，若表头需加粗，可在此覆盖或删除上面的 normal */
}

/* 特定列的样式 */
.data-display-table .col-no, /* NO 列的td */
.data-display-table th[style*="width: 80px"] /* NO 列的th */ {
  text-align: center;
}

.data-display-table .col-amount {
  text-align: right;
}

/* 表脚样式 */
.data-display-table tfoot tr {
  background-color: #f5f7fa; /* 表脚背景色，同表头 */
}

.data-display-table tfoot td {
  font-weight: bold; /* 表脚文字加粗 */
}

.data-display-table tfoot .summary-label {
  text-align: center; /* “费用合计”居中 */
}

/* 无数据时的单元格样式 */
.data-display-table .no-data-cell {
  text-align: center;
  padding: 10px 0; /* 给“暂无数据”一些垂直空间 */
  color: #909399;
}
</style>
