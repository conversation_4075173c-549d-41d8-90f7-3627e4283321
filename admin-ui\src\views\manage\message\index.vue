<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('消息')" prop="sessionId">
        <el-input
          v-model="queryParams.sessionId"
          :placeholder="strConvert('请输入消息')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('消息标题')" prop="sessionTitle">
        <el-input
          v-model="queryParams.sessionTitle"
          :placeholder="strConvert('请输入消息标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户号码')" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          :placeholder="strConvert('请输入客户号码')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户邮箱')" prop="email">
        <el-input
          v-model="queryParams.email"
          :placeholder="strConvert('请输入客户邮箱')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('消息类型')" prop="sessionType">
        <el-select v-model="form.sessionType" :placeholder="strConvert('请选择消息类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_message_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:message:remove']"
        >{{ strConvert('删除') }}</el-button>
      </el-col>
<!--      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['manage:message:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="messageList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('消息ID')" align="center" prop="sessionId"/>
      <el-table-column :label="strConvert('消息标题')" align="center" prop="sessionTitle" />
      <el-table-column :label="strConvert('消息类型')" align="center" prop="sessionType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_message_type" :value="scope.row.sessionType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('客户号码')" align="center" prop="phonenumber" />
      <el-table-column :label="strConvert('客户邮箱')" align="center" prop="email" />
      <el-table-column :label="strConvert('备注')" align="center" prop="remark" />
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <span v-if="messageNotice[scope.row.sessionId]"
                style="position: relative;margin-top: 8px; display: inline-block;">
            <el-badge :value="messageNotice[scope.row.sessionId].length" size="min" class="item" type="danger">
              <el-button
                type="warning"
                icon="el-icon-chat-dot-round"
                @click="openMessage(scope.row)"
                v-hasPermi="['manage:message:edit']"
              >{{ strConvert('沟  通') }}</el-button>
            </el-badge>
          </span>
          <el-button
            v-else
            type="warning"
            icon="el-icon-chat-dot-round"
            @click="openMessage(scope.row)"
            v-hasPermi="['manage:message:edit']"
          >{{ strConvert('沟  通') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


    <!-- 添加或修改网站消息对话框 -->
    <el-dialog :close-on-click-modal="false"  :title="title" :visible.sync="open" width="700px" append-to-body>
        <el-form ref="form" :model="form" :rules="rules" label-width="80px">
          <el-form-item :label="strConvert('消息标题')" prop="sessionTitle">
            <el-input v-model="form.sessionTitle" :placeholder="strConvert('请输入消息标题')" />
          </el-form-item>
          <el-form-item :label="strConvert('客户号码')" prop="phonenumber">
            <el-input v-model="form.phonenumber" :placeholder="strConvert('请输入客户号码')" />
          </el-form-item>
          <el-form-item :label="strConvert('客户邮箱')" prop="email">
            <el-input v-model="form.email" :placeholder="strConvert('请输入客户邮箱')" />
          </el-form-item>
          <el-form-item :label="strConvert('消息类型')" prop="sessionType">
            <el-select v-model="form.sessionType" :placeholder="strConvert('请选择消息类型')" clearable>
              <el-option
                v-for="dict in dict.type.t_message_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="strConvert('消息内容')">
            <editor disabled="disabled"  v-model="form.sessionContent" :min-height="300" :min-width="200"/>
          </el-form-item>
        </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改网站消息对话框 -->
    <el-dialog :close-on-click-modal="false"  :title="strConvert('客户沟通')" :visible.sync="messageOpen" width="1000px" append-to-body>
      <el-container>
        <el-aside style="width:400px" >
          <el-descriptions style="margin-top:20px" :title="strConvert('基本信息')" :column="1"  border>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                消息标题
              </template>
              {{ form.sessionTitle }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-mobile-phone"></i>
                客户号码
              </template>
              {{ form.phonenumber||'无' }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-upload"></i>
                客户邮箱
              </template>
              {{ form.email||'无' }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-location-outline"></i>
                消息类型
              </template>
              <template slot-scope="scope">
                <dict-tag :options="dict.type.t_message_type" :value="form.sessionType"/>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </el-aside>
        <el-main style="width: 600px;padding:0">
              <ChatConsult :isVisible="messageOpen"  :query-session-id="sessionId" @close="messageOpen=false" />

          <!--          <div slot="footer" class="dialog-footer">
                      <el-button @click="closeMessage">{{ strConvert('取 消') }}</el-button>
                    </div>-->
        </el-main>
      </el-container>
    </el-dialog>



  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import {
  listMessage,
  getMessage,
  delMessage,
  addMessage,
  updateMessage,
  removeNoReaderMessage
} from '@/api/manage/message'
import ChatConsult from '@/components/chat/ChatConsult.vue'

export default {
  name: "Message",
  components: { ChatConsult },
  dicts: ['t_message_type'],
  data() {
    return {
      showChat: false, // 添加聊天组件显示控制
      sessionId:"",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 网站消息表格数据
      messageList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      messageOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sessionId: null,
        sessionTitle: null,
        sessionType: null,
        phonenumber: null,
        email: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        sessionContent: [
          { required: true, message: "消息内容不能为空", trigger: "blur" }
        ],
      },
      messageNotice:{},
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询网站消息列表 */
    getList() {
      this.loading = true
      listMessage(this.queryParams).then(response => {
        this.messageList = response.data.tablesData.rows
        this.messageNotice = response.data.messageNotice
        console.log(this.messageNotice)
        this.total = response.data.tablesData.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        sessionId: null,
        sessionTitle: null,
        sessionType: null,
        phonenumber: null,
        email: null,
        sessionContent: null,
        status: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sessionId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加网站消息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const sessionId = row.sessionId || this.ids
      getMessage(sessionId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改网站消息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.sessionId != null) {
            updateMessage(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addMessage(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const sessionIds = row.sessionId || this.ids
      this.$modal.confirm('是否确认删除网站消息编号为"' + sessionIds + '"的数据项？').then(function() {
        return delMessage(sessionIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/message/export', {
        ...this.queryParams
      }, `message_${new Date().getTime()}.xlsx`)
    },
    openMessage(row){
      const sessionId = row.sessionId || this.ids
      console.log(row)
      this.reset()
      removeNoReaderMessage(sessionId).then(response => {
        this.getList();
        this.form = response.data
        this.sessionId=this.form.sessionId;
        this.messageOpen=true;
      })

    },
    closeMessage(){
      this.messageOpen=false;
    },
    submitMessage(){
      console.log(11)
    }
  }
}
</script>
