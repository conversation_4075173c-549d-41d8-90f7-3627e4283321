<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Univer 动态边框功能演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .demo-section h2 {
            color: #555;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .demo-controls {
            margin-bottom: 20px;
        }
        
        .demo-controls button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-controls button:hover {
            background: #0056b3;
        }
        
        .demo-controls select {
            padding: 8px 12px;
            margin-right: 10px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .demo-output {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        
        .table-preview {
            border-collapse: collapse;
            width: 100%;
            margin-top: 15px;
        }
        
        .table-preview td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: center;
            min-width: 80px;
            height: 30px;
        }
        
        .border-thick {
            border-width: 2px !important;
            border-color: #000 !important;
        }
        
        .border-dashed {
            border-style: dashed !important;
        }
        
        .border-red {
            border-color: #ff0000 !important;
        }
        
        .header-cell {
            background-color: #4472C4;
            color: white;
            font-weight: bold;
        }
        
        .data-cell {
            background-color: #f8f9fa;
        }
        
        .highlight-cell {
            background-color: #fff3cd;
            border: 2px solid #ff0000 !important;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
        }
        
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Univer 动态边框功能演示</h1>
        
        <div class="demo-section">
            <h2>📋 功能特性</h2>
            <ul class="feature-list">
                <li>智能边框添加 - 根据单元格位置自动添加合适的边框</li>
                <li>边框样式预设 - 提供多种预定义的边框样式</li>
                <li>边框管理器 - 统一管理多个边框区域</li>
                <li>表格专用边框 - 为表格提供专业的边框样式</li>
                <li>高亮边框 - 为选中区域添加醒目的边框</li>
                <li>优先级控制 - 支持边框优先级管理</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>🎯 基础边框演示</h2>
            <div class="demo-controls">
                <button onclick="demonstrateBasicBorders()">演示基础边框</button>
                <select id="borderStyleSelect">
                    <option value="default">默认样式</option>
                    <option value="thick">粗边框</option>
                    <option value="thin">细边框</option>
                    <option value="dashed">虚线边框</option>
                    <option value="dotted">点线边框</option>
                </select>
                <select id="borderTypeSelect">
                    <option value="all">全边框</option>
                    <option value="outer">外边框</option>
                    <option value="inner">内边框</option>
                    <option value="top">顶部边框</option>
                    <option value="bottom">底部边框</option>
                </select>
            </div>
            <div class="demo-output" id="basicBordersOutput">
                <div class="status info">点击"演示基础边框"按钮查看效果</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📊 表格边框演示</h2>
            <div class="demo-controls">
                <button onclick="demonstrateTableBorders()">演示表格边框</button>
                <button onclick="demonstrateHighlightBorders()">演示高亮边框</button>
            </div>
            <div class="demo-output" id="tableBordersOutput">
                <div class="status info">点击按钮查看表格边框效果</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>⚙️ 边框管理器演示</h2>
            <div class="demo-controls">
                <button onclick="demonstrateBorderManager()">演示边框管理器</button>
                <button onclick="clearAllDemos()">清除所有演示</button>
            </div>
            <div class="demo-output" id="borderManagerOutput">
                <div class="status info">点击"演示边框管理器"按钮查看效果</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>💻 代码示例</h2>
            <div class="code-block">
<pre>// 基础边框添加
converter.addDynamicBorders(cellData, {
  startRow: 3,
  endRow: 6,
  startCol: 0,
  endCol: 7,
  borderStyle: {
    color: '#000000',
    width: 1,
    style: 'solid'
  },
  borderTypes: ['all'],
  regionType: '基本信息区域'
});

// 使用边框管理器
converter.borderManager.registerBorderRegion(
  '表格区域',
  8, 15, 0, 7,
  {
    borderStyle: converter.getBorderStylePreset('tableData'),
    borderTypes: ['all'],
    priority: 2
  }
);

converter.borderManager.applyAllBorders(cellData, converter);</pre>
            </div>
        </div>
    </div>

    <script>
        // 模拟边框功能演示
        function demonstrateBasicBorders() {
            const output = document.getElementById('basicBordersOutput');
            const borderStyle = document.getElementById('borderStyleSelect').value;
            const borderType = document.getElementById('borderTypeSelect').value;
            
            output.innerHTML = `
                <div class="status success">✅ 基础边框演示完成</div>
                <p><strong>边框样式:</strong> ${borderStyle}</p>
                <p><strong>边框类型:</strong> ${borderType}</p>
                <table class="table-preview">
                    <tr>
                        <td class="border-${borderStyle === 'thick' ? 'thick' : borderStyle === 'dashed' ? 'dashed' : ''}">A1</td>
                        <td class="border-${borderStyle === 'thick' ? 'thick' : borderStyle === 'dashed' ? 'dashed' : ''}">B1</td>
                        <td class="border-${borderStyle === 'thick' ? 'thick' : borderStyle === 'dashed' ? 'dashed' : ''}">C1</td>
                    </tr>
                    <tr>
                        <td class="border-${borderStyle === 'thick' ? 'thick' : borderStyle === 'dashed' ? 'dashed' : ''}">A2</td>
                        <td class="border-${borderStyle === 'thick' ? 'thick' : borderStyle === 'dashed' ? 'dashed' : ''}">B2</td>
                        <td class="border-${borderStyle === 'thick' ? 'thick' : borderStyle === 'dashed' ? 'dashed' : ''}">C2</td>
                    </tr>
                </table>
                <p><em>实际效果会在 Univer 中显示</em></p>
            `;
        }
        
        function demonstrateTableBorders() {
            const output = document.getElementById('tableBordersOutput');
            output.innerHTML = `
                <div class="status success">✅ 表格边框演示完成</div>
                <table class="table-preview">
                    <tr>
                        <td class="header-cell border-thick">表头1</td>
                        <td class="header-cell border-thick">表头2</td>
                        <td class="header-cell border-thick">表头3</td>
                        <td class="header-cell border-thick">表头4</td>
                    </tr>
                    <tr>
                        <td class="data-cell">数据1</td>
                        <td class="data-cell">数据2</td>
                        <td class="data-cell">数据3</td>
                        <td class="data-cell">数据4</td>
                    </tr>
                    <tr>
                        <td class="data-cell">数据5</td>
                        <td class="data-cell">数据6</td>
                        <td class="data-cell">数据7</td>
                        <td class="data-cell">数据8</td>
                    </tr>
                </table>
                <p><strong>特点:</strong> 表头使用蓝色背景和白色边框，数据区域使用灰色边框，外边框加粗</p>
            `;
        }
        
        function demonstrateHighlightBorders() {
            const output = document.getElementById('tableBordersOutput');
            output.innerHTML = `
                <div class="status success">✅ 高亮边框演示完成</div>
                <table class="table-preview">
                    <tr>
                        <td>A1</td>
                        <td>B1</td>
                        <td>C1</td>
                        <td>D1</td>
                    </tr>
                    <tr>
                        <td>A2</td>
                        <td class="highlight-cell">B2</td>
                        <td class="highlight-cell">C2</td>
                        <td>D2</td>
                    </tr>
                    <tr>
                        <td>A3</td>
                        <td class="highlight-cell">B3</td>
                        <td class="highlight-cell">C3</td>
                        <td>D3</td>
                    </tr>
                    <tr>
                        <td>A4</td>
                        <td>B4</td>
                        <td>C4</td>
                        <td>D4</td>
                    </tr>
                </table>
                <p><strong>特点:</strong> 选中区域 B2:C3 使用红色高亮边框</p>
            `;
        }
        
        function demonstrateBorderManager() {
            const output = document.getElementById('borderManagerOutput');
            output.innerHTML = `
                <div class="status success">✅ 边框管理器演示完成</div>
                <p><strong>注册的边框区域:</strong></p>
                <ul>
                    <li>区域1: 行0-2, 列0-4 (粗边框, 优先级1)</li>
                    <li>区域2: 行3-5, 列0-4 (虚线边框, 优先级2)</li>
                    <li>高优先级区域: 行1-1, 列1-3 (表头样式, 优先级10)</li>
                </ul>
                <table class="table-preview">
                    <tr>
                        <td class="border-thick">R0C0</td>
                        <td class="border-thick header-cell">R0C1</td>
                        <td class="border-thick header-cell">R0C2</td>
                        <td class="border-thick header-cell">R0C3</td>
                        <td class="border-thick">R0C4</td>
                    </tr>
                    <tr>
                        <td class="border-thick">R1C0</td>
                        <td class="border-thick header-cell">R1C1</td>
                        <td class="border-thick header-cell">R1C2</td>
                        <td class="border-thick header-cell">R1C3</td>
                        <td class="border-thick">R1C4</td>
                    </tr>
                    <tr>
                        <td class="border-thick">R2C0</td>
                        <td class="border-thick">R2C1</td>
                        <td class="border-thick">R2C2</td>
                        <td class="border-thick">R2C3</td>
                        <td class="border-thick">R2C4</td>
                    </tr>
                    <tr>
                        <td class="border-dashed">R3C0</td>
                        <td class="border-dashed">R3C1</td>
                        <td class="border-dashed">R3C2</td>
                        <td class="border-dashed">R3C3</td>
                        <td class="border-dashed">R3C4</td>
                    </tr>
                </table>
                <p><strong>说明:</strong> 高优先级区域的样式覆盖了低优先级区域</p>
            `;
        }
        
        function clearAllDemos() {
            document.getElementById('basicBordersOutput').innerHTML = '<div class="status info">点击"演示基础边框"按钮查看效果</div>';
            document.getElementById('tableBordersOutput').innerHTML = '<div class="status info">点击按钮查看表格边框效果</div>';
            document.getElementById('borderManagerOutput').innerHTML = '<div class="status info">点击"演示边框管理器"按钮查看效果</div>';
        }
    </script>
</body>
</html>
