<template>
  <div class="universal-spreadsheet-univer">
    <!-- 操作按钮区域 -->
    <div v-if="showOperations" class="operation-buttons">
      <el-button
        type="primary"
        icon="el-icon-download"
        :loading="pdfLoading"
        size="small"
        @click="exportToPDF"
      >
        导出PDF
      </el-button>
      <el-button
        type="success"
        icon="el-icon-s-grid"
        :loading="excelLoading"
        size="small"
        @click="exportToExcel"
      >
        导出Excel
      </el-button>
      <el-button
        type="info"
        icon="el-icon-refresh"
        size="small"
        @click="refreshData"
      >
        刷新
      </el-button>
      <el-button
        type="warning"
        icon="el-icon-printer"
        size="small"
        @click="printSpreadsheet"
      >
        打印
      </el-button>
    </div>

    <!-- Univer表格容器 -->
    <div
      :id="containerId"
      class="spreadsheet-container"
      :style="containerStyle"
    />
  </div>
</template>

<script>
import { createUniqueString } from '@/utils'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'
// 导入Univer相关依赖
// 使用 preset 模式，从 @univerjs/presets 导入 LocaleType
// 在 preset 模式下，插件已包含在 UniverSheetsCorePreset 中，无需单独导入
import { createUniver, LocaleType, mergeLocales } from '@univerjs/presets'
import { UniverSheetsCorePreset } from '@univerjs/preset-sheets-core'

// 导入绘图相关插件
import { UniverDrawingPlugin } from '@univerjs/drawing'
import { UniverDrawingUIPlugin } from '@univerjs/drawing-ui'
import { UniverSheetsDrawingPlugin } from '@univerjs/sheets-drawing'
import { UniverSheetsDrawingUIPlugin } from '@univerjs/sheets-drawing-ui'

// 导入Univer国际化语言包
import { zhCN, enUS } from '@univerjs/sheets-ui'
import { ensureLocaleLoaded } from '@/i18n/i18nMix'
import i18n from '@/i18n/i18n'

// 导入Univer样式
import '@univerjs/design/lib/index.css'
import '@univerjs/ui/lib/index.css'
import '@univerjs/sheets-ui/lib/index.css'
// 导入绘图相关样式
import '@univerjs/drawing-ui/lib/index.css'
import '@univerjs/sheets-drawing-ui/lib/index.css'

// 导入绘图相关的 Facade API
import '@univerjs/sheets-drawing-ui/facade'



export default {
  name: 'UniversalSpreadsheetUniver',
  props: {
    // 数据转换器实例
    converter: {
      type: Object,
      required: true
    },
    // 表单数据
    formData: {
      type: Object,
      default: () => ({})
    },
    // 订单详情列表
    orderDetailList: {
      type: Array,
      default: () => []
    },
    // 服务类型字典
    serviceTypeDict: {
      type: [Array, Object],
      default: () => ({})
    },
    // 表格宽度
    width: {
      type: [String, Number],
      default: '100%'
    },
    // 表格高度
    height: {
      type: [String, Number],
      default: '600px'
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: true
    },
    // 是否显示操作按钮
    showOperations: {
      type: Boolean,
      default: true
    },
    // 自定义配置
    customConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      containerId: '',
      univerInstance: null,
      workbook: null,
      pdfLoading: false,
      excelLoading: false,
      isInitialized: false,
      _isRefreshing: false // 防抖标志，防止重复刷新
    }
  },
  computed: {
    /**
     * 计算容器样式
     * @returns {Object} 样式对象
     */
    containerStyle() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height: typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    }
  },
  watch: {
    /**
     * 监听表单数据变化
     */
    formData: {
      handler() {
        if (this.isInitialized) {
          // 表单数据变化时，清除半静态缓存以确保数据更新
          this.refreshSpreadsheetData({
            formDataChanged: true,
            orderDetailListChanged: false
          })
        }
      },
      deep: true
    },
    /**
     * 监听订单详情列表变化
     */
    orderDetailList: {
      handler() {
        if (this.isInitialized) {
          // 订单详情变化时，强制清除所有缓存以确保动态数据更新
          this.refreshSpreadsheetData({
            forceFullRefresh: true,
            orderDetailListChanged: true
          })
        }
      },
      deep: true
    },
    /**
     * 监听语言变化
     */
    '$i18n.locale': {
      async handler(newLocale) {
        if (this.isInitialized && this.univerInstance) {
          await this.updateUniverLocale(newLocale)
        }
      }
    }
  },
  mounted() {
    this.containerId = `univer-container-${createUniqueString()}`
    this.$nextTick(() => {
      this.initUniver()
    })
  },
  beforeDestroy() {
    this.destroyUniver()
  },
  methods: {
    /**
     * 初始化Univer实例
     */
    async initUniver() {
      try {
        // 确保当前语言包已加载
        await ensureLocaleLoaded(i18n.locale)

        // 根据当前语言设置Univer的locale
        const univerLocale = this.getUniverLocale(i18n.locale)

        // 使用 createUniver 创建实例（preset 模式）
        const { univer, univerAPI } = createUniver({
          locale: univerLocale,
          locales: {
            [LocaleType.ZH_CN]: zhCN,
            [LocaleType.EN_US]: enUS
          },
          presets: [
            UniverSheetsCorePreset({
              container: this.containerId
            })
          ],
          // 添加绘图插件
          plugins: [
            UniverDrawingPlugin,
            UniverDrawingUIPlugin,
            UniverSheetsDrawingPlugin,
            UniverSheetsDrawingUIPlugin
          ]
        })

        this.univerInstance = univer
        this.univerAPI = univerAPI

        // 生成表格数据
        const sheetData = this.generateSpreadsheetDataDirectly()

        console.log('🔍 [DEBUG] 准备传递给Univer的数据结构:', {
          cellData: sheetData.cellData,
          rowCount: sheetData.rowCount,
          columnCount: sheetData.columnCount
        })

        // 转换cellData格式为Univer期望的格式
        const univerCellData = {}
        if (sheetData.cellData) {
          // 检查cellData是数组还是对象格式
          if (Array.isArray(sheetData.cellData)) {
            // 数组格式：转换为对象格式
            sheetData.cellData.forEach(cell => {
              if (cell && typeof cell.r === 'number' && typeof cell.c === 'number') {
                const univerKey = `${cell.r}_${cell.c}`
                // 保留完整的单元格对象，但移除r和c属性
                const { r, c, ...cellWithoutCoords } = cell
                univerCellData[univerKey] = cellWithoutCoords
              }
            })
          } else {
            // 对象格式：直接转换键名
            Object.entries(sheetData.cellData).forEach(([key, cell]) => {
              // 使用行列坐标作为键：row_col
              const univerKey = `${cell.r}_${cell.c}`
              // 保留完整的单元格对象，但移除r和c属性
              const { r, c, ...cellWithoutCoords } = cell
              univerCellData[univerKey] = cellWithoutCoords
            })
          }
        }

        // 调试：检查边框数据是否正确传递
        let borderCellCount = 0;
        Object.values(univerCellData).forEach(cell => {
          if (cell.v && (cell.v.borderTop || cell.v.borderBottom || cell.v.borderLeft || cell.v.borderRight)) {
            borderCellCount++;
            if (borderCellCount <= 3) { // 只打印前3个有边框的单元格
              console.log(`🔍 [DEBUG] 发现有边框的单元格:`, cell.v);
            }
          }
        });

        console.log(`🔍 [DEBUG] 边框数据传递检查: 共发现 ${borderCellCount} 个有边框的单元格`);

        // console.log('🔍 [DEBUG] 转换后的cellData格式:', {
        //   originalCount: Object.keys(sheetData.cellData || {}).length,
        //   convertedCount: Object.keys(univerCellData).length,
        //   borderCellCount: borderCellCount
        //   sampleKeys: Object.keys(univerCellData).slice(0, 5)
        // })
        
        // 调试：检查标题单元格的样式
        Object.entries(univerCellData).forEach(([key, cellData]) => {
          if (cellData.v && typeof cellData.v === 'string' && (cellData.v.includes('汽车定损车辆维修单') || cellData.v.includes('ავტომობილის'))) {
            console.log(`🔍 [DEBUG] 标题单元格 ${key} 样式:`, {
              value: cellData.v,
              style: cellData.s,
              horizontalAlign: cellData.s?.ht,
              fullCellData: cellData
            })
          }
        })

        // 收集边框信息并缓存
        const borderInfo = []
        this._cachedBorderCells = {}

        Object.entries(univerCellData).forEach(([key, cellData]) => {
          const [row, col] = key.split('_').map(Number)
          if (cellData.v && (cellData.v.borderTop || cellData.v.borderBottom || cellData.v.borderLeft || cellData.v.borderRight)) {
            borderInfo.push({
              range: {
                startRow: row,
                endRow: row,
                startColumn: col,
                endColumn: col
              },
              top: cellData.v.borderTop,
              bottom: cellData.v.borderBottom,
              left: cellData.v.borderLeft,
              right: cellData.v.borderRight
            })

            // 缓存边框信息用于后续使用
            if (!this._cachedBorderCells[row]) {
              this._cachedBorderCells[row] = {}
            }
            this._cachedBorderCells[row][col] = {
              v: {
                borderTop: cellData.v.borderTop,
                borderBottom: cellData.v.borderBottom,
                borderLeft: cellData.v.borderLeft,
                borderRight: cellData.v.borderRight
              }
            }
          }
        })

        console.log(`🔍 [DEBUG] 收集到 ${borderInfo.length} 个边框配置`)

        // 创建工作簿 - 使用正确的Univer数据格式
        const workbookData = {
          id: 'workbook-01',
          name: 'Receipt',
          sheetOrder: ['sheet-01'],
          sheets: {
            'sheet-01': {
              id: 'sheet-01',
              name: sheetData.name || 'Sheet1',
              cellData: univerCellData,
              rowCount: sheetData.rowCount || 20,
              columnCount: sheetData.columnCount || 10,
              // 应用行高配置
              rowData: sheetData.rowData || {},
              // 应用列宽配置
              columnData: sheetData.columnData || {},
              // 应用合并单元格配置
              mergeData: sheetData.mergeData || [],
              // 添加边框配置
              borderInfo: borderInfo,
              // 其他配置
              defaultRowHeight: sheetData.defaultRowHeight || 25,
              defaultColumnWidth: sheetData.defaultColumnWidth || 100,
              showGridLines: sheetData.showGridLines !== undefined ? sheetData.showGridLines : 1,
              rightToLeft: sheetData.rightToLeft || 0,
              hidden: sheetData.hidden || 0,
              tabColor: sheetData.tabColor || '',
              zoomRatio: sheetData.zoomRatio || 1
            }
          }
        }

        console.log('🔍 [DEBUG] 完整的工作簿数据:', workbookData)

        // 使用 Facade API 创建工作簿
        this.workbook = this.univerAPI.createWorkbook(workbookData)

        // 监听渲染完成事件，确保数据正确显示
        this.univerAPI.addEvent(this.univerAPI.Event.LifeCycleChanged, async (event) => {
          if (event.stage === this.univerAPI.Enum.LifecycleStages.Rendered) {
            const fWorkbook = this.univerAPI.getActiveWorkbook()
            const fWorksheet = fWorkbook?.getActiveSheet()

            // 插入头部图片
            await this.insertHeaderImage(fWorksheet)

            // 在渲染完成后再次尝试应用边框
            console.log('🔄 渲染完成，尝试重新应用边框')

            // 使用简化的边框应用方法
            await this.applyKnownBorderRegions(fWorkbook, fWorksheet)

            // 检查 CSS 样式覆盖问题
            this.checkCSSOverrides()

            // 作为最后的备选方案，尝试直接 DOM 操作
            setTimeout(() => {
              this.applyBordersViaDOMManipulation()
            }, 500)

            console.log('✅ Univer图片渲染完成')
            this.$emit('rendered')
          }
        })

        this.isInitialized = true
        this.$emit('initialized', this.univerInstance)

        console.log('✅ Univer初始化成功')
      } catch (error) {
        console.error('❌ Univer初始化失败:', error)
        this.$message.error('表格组件初始化失败，请刷新页面重试')
        this.$emit('error', error)
      }
    },

    /**
     * 插入头部图片到指定工作表并设置标题单元格居中对齐
     * @param {Object} fWorksheet Univer工作表实例
     */
    async insertHeaderImage(fWorksheet) {
      try {
        if (!fWorksheet) {
          console.warn('⚠️ 工作表实例无效，无法插入图片')
          return
        }

        const imageUrl = require('@/assets/logo/fileLog.png')
        
        // Insert a cell image into the active worksheet
        const cells = ['A1']
        cells.forEach(async (cell) => {
          const fRange = fWorksheet?.getRange(cell)
          await fRange?.insertCellImageAsync(imageUrl)
          
          // 设置单元格居中对齐
          if (fRange) {
            fRange.setHorizontalAlignment('center')
            fRange.setVerticalAlignment('middle')
          }
        })
        
        // 为标题单元格应用居中对齐（使用Facade API）
        await this.applyTitleCellAlignment(fWorksheet)
        
        console.log('✅ 头部图片插入完成，已设置居中对齐')
      } catch (error) {
        console.error('❌ 插入头部图片失败:', error)
      }
    },

    /**
     * 为标题单元格应用居中对齐
     * @param {Object} fWorksheet Univer工作表实例
     */
    async applyTitleCellAlignment(fWorksheet) {
      try {
        // 直接针对已知的标题单元格位置应用居中对齐
        // 第2行：主标题
        const mainTitleRange = fWorksheet.getRange(1, 0)
        if (mainTitleRange) {
          mainTitleRange.setHorizontalAlignment('center')
          mainTitleRange.setVerticalAlignment('middle')
          console.log('🎯 [DEBUG] 为主标题单元格应用居中对齐')
        }
        
        // 第3行：副标题
        const subTitleRange = fWorksheet.getRange(2, 0)
        if (subTitleRange) {
          subTitleRange.setHorizontalAlignment('center')
          subTitleRange.setVerticalAlignment('middle')
          console.log('🎯 [DEBUG] 为副标题单元格应用居中对齐')
        }
        
        console.log('✅ 标题单元格居中对齐应用完成')
      } catch (error) {
        console.error('❌ 应用标题单元格居中对齐失败:', error)
      }
    },

    /**
     * 获取Univer对应的语言代码
     * @param {string} locale 项目语言代码
     * @returns {string} Univer语言代码
     */
    getUniverLocale(locale) {
      const localeMap = {
        'zh': LocaleType.ZH_CN,
        'zh-CN': LocaleType.ZH_CN,
        'en': LocaleType.EN_US,
        'en-US': LocaleType.EN_US,
        'ru': LocaleType.ZH_CN, // 俄语暂时使用中文
        'ka': LocaleType.ZH_CN  // 格鲁吉亚语暂时使用中文
      }
      return localeMap[locale] || LocaleType.ZH_CN
    },

    /**
     * 更新Univer语言设置
     * @param {string} newLocale 新的语言代码
     */
    async updateUniverLocale(newLocale) {
      try {
        await ensureLocaleLoaded(newLocale)
        const univerLocale = this.getUniverLocale(newLocale)

        if (this.univerInstance && this.univerInstance.getLocaleService) {
          const localeService = this.univerInstance.getLocaleService()
          if (localeService && localeService.setLocale) {
            localeService.setLocale(univerLocale)
            console.log('✅ Univer语言已更新为:', univerLocale)
          }
        }
      } catch (error) {
        console.error('❌ 更新Univer语言失败:', error)
      }
    },

    /**
     * 生成表格数据
     * 使用convertToUniverData方法获取表格模板数据
     * @returns {Object} 包含cellData、rowCount、columnCount的对象
     */
    generateSpreadsheetData() {
      try {
        // 验证转换器
        if (!this.converter) {
          console.error('❌ 转换器无效')
          return this.getEmptySheetData()
        }

        // 验证数据有效性
        if (!this.formData && (!this.orderDetailList || this.orderDetailList.length === 0)) {
          console.warn('⚠️ 表单数据和订单详情列表都为空，生成空表格')
          return this.getEmptySheetData()
        }

        console.log('🔍 [DEBUG] 开始生成表格数据:', {
          converter: this.converter.constructor.name,
          formData: this.formData,
          orderDetailList: this.orderDetailList
        })

        // 检查转换器是否支持convertToUniverData方法
        if (typeof this.converter.convertToUniverData !== 'function') {
          console.error('❌ 转换器不支持convertToUniverData方法')
          return this.getEmptySheetData()
        }

        console.log('🚀 使用convertToUniverData方法获取表格模板数据')
        return this.generateSpreadsheetDataDirectly()
      } catch (error) {
        console.error('❌ 表格数据生成失败:', error)
        this.$message.error('生成表格数据失败，请检查数据格式')
        return this.getEmptySheetData()
      }
    },

    /**
     * 直接生成表格数据
     * 使用convertToUniverData方法获取表格模板数据
     * @returns {Object} 包含cellData、rowCount、columnCount的对象
     */
    generateSpreadsheetDataDirectly() {
      const startTime = performance.now()
      
      try {
        // 验证转换器参数
        console.log('🔍 [DEBUG] 转换器参数:', {
          formData: this.formData,
          orderDetailList: this.orderDetailList,
          serviceTypeDict: this.serviceTypeDict,
          customConfig: this.customConfig
        })

        // 只调用一次convertToUniverData，避免重复调用
        const univerData = this.converter.convertToUniverData(
          this.formData,
          this.orderDetailList,
          {
            serviceTypeDict: this.serviceTypeDict,
            ...this.customConfig
          }
        )
        const sheetConfig = univerData.sheets['sheet-01']
        const spreadsheetData = sheetConfig ? sheetConfig.cellData : []

        console.log('🔍 [DEBUG] 生成的表格数据:', {
          dataType: Array.isArray(spreadsheetData) ? 'Array' : typeof spreadsheetData,
          dataLength: spreadsheetData?.length || 0
        })

        // 验证转换结果
        if (!spreadsheetData) {
          console.error('❌ 转换器返回空数据')
          throw new Error('转换器返回空数据')
        }

        if (!Array.isArray(spreadsheetData)) {
          console.error('❌ cellData应该是数组格式')
          throw new Error('cellData应该是数组格式')
        }

        if (spreadsheetData.length === 0) {
          console.warn('⚠️ 转换器返回的数据为空数组')
          return this.getEmptySheetData()
        }

        // 计算行列数
        let maxRow = 0
        let maxCol = 0
        spreadsheetData.forEach(cell => {
          if (cell && typeof cell.r === 'number' && typeof cell.c === 'number') {
            maxRow = Math.max(maxRow, cell.r)
            maxCol = Math.max(maxCol, cell.c)
          }
        })

        const duration = performance.now() - startTime
        console.log(`✅ 表格数据生成完成，耗时: ${duration.toFixed(2)}ms`)
        console.log(`📊 数据统计: 总单元格数=${spreadsheetData.length}, 最大行=${maxRow}, 最大列=${maxCol}`)
        
        return {
          cellData: spreadsheetData,
          // 使用converter计算的正确行数，而不是基于单元格数据的最大行号
          rowCount: sheetConfig.rowCount || (maxRow + 1),
          columnCount: sheetConfig.columnCount || (maxCol + 1),
          // 应用Univer格式的配置
          rowData: sheetConfig.rowData || {},
          columnData: sheetConfig.columnData || {},
          mergeData: sheetConfig.mergeData || [],
          defaultRowHeight: sheetConfig.defaultRowHeight || 25,
          defaultColumnWidth: sheetConfig.defaultColumnWidth || 100,
          showGridLines: sheetConfig.showGridLines !== undefined ? sheetConfig.showGridLines : 1,
          rightToLeft: sheetConfig.rightToLeft || 0,
          hidden: sheetConfig.hidden || 0,
          tabColor: sheetConfig.tabColor || '',
          zoomRatio: sheetConfig.zoomRatio || 1,
          name: sheetConfig.name || 'Sheet1'
        }
      } catch (error) {
        console.error('❌ 生成表格数据失败:', error)
        throw error
      }
    },







    /**
     * 应用边框样式到 Univer
     * @param {Object} univerCellData 单元格数据
     * @param {Object} fWorkbook 工作簿对象
     * @param {Object} fWorksheet 工作表对象
     */
    async applyBordersToUniver(univerCellData, fWorkbook, fWorksheet) {
      try {
        console.log('🔄 开始应用边框样式到 Univer')

        // 收集所有有边框的单元格
        const borderCells = []
        Object.entries(univerCellData).forEach(([key, cellData]) => {
          const [row, col] = key.split('_').map(Number)
          if (cellData.v && (cellData.v.borderTop || cellData.v.borderBottom || cellData.v.borderLeft || cellData.v.borderRight)) {
            borderCells.push({
              row,
              col,
              borders: {
                top: cellData.v.borderTop,
                bottom: cellData.v.borderBottom,
                left: cellData.v.borderLeft,
                right: cellData.v.borderRight
              }
            })
          }
        })

        console.log(`🔍 [DEBUG] 找到 ${borderCells.length} 个需要应用边框的单元格`)

        // 使用 Univer 的边框设置 API
        for (const cell of borderCells) {
          try {
            // 构建 Univer 边框样式 - 使用正确的格式
            const borderStyle = {}

            if (cell.borders.top) {
              borderStyle.top = {
                style: this.convertBorderStyleToUniver(cell.borders.top.style),
                color: this.convertColorToUniver(cell.borders.top.color),
                width: cell.borders.top.width || 1
              }
            }
            if (cell.borders.bottom) {
              borderStyle.bottom = {
                style: this.convertBorderStyleToUniver(cell.borders.bottom.style),
                color: this.convertColorToUniver(cell.borders.bottom.color),
                width: cell.borders.bottom.width || 1
              }
            }
            if (cell.borders.left) {
              borderStyle.left = {
                style: this.convertBorderStyleToUniver(cell.borders.left.style),
                color: this.convertColorToUniver(cell.borders.left.color),
                width: cell.borders.left.width || 1
              }
            }
            if (cell.borders.right) {
              borderStyle.right = {
                style: this.convertBorderStyleToUniver(cell.borders.right.style),
                color: this.convertColorToUniver(cell.borders.right.color),
                width: cell.borders.right.width || 1
              }
            }

            console.log(`🔍 [DEBUG] 单元格(${cell.row},${cell.col})边框样式:`, borderStyle)

            // 尝试使用样式设置命令应用边框
            try {
              await this.univerAPI.executeCommand('sheet.command.set-range-style', {
                unitId: fWorkbook.getId(),
                subUnitId: fWorksheet.getSheetId(),
                range: {
                  startRow: cell.row,
                  endRow: cell.row,
                  startColumn: cell.col,
                  endColumn: cell.col
                },
                style: {
                  border: borderStyle
                }
              })
              console.log(`✅ 使用样式命令成功应用边框到单元格(${cell.row},${cell.col})`)
            } catch (styleError) {
              console.log(`⚠️ 样式命令失败:`, styleError.message)

              // 回退到边框命令
              try {
                await this.univerAPI.executeCommand('sheet.command.set-border', {
                  unitId: fWorkbook.getId(),
                  subUnitId: fWorksheet.getSheetId(),
                  range: {
                    startRow: cell.row,
                    endRow: cell.row,
                    startColumn: cell.col,
                    endColumn: cell.col
                  },
                  border: borderStyle
                })
                console.log(`✅ 使用边框命令成功应用边框到单元格(${cell.row},${cell.col})`)
              } catch (borderError) {
                console.warn(`❌ 所有边框命令都失败，单元格(${cell.row},${cell.col}):`, borderError.message)
              }
            }

          } catch (error) {
            console.warn(`⚠️ 为单元格(${cell.row},${cell.col})应用边框失败:`, error)
          }
        }

        console.log('✅ 边框样式应用完成')

      } catch (error) {
        console.error('❌ 应用边框样式失败:', error)
      }
    },

    /**
     * 转换边框样式为 Univer 格式
     * @param {string} style 边框样式
     * @returns {number} Univer 边框样式
     */
    convertBorderStyleToUniver(style) {
      const styleMap = {
        'solid': 1,
        'dashed': 2,
        'dotted': 3,
        'double': 4,
        'thin': 1,
        'medium': 2,
        'thick': 3
      }
      return styleMap[style] || 1
    },

    /**
     * 转换颜色为 Univer 格式
     * @param {string} color 颜色值
     * @returns {string} Univer 颜色格式
     */
    convertColorToUniver(color) {
      if (!color) return '#000000'

      // 如果已经是十六进制格式，直接返回
      if (color.startsWith('#')) {
        return color
      }

      // 如果是 rgb 格式，转换为十六进制
      if (color.startsWith('rgb')) {
        const matches = color.match(/\d+/g)
        if (matches && matches.length >= 3) {
          const r = parseInt(matches[0]).toString(16).padStart(2, '0')
          const g = parseInt(matches[1]).toString(16).padStart(2, '0')
          const b = parseInt(matches[2]).toString(16).padStart(2, '0')
          return `#${r}${g}${b}`
        }
      }

      // 默认返回黑色
      return '#000000'
    },

    /**
     * 在渲染完成后重新应用边框
     * @param {Object} fWorkbook 工作簿对象
     * @param {Object} fWorksheet 工作表对象
     */
    async reapplyBordersAfterRender(fWorkbook, fWorksheet) {
      try {
        console.log('🔄 开始在渲染后重新应用边框')

        // 等待一小段时间确保渲染完全完成
        await new Promise(resolve => setTimeout(resolve, 100))

        // 获取当前工作表的所有单元格数据
        let cellData = {}
        try {
          // 尝试不同的方法获取单元格数据
          if (fWorksheet.getCellData) {
            cellData = fWorksheet.getCellData() || {}
          } else if (fWorksheet.getSheetData) {
            const sheetData = fWorksheet.getSheetData()
            cellData = sheetData.cellData || {}
          } else {
            console.warn('⚠️ 无法获取工作表单元格数据，使用缓存的边框信息')
            // 使用之前缓存的边框信息
            cellData = this._cachedBorderCells || {}
          }
        } catch (error) {
          console.warn('⚠️ 获取单元格数据失败，跳过渲染后边框应用:', error.message)
          return
        }

        // 查找有边框数据的单元格（使用缓存或实际数据）
        const borderCells = []

        if (this._cachedBorderCells && Object.keys(this._cachedBorderCells).length > 0) {
          // 使用缓存的边框信息
          Object.entries(this._cachedBorderCells).forEach(([rowKey, rowData]) => {
            Object.entries(rowData).forEach(([colKey, cell]) => {
              const row = parseInt(rowKey)
              const col = parseInt(colKey)

              if (cell.v && (cell.v.borderTop || cell.v.borderBottom || cell.v.borderLeft || cell.v.borderRight)) {
                borderCells.push({
                  row,
                  col,
                  borders: {
                    top: cell.v.borderTop,
                    bottom: cell.v.borderBottom,
                    left: cell.v.borderLeft,
                    right: cell.v.borderRight
                  }
                })
              }
            })
          })
        } else {
          // 尝试从实际数据中获取
          Object.entries(cellData).forEach(([rowKey, rowData]) => {
            Object.entries(rowData || {}).forEach(([colKey, cell]) => {
              const row = parseInt(rowKey)
              const col = parseInt(colKey)

              if (cell && cell.v && (cell.v.borderTop || cell.v.borderBottom || cell.v.borderLeft || cell.v.borderRight)) {
                borderCells.push({
                  row,
                  col,
                  borders: {
                    top: cell.v.borderTop,
                    bottom: cell.v.borderBottom,
                    left: cell.v.borderLeft,
                    right: cell.v.borderRight
                  }
                })
              }
            })
          })
        }

        console.log(`🔍 [DEBUG] 渲染后找到 ${borderCells.length} 个需要重新应用边框的单元格`)

        // 使用强制样式设置
        for (const cell of borderCells) {
          try {
            const borderStyle = {}

            if (cell.borders.top) {
              borderStyle.borderTop = {
                style: this.convertBorderStyleToUniver(cell.borders.top.style),
                color: this.convertColorToUniver(cell.borders.top.color)
              }
            }
            if (cell.borders.bottom) {
              borderStyle.borderBottom = {
                style: this.convertBorderStyleToUniver(cell.borders.bottom.style),
                color: this.convertColorToUniver(cell.borders.bottom.color)
              }
            }
            if (cell.borders.left) {
              borderStyle.borderLeft = {
                style: this.convertBorderStyleToUniver(cell.borders.left.style),
                color: this.convertColorToUniver(cell.borders.left.color)
              }
            }
            if (cell.borders.right) {
              borderStyle.borderRight = {
                style: this.convertBorderStyleToUniver(cell.borders.right.style),
                color: this.convertColorToUniver(cell.borders.right.color)
              }
            }

            // 强制设置样式
            await this.univerAPI.executeCommand('sheet.command.set-range-style', {
              unitId: fWorkbook.getId(),
              subUnitId: fWorksheet.getSheetId(),
              range: {
                startRow: cell.row,
                endRow: cell.row,
                startColumn: cell.col,
                endColumn: cell.col
              },
              style: borderStyle
            })

          } catch (error) {
            console.warn(`⚠️ 渲染后重新应用边框失败，单元格(${cell.row},${cell.col}):`, error)
          }
        }

        console.log('✅ 渲染后边框重新应用完成')

      } catch (error) {
        console.error('❌ 渲染后重新应用边框失败:', error)
      }
    },

    /**
     * 应用已知的边框区域（简化版本）
     * @param {Object} fWorkbook 工作簿对象
     * @param {Object} fWorksheet 工作表对象
     */
    async applyKnownBorderRegions(fWorkbook, fWorksheet) {
      try {
        console.log('🔄 开始应用已知边框区域')

        // 定义已知的边框区域
        const borderRegions = [
          { name: '基本信息区域', startRow: 3, endRow: 6, startCol: 0, endCol: 7 },
          { name: '表格区域', startRow: 8, endRow: 16, startCol: 0, endCol: 7 }
        ]

        const borderStyle = {
          style: 1, // solid
          color: '#000000'
        }

        for (const region of borderRegions) {
          console.log(`🔄 应用边框到 ${region.name}`)

          for (let row = region.startRow; row <= region.endRow; row++) {
            for (let col = region.startCol; col <= region.endCol; col++) {
              try {
                await this.univerAPI.executeCommand('sheet.command.set-range-style', {
                  unitId: fWorkbook.getId(),
                  subUnitId: fWorksheet.getSheetId(),
                  range: {
                    startRow: row,
                    endRow: row,
                    startColumn: col,
                    endColumn: col
                  },
                  style: {
                    borderTop: borderStyle,
                    borderBottom: borderStyle,
                    borderLeft: borderStyle,
                    borderRight: borderStyle
                  }
                })
              } catch (error) {
                // 忽略单个单元格的错误，继续处理其他单元格
              }
            }
          }

          console.log(`✅ ${region.name} 边框应用完成`)
        }

        console.log('✅ 所有已知边框区域应用完成')

      } catch (error) {
        console.error('❌ 应用已知边框区域失败:', error)
      }
    },

    /**
     * 检查 CSS 样式覆盖问题
     */
    checkCSSOverrides() {
      try {
        console.log('🔍 [DEBUG] 开始检查 CSS 样式覆盖问题')

        // 查找 Univer 容器
        const container = document.getElementById(this.containerId)
        if (!container) {
          console.warn('⚠️ 未找到 Univer 容器')
          return
        }

        // 查找单元格元素
        const cells = container.querySelectorAll('[data-row][data-col]')
        console.log(`🔍 [DEBUG] 找到 ${cells.length} 个单元格元素`)

        if (cells.length > 0) {
          // 检查前几个单元格的样式
          for (let i = 0; i < Math.min(5, cells.length); i++) {
            const cell = cells[i]
            const computedStyle = window.getComputedStyle(cell)

            console.log(`🔍 [DEBUG] 单元格 ${i} 的边框样式:`, {
              borderTop: computedStyle.borderTop,
              borderBottom: computedStyle.borderBottom,
              borderLeft: computedStyle.borderLeft,
              borderRight: computedStyle.borderRight,
              border: computedStyle.border
            })
          }
        }

        // 检查是否有全局样式覆盖
        const allStyleSheets = Array.from(document.styleSheets)
        const suspiciousRules = []

        allStyleSheets.forEach((sheet, index) => {
          try {
            const rules = Array.from(sheet.cssRules || sheet.rules || [])
            rules.forEach(rule => {
              if (rule.selectorText && (
                rule.selectorText.includes('univer') ||
                rule.selectorText.includes('cell') ||
                rule.selectorText.includes('border')
              )) {
                if (rule.style && (
                  rule.style.border ||
                  rule.style.borderTop ||
                  rule.style.borderBottom ||
                  rule.style.borderLeft ||
                  rule.style.borderRight
                )) {
                  suspiciousRules.push({
                    sheet: index,
                    selector: rule.selectorText,
                    borderStyles: {
                      border: rule.style.border,
                      borderTop: rule.style.borderTop,
                      borderBottom: rule.style.borderBottom,
                      borderLeft: rule.style.borderLeft,
                      borderRight: rule.style.borderRight
                    }
                  })
                }
              }
            })
          } catch (e) {
            // 跨域样式表无法访问，忽略
          }
        })

        if (suspiciousRules.length > 0) {
          console.warn('⚠️ 发现可能影响边框的 CSS 规则:', suspiciousRules)
        } else {
          console.log('✅ 未发现明显的 CSS 样式覆盖问题')
        }

      } catch (error) {
        console.error('❌ 检查 CSS 样式覆盖失败:', error)
      }
    },

    /**
     * 通过 DOM 操作直接应用边框（备选方案）
     */
    applyBordersViaDOMManipulation() {
      try {
        console.log('🔄 开始通过 DOM 操作直接应用边框')

        const container = document.getElementById(this.containerId)
        if (!container) {
          console.warn('⚠️ 未找到 Univer 容器')
          return
        }

        // 查找所有单元格元素
        const cells = container.querySelectorAll('div[data-row][data-col], .univer-cell, [class*="cell"]')
        console.log(`🔍 [DEBUG] 找到 ${cells.length} 个可能的单元格元素`)

        if (cells.length === 0) {
          // 尝试其他选择器
          const alternativeCells = container.querySelectorAll('div, span, td')
          console.log(`🔍 [DEBUG] 备选查找到 ${alternativeCells.length} 个元素`)

          // 检查前几个元素的属性
          for (let i = 0; i < Math.min(10, alternativeCells.length); i++) {
            const cell = alternativeCells[i]
            console.log(`🔍 [DEBUG] 元素 ${i}:`, {
              className: cell.className,
              dataset: cell.dataset,
              style: cell.style.cssText
            })
          }
        }

        // 应用边框到基本信息区域 (行3-6, 列0-7)
        this.applyDOMBordersToRegion(container, 3, 6, 0, 7, '基本信息区域')

        // 应用边框到表格区域 (行8-16, 列0-7)
        this.applyDOMBordersToRegion(container, 8, 16, 0, 7, '表格区域')

        console.log('✅ DOM 边框应用完成')

      } catch (error) {
        console.error('❌ DOM 边框应用失败:', error)
      }
    },

    /**
     * 为指定区域应用 DOM 边框
     * @param {Element} container 容器元素
     * @param {number} startRow 起始行
     * @param {number} endRow 结束行
     * @param {number} startCol 起始列
     * @param {number} endCol 结束列
     * @param {string} regionName 区域名称
     */
    applyDOMBordersToRegion(container, startRow, endRow, startCol, endCol, regionName) {
      try {
        console.log(`🔄 为 ${regionName} 应用 DOM 边框 (${startRow}-${endRow}, ${startCol}-${endCol})`)

        for (let row = startRow; row <= endRow; row++) {
          for (let col = startCol; col <= endCol; col++) {
            // 尝试多种选择器查找单元格
            const selectors = [
              `[data-row="${row}"][data-col="${col}"]`,
              `[data-row-index="${row}"][data-col-index="${col}"]`,
              `.cell-${row}-${col}`,
              `.r${row}c${col}`
            ]

            let cell = null
            for (const selector of selectors) {
              cell = container.querySelector(selector)
              if (cell) break
            }

            if (cell) {
              // 应用边框样式
              cell.style.border = '1px solid #000000'
              cell.style.borderTop = '1px solid #000000'
              cell.style.borderBottom = '1px solid #000000'
              cell.style.borderLeft = '1px solid #000000'
              cell.style.borderRight = '1px solid #000000'

              console.log(`✅ 为单元格(${row},${col})应用了 DOM 边框`)
            }
          }
        }

      } catch (error) {
        console.error(`❌ 为 ${regionName} 应用 DOM 边框失败:`, error)
      }
    },

    /**
     * 获取空表格数据
     * @returns {Object} 空表格数据
     */
    getEmptySheetData() {
      return {
        cellData: {},
        rowCount: 20,
        columnCount: 10
      }
    },

    /**
     * 刷新表格数据（智能刷新，支持缓存优化）
     * @param {object} options 刷新选项
     */
    async refreshSpreadsheetData(options = {}) {
      if (!this.isInitialized || !this.univerAPI) {
        console.warn('⚠️ Univer未初始化，无法刷新数据')
        return
      }

      // 额外检查：确保 Univer 实例完全可用
      try {
        const testWorkbook = this.univerAPI.getActiveWorkbook()
        if (!testWorkbook) {
          console.warn('⚠️ Univer工作簿未准备就绪，延迟刷新')
          // 延迟100ms后重试
          setTimeout(() => this.refreshSpreadsheetData(options), 100)
          return
        }
      } catch (error) {
        console.error('❌ Univer API调用失败，可能未完全初始化:', error)
        return
      }

      // 防抖机制：如果正在刷新中，跳过本次调用
      if (this._isRefreshing) {
        console.log('🔄 [DEBUG] 正在刷新中，跳过重复调用')
        return
      }
      this._isRefreshing = true

      try {
        console.log('🔄 开始智能刷新表格数据')
        console.log('📝 [DEBUG] 刷新选项:', options)

        // 检查数据变化类型
        const {
          forceFullRefresh = false,
          formDataChanged = false,
          orderDetailListChanged = true // 默认认为订单详情变化了
        } = options

        // 如果强制全量刷新，清除所有缓存
        if (forceFullRefresh && this.converter.clearCache) {
          this.converter.clearCache()
          console.log('🔄 [DEBUG] 强制全量刷新，已清除所有缓存')
        } else if (formDataChanged && this.converter.clearSemiStaticCache) {
          // 如果只是formData变化，清除半静态缓存
          this.converter.clearSemiStaticCache()
          console.log('🔄 [DEBUG] formData变化，已清除半静态缓存')
        }

        // 生成新的表格数据（会自动使用缓存优化）
        const newSheetData = this.generateSpreadsheetData()

        // 使用 univerAPI 获取当前活动的工作簿和工作表
        const fWorkbook = this.univerAPI.getActiveWorkbook()
        if (!fWorkbook) {
          console.error('❌ 无法获取活动工作簿，Univer可能未正确初始化')
          this._isRefreshing = false
          return
        }

        const fWorksheet = fWorkbook.getActiveSheet()
        if (!fWorksheet) {
          console.error('❌ 无法获取活动工作表，工作表可能未正确创建')
          this._isRefreshing = false
          return
        }

        // 验证工作簿和工作表的ID
        const workbookId = fWorkbook.getId()
        const worksheetId = fWorksheet.getSheetId()

        if (!workbookId || !worksheetId) {
          console.error('❌ 工作簿或工作表ID无效', { workbookId, worksheetId })
          this._isRefreshing = false
          return
        }

        console.log('✅ 成功获取工作簿和工作表', { workbookId, worksheetId })

        // 转换cellData格式为Univer期望的格式
        const univerCellData = {}
        const cellDataArray = newSheetData.cellData || []
        
        if (Array.isArray(cellDataArray) && cellDataArray.length > 0) {
          console.log('🔄 开始转换并设置表格数据到Univer')
          
          // 将数组格式转换为Univer的cellData对象格式
          cellDataArray.forEach(cell => {
            if (cell && typeof cell.r === 'number' && typeof cell.c === 'number') {
              const univerKey = `${cell.r}_${cell.c}`
              // 保留完整的单元格对象，但移除r和c属性
              const { r, c, ...cellWithoutCoords } = cell
              univerCellData[univerKey] = cellWithoutCoords
            }
          })

          console.log('🔍 [DEBUG] 刷新时转换的cellData:', {
            originalCount: cellDataArray.length,
            convertedCount: Object.keys(univerCellData).length
          })

          // 清空现有数据
           const fRange = fWorksheet.getRange(0, 0, newSheetData.rowCount, newSheetData.columnCount)
           fRange.clearContent()
           
           // 使用Univer命令API批量设置单元格数据
           const setCellCommands = []
           Object.entries(univerCellData).forEach(([key, cellData]) => {
             const [row, col] = key.split('_').map(Number)

             // 再次验证工作簿和工作表对象的有效性
             if (!fWorkbook || !fWorksheet) {
               console.error('❌ 工作簿或工作表对象在处理过程中变为null')
               return
             }

             const currentWorkbookId = fWorkbook.getId()
             const currentWorksheetId = fWorksheet.getSheetId()

             if (!currentWorkbookId || !currentWorksheetId) {
               console.error('❌ 工作簿或工作表ID在处理过程中变为无效', {
                 currentWorkbookId,
                 currentWorksheetId
               })
               return
             }

             setCellCommands.push({
               unitId: currentWorkbookId,
               subUnitId: currentWorksheetId,
               range: {
                 startRow: row,
                 endRow: row,
                 startColumn: col,
                 endColumn: col
               },
               value: cellData
             })
           })
           
           // 批量执行设置单元格命令
           if (setCellCommands.length > 0) {
             console.log(`🔄 准备执行 ${setCellCommands.length} 个单元格设置命令`)

             for (const command of setCellCommands) {
               try {
                 // 再次验证命令参数的有效性
                 if (!command.unitId || !command.subUnitId) {
                   console.error('❌ 命令参数无效，跳过此单元格', command)
                   continue
                 }

                 // 构建完整的样式对象，包括边框
                 const cellStyle = command.value.s || {}

                 // 如果单元格有边框数据，添加到样式中
                 if (command.value.v && (command.value.v.borderTop || command.value.v.borderBottom || command.value.v.borderLeft || command.value.v.borderRight)) {
                   // 使用 Univer 标准的边框格式
                   const borderData = {}

                   if (command.value.v.borderTop) {
                     borderData.borderTop = {
                       style: this.convertBorderStyleToUniver(command.value.v.borderTop.style),
                       color: this.convertColorToUniver(command.value.v.borderTop.color)
                     }
                   }
                   if (command.value.v.borderBottom) {
                     borderData.borderBottom = {
                       style: this.convertBorderStyleToUniver(command.value.v.borderBottom.style),
                       color: this.convertColorToUniver(command.value.v.borderBottom.color)
                     }
                   }
                   if (command.value.v.borderLeft) {
                     borderData.borderLeft = {
                       style: this.convertBorderStyleToUniver(command.value.v.borderLeft.style),
                       color: this.convertColorToUniver(command.value.v.borderLeft.color)
                     }
                   }
                   if (command.value.v.borderRight) {
                     borderData.borderRight = {
                       style: this.convertBorderStyleToUniver(command.value.v.borderRight.style),
                       color: this.convertColorToUniver(command.value.v.borderRight.color)
                     }
                   }

                   // 将边框数据添加到样式对象
                   Object.assign(cellStyle, borderData)

                   console.log(`🔍 [DEBUG] 为单元格(${command.range.startRow},${command.range.startColumn})添加边框样式:`, borderData)
                 }

                 await this.univerAPI.executeCommand('sheet.command.set-range-values', {
                   unitId: command.unitId,
                   subUnitId: command.subUnitId,
                   range: command.range,
                   value: command.value.v || command.value,
                   style: cellStyle
                 })
               } catch (error) {
                 console.error('❌ 执行单元格设置命令失败:', error, command)
                 // 继续执行其他命令，不中断整个过程
               }
             }
             console.log('✅ 表格数据刷新完成')

             // 应用边框样式（使用 Univer 的边框 API）
             await this.applyBordersToUniver(univerCellData, fWorkbook, fWorksheet)

             // 注意：Univer中没有直接的recalculate命令，数据更新后会自动重新渲染
             
             // 智能图片处理：只有在静态区域可能受影响时才重新插入图片
             if (forceFullRefresh || !this.converter.staticSectionsCache) {
               console.log('🖼️ [DEBUG] 重新插入图片（静态区域变化）')
               await this.insertHeaderImage(fWorksheet)
             } else {
               console.log('🖼️ [DEBUG] 跳过图片插入（静态区域未变化，使用缓存）')
             }
             
             // 强制视图更新
             this.$nextTick(() => {
               this.$forceUpdate()
               console.log('🔄 强制Vue组件更新完成')
             })
           }
        } else {
          console.warn('⚠️ cellData不是数组格式或为空:', cellDataArray)
        }

        this.$emit('refreshed')
        console.log('✅ Univer数据刷新成功')
      } catch (error) {
        console.error('❌ 刷新Univer数据失败:', error)
        this.$emit('error', error)
      } finally {
        // 重置刷新状态
        this._isRefreshing = false
      }
    },

    /**
     * 导出为PDF
     */
    async exportToPDF() {
      this.pdfLoading = true
      try {
        const container = document.getElementById(this.containerId)
        if (!container) {
          throw new Error('找不到容器元素')
        }

        const canvas = await html2canvas(container, {
          scale: 2,
          useCORS: true,
          allowTaint: true
        })

        const imgData = canvas.toDataURL('image/png')
        const pdf = new jsPDF('landscape', 'mm', 'a4')

        const imgWidth = 297
        const pageHeight = 210
        const imgHeight = (canvas.height * imgWidth) / canvas.width
        let heightLeft = imgHeight
        let position = 0

        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
        heightLeft -= pageHeight

        while (heightLeft >= 0) {
          position = heightLeft - imgHeight
          pdf.addPage()
          pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight)
          heightLeft -= pageHeight
        }

        const fileName = `表格_${this.formatDate(new Date())}.pdf`
        pdf.save(fileName)

        this.$message.success('PDF导出成功')
      } catch (error) {
        console.error('PDF导出失败:', error)
        this.$message.error('PDF导出失败，请重试')
      } finally {
        this.pdfLoading = false
      }
    },

    /**
     * 导出为Excel
     */
    async exportToExcel() {
      this.excelLoading = true
      try {
        if (!this.workbook) {
          throw new Error('工作簿未初始化')
        }

        // 使用Univer的导出功能
        const activeSheet = this.workbook.getActiveSheet()
        if (!activeSheet) {
          throw new Error('无法获取活动工作表')
        }

        // 获取表格数据
        const data = []
        const range = activeSheet.getUsedRange()

        if (range) {
          for (let row = range.startRow; row <= range.endRow; row++) {
            const rowData = []
            for (let col = range.startColumn; col <= range.endColumn; col++) {
              const cell = activeSheet.getCell(row, col)
              rowData.push(cell ? cell.getValue() : '')
            }
            data.push(rowData)
          }
        }

        // 使用xlsx库导出
        const XLSX = await import('xlsx')
        const ws = XLSX.utils.aoa_to_sheet(data)
        const wb = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(wb, ws, 'Sheet1')

        const fileName = `表格_${this.formatDate(new Date())}.xlsx`
        XLSX.writeFile(wb, fileName)

        this.$message.success('Excel导出成功')
      } catch (error) {
        console.error('Excel导出失败:', error)
        this.$message.error('Excel导出失败，请重试')
      } finally {
        this.excelLoading = false
      }
    },

    /**
     * 打印表格
     */
    printSpreadsheet() {
      try {
        const container = document.getElementById(this.containerId)
        if (!container) {
          this.$message.error('找不到表格容器')
          return
        }

        // 创建打印窗口
        const printWindow = window.open('', '_blank')
        if (!printWindow) {
          this.$message.error('无法打开打印窗口，请检查浏览器设置')
          return
        }

        // 获取表格HTML内容
        const tableHTML = container.innerHTML

        // 构建打印页面
        const printHTML = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>表格打印</title>
            <style>
              @page {
                size: A4 landscape;
                margin: 1.9cm;
              }
              body {
                margin: 0;
                padding: 0;
                font-family: 'Microsoft YaHei', Arial, sans-serif;
              }
              .print-container {
                width: 100%;
                height: auto;
              }
              @media print {
                body { -webkit-print-color-adjust: exact; }
              }
            </style>
          </head>
          <body>
            <div class="print-container">
              ${tableHTML}
            </div>
          </body>
          </html>
        `

        printWindow.document.write(printHTML)
        printWindow.document.close()

        // 等待内容加载完成后打印
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
        }, 500)

      } catch (error) {
        console.error('打印失败:', error)
        this.$message.error('打印失败，请重试')
      }
    },

    /**
     * 刷新数据
     */
    refreshData() {
      this.refreshSpreadsheetData()
      this.$emit('refresh')
    },

    /**
     * 销毁Univer实例
     */
    destroyUniver() {
      if (this.univerInstance) {
        try {
          this.univerInstance.dispose()
          this.univerInstance = null
          this.workbook = null
          this.isInitialized = false
          console.log('✅ Univer实例已销毁')
        } catch (error) {
          console.error('❌ 销毁Univer实例失败:', error)
        }
      }
    },

    /**
     * 重新初始化
     */
    reinitialize() {
      this.destroyUniver()
      this.$nextTick(() => {
        this.initUniver()
      })
    },

    /**
     * 格式化日期
     * @param {Date} date 日期对象
     * @returns {String} 格式化后的日期字符串
     */
    formatDate(date) {
      if (!date) return ''
      const d = new Date(date)
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`
    },

    /**
     * 获取Univer实例
     * @returns {Object|null} Univer实例
     */
    getSpreadsheetInstance() {
      return this.univerInstance
    },

    /**
     * 获取工作簿实例
     * @returns {Object|null} 工作簿实例
     */
getWorkbook() {
       return this.workbook
     }
   }
 }
</script>

<style scoped>
.universal-spreadsheet-univer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  gap: 8px;
  flex-wrap: wrap;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background: #dcdfe6;
  margin: 0 4px;
}

.univer-container {
  flex: 1;
  min-height: 400px;
  position: relative;
  overflow: hidden;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #fef0f0;
  color: #f56c6c;
  font-size: 14px;
  text-align: center;
}

.error-container .error-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}

.error-container .error-message {
  margin-bottom: 16px;
  max-width: 80%;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    padding: 6px 8px;
  }

  .toolbar-group {
    gap: 4px;
  }

  .univer-container {
    min-height: 300px;
  }
}

/* Univer样式覆盖 */
.univer-container :deep(.univer-workbook) {
  width: 100% !important;
  height: 100% !important;
}

/* 移除工作表外边框，但保留单元格边框 */
.univer-container :deep(.univer-sheet-container) {
  border: none !important;
}

.univer-container :deep(.univer-toolbar) {
  border-bottom: 1px solid #e4e7ed !important;
}

/* 打印样式 */
@media print {
  .toolbar {
    display: none !important;
  }

  .universal-spreadsheet-univer {
    border: none !important;
    height: auto !important;
  }

  .univer-container {
    min-height: auto !important;
    height: auto !important;
  }
}
</style>
