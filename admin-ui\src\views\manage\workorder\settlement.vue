<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item :label="strConvert('工单编号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('工单标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入工单标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户编号')" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          :placeholder="strConvert('请输入客户编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户姓名')" prop="customerNickName">
        <el-input
          v-model="queryParams.customerNickName"
          :placeholder="strConvert('请输入客户姓名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('车牌号')" prop="carPlateNumber">
        <el-input
          v-model="queryParams.carPlateNumber"
          :placeholder="strConvert('请输入车牌号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="strConvert('开始日期')"
          :end-placeholder="strConvert('结束日期')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{strConvert('搜索')}}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{strConvert('重置')}}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="24">
       <span style="color: red">{{ strConvertLangeTitle3() }}</span>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="workorderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('工单编号')"  width="160"  align="center" prop="serviceId" />
      <el-table-column :label="strConvert('工单标题')"  width="180"  align="center" prop="title" />
      <el-table-column :label="strConvert('客户姓名')" width="100" align="center" prop="customerNickName" />
      <el-table-column :label="strConvert('总金额')" width="100" align="center" prop="totalAmount" />
      <el-table-column :label="strConvert('折后金额')" width="100" align="center" prop="discountAmount" />
      <el-table-column :label="strConvert('预收款')" width="100" align="center" prop="advancePayment" />
      <el-table-column :label="strConvert('折扣后含税金额')" width="100" align="center" prop="balancePayment" />
      <el-table-column :label="strConvert('结算金额')" width="100" align="center" prop="settlementPayment" />
<!--      <el-table-column :label="strConvert('消耗时间')" align="center" prop="costTime" />-->
<!--      <el-table-column :label="strConvert('图片')" align="center" width="180" prop="images">
        <template slot-scope="scope">
          <image-preview :src="scope.row.images" :width="50" :height="50"/>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('问题描述')" align="center" prop="comment" width="200"/>
      <el-table-column :label="strConvert('操作')" align="center" width="250" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div v-if="isValidString(scope.row.settlementPayment)">
            <el-button
              type="info"
              icon="el-icon-s-check"
              @click="goPrint(scope.row)"
            >{{strConvert('结算单打印')}}</el-button>
            <el-button
              type="text"
              icon="el-icon-circle-check"
              @click="gotSInfo(scope.row)"
            >{{strConvert('工单完结')}}</el-button>
<!--            <el-button
              type="text"
              icon="el-icon-s-opportunity"
              @click="gotSInfo(scope.row)"
            >{{ strConvert('查看') }}</el-button>-->
          </div>
          <div v-else>
            <el-button
              type="warning"
              icon="el-icon-s-finance"
              @click="gotSettlement(scope.row)"
            >{{strConvert('去结算')}}</el-button>
          </div>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <receipt show="settlement" :serviceId="serviceId" printType="settlementReceipt" :OpenPrint.sync="printOpen" />

  </div>
</template>

<script>
import { strConvert, strConvertLangeTitle3 } from '@/i18n/i18nMix'
import { sprintf } from 'sprintf-js'
import {
  delWorkorder,
  endWorkorder,
  getWorkorder,
  listWorkorder,
  settlementWorkorder,
  updateWorkorder
} from '@/api/manage/workorder'
import { getInfo } from '@/api/login'
import settlementReceipt from '@/views/receipt/settlementReceipt'
import { isValidString } from '@/utils'
import Receipt from '@/views/receipt/components/index.vue'
export default {
  name: "settlement",
  dicts: ['t_work_question_service_type'],
  components: {
    Receipt,
    settlementReceipt
  },
  data() {
    return {
      // 遮罩层
      loading: true,


      // 选中数组
      ids: [],
      // 子表选中数据
      checkedTWorkOrderDetail: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      restCustomer: [],
      serviceId:"",
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单记录表格数据
      workorderList: [],
      // 工单详情表格数据
      tWorkPartsDetailList: [],
      tWorkOrderQuestionDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      printOpen:false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceId: null,
        title: null,
        businessType: null,
        carPlateNumber:null,
        customerId: null,
        customerNickName: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        balancePayment: [
          { required: true, message: this.strConvert('折扣后含税金额不能为空'), trigger: "blur" }
        ],
      },
      queryListParts:null,
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvertLangeTitle3,
    strConvert,
    isValidString,
    /** 查询工单记录列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      this.queryParams.businessType="4"
      listWorkorder(this.queryParams).then(response => {
        this.workorderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        serviceId: null,
        title: null,
        businessType: "0",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        phonenumber:null,
        advancePayment:null,
        balancePayment:null,
        expectedTime:null,
        comment:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        fastRemark:null
      }
      this.tWorkPartsDetailList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.serviceId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = this.strConvert('添加工单')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const serviceId = row.serviceId || this.ids
      getWorkorder(serviceId).then(response => {
        this.form = response.data
        if(response.data.tWorkPartsDetailList!==undefined){
          this.tWorkPartsDetailList = response.data.tWorkPartsDetailList
        }
        this.open = true
        this.title = this.strConvert('修改工单记录')
      })
    },
    /** 去结算 */
    gotSettlement(row) {
      const serviceId = row.serviceId || this.ids
      this.$router.push(`/manage/settlementForm/settlement/`+serviceId);
    },
    /** 去查看 */
    gotSInfo(row) {
      const serviceId = row.serviceId || this.ids
      this.$router.push(`/manage/infoForm/info/`+serviceId);
    },
    goPrint(row){
      this.serviceId = row.serviceId
      this.printOpen = true
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const serviceIds = row.serviceId || this.ids
      this.$modal.confirm(this.sprintf(this.strConvert('是否确认删除工单记录编号为 %s 的数据项？'),serviceIds)).then(function() {
        return delWorkorder(serviceIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess(this.strConvert('删除成功'))
      }).catch(() => {})
    },
	/** 工单详情序号 */
    rowTWorkOrderDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },

  }
}
</script>
