# 电话和颜色字段布局修复

## 问题描述

用户反馈在CarDamageConverter.js中：
1. `ტელეფონი电话:` 字段跨行失败
2. `ფერი/ 颜色:` 字段不需要跨行，应该在电话字段后面依次显示

## 原始配置问题

在修复前，第三行的布局配置为：
- 电话标签和值都跨两列显示
- 颜色标签和值也都跨两列显示
- 车牌号标签和值跨两列显示

这导致颜色字段占用了过多的列空间，与用户期望的布局不符。

## 解决方案

### 1. 修改createBasicInfoSection方法

调整第三行的单元格创建逻辑：
```javascript
// 修改前
basicInfoCells.push(this.createCell(startRow + 2, 6, formData.color || '', { ht: 1, vt: 1 }))

// 修改后
basicInfoCells.push(this.createCell(startRow + 2, 5, formData.color || '', { ht: 1, vt: 1 }))
```

### 2. 修改getMergeConfig方法

更新第三行的合并配置：
```javascript
// 删除颜色字段的跨列配置
'5_4_5_5': { r: 5, c: 4, rs: 1, cs: 2 },  // 颜色标签跨两列（已删除）
'5_6_5_7': { r: 5, c: 6, rs: 1, cs: 2 },  // 颜色值跨两列（已删除）

// 保留电话和车牌号的跨列配置
'5_0_5_1': { r: 5, c: 0, rs: 1, cs: 2 },  // 电话标签跨两列
'5_2_5_3': { r: 5, c: 2, rs: 1, cs: 2 },  // 电话值跨两列
'5_8_5_9': { r: 5, c: 8, rs: 1, cs: 2 },  // 车牌号标签跨两列
'5_10_5_11': { r: 5, c: 10, rs: 1, cs: 2 }, // 车牌号值跨两列
```

## 修复效果

修复后的第三行布局：
- **电话字段**：标签和值都跨两列显示（列0-1和列2-3）
- **颜色字段**：标签和值各占一列（列4和列5）
- **车牌号字段**：标签和值都跨两列显示（列8-9和列10-11）

## 技术要点

1. **列位置调整**：将颜色值的列位置从第6列调整到第5列
2. **合并配置移除**：删除颜色字段的跨列合并配置
3. **布局优化**：确保字段在行内紧凑排列，提高空间利用率

## 验证

修复已在开发服务器中验证通过，电话字段正常跨列显示，颜色字段按单列显示。