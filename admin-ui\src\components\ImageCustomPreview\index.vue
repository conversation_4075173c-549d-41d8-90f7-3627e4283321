<template>
  <div class="image-container">
    <div class="images">
      <img
        v-for="(src, index) in realSrcList"
        :key="index"
        :src="src"
        :style="`width:${realWidth};height:${realHeight};max-width:100%;`"
        class="print-image"
      >
    </div>
  </div>
</template>

<script>
import 'viewerjs/dist/viewer.css'
import Viewer from 'viewerjs'
import { isExternal } from '@/utils/validate'


export default {
  name: "ImageCustomPreview",
  props: {
    src: {
      type: String,
      default: ""
    },
    width: {
      type: [Number, String],
      default: ""
    },
    height: {
      type: [Number, String],
      default: ""
    }
  },
  computed: {
    realSrc() {
      if (!this.src) {
        return
      }
      let real_src = this.src.split(",")[0]
      if (isExternal(real_src)) {
        return real_src
      }
      return process.env.VUE_APP_BASE_API + real_src
    },
    realSrcList() {
      if (!this.src) {
        return
      }
      let real_src_list = this.src.split(",")
      let srcList = []
      real_src_list.forEach(item => {
        if (isExternal(item)) {
          return srcList.push(item)
        }
        return srcList.push(process.env.VUE_APP_BASE_API + item)
      })
      return srcList
    },
    realWidth() {
      return typeof this.width == "string" ? this.width : `${this.width}px`
    },
    realHeight() {
      return typeof this.height == "string" ? this.height : `${this.height}px`
    }
  },
  mounted() {
    this.initViewer();
  },
  methods: {
    initViewer() {
      if (this.viewer) {
        this.viewer.destroy();
      }
      this.viewer = new Viewer(this.$el, {
        toolbar: {
          zoomIn: 1,
          zoomOut: 1,
          oneToOne: 1,
          reset: 1,
          prev: 1,
          play: 0,
          next: 1,
          rotateLeft: 1,
          rotateRight: 1,
          flipHorizontal: 1,
          flipVertical: 1,
        },
      });
    }
  },
  beforeDestroy() {
    if (this.viewer) {
      this.viewer.destroy();
    }
  },
  watch: {
    src() {
      this.$nextTick(() => {
        this.initViewer();
      });
    }
  }
}
</script>
<style scoped>
.images{
  text-align: left;
}

.image-container {
  page-break-inside: avoid;
  break-inside: avoid;
  /*display: block;*/
  margin-right: 2mm;
}

.print-image {
  page-break-inside: avoid !important;
  break-inside: avoid !important;
  max-width: 100% !important;
  height: auto !important;
 /* display: block;*/
  margin-right: 2mm;
}
</style>
