<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('申请单号')" prop="resRequestId">
        <el-input
          v-model="queryParams.resRequestId"
          :placeholder="strConvert('请输入申请单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('物件类型')" prop="resType">
        <el-select v-model="queryParams.resType" :placeholder="strConvert('请选择物件类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_res_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item :label="strConvert('工单号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-plus"

          @click="handleAdd"
          v-hasPermi="['manage:request:add']"
        >{{ strConvert('创建申请') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"

          icon="el-icon-edit"

          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['manage:request:edit']"
        >{{ strConvert('修改') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['manage:request:remove']"
        >{{ strConvert('删除') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-download"
          @click="handleExport"
          v-hasPermi="['manage:request:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      ref="mainTable"
      v-loading="loading"
      :data="requestList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column :label="strConvert('申请单号')" align="center" prop="resRequestId" />
      <el-table-column :label="strConvert('标题')" align="center" prop="title" />
      <el-table-column :label="strConvert('物件类型')" align="center" prop="resType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_res_type" :value="scope.row.resType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('审批状态')" align="center" width="80" prop="approvalStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_approval_status" :value="scope.row.approvalStatus"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('工单号')" align="center" prop="serviceId" />
      <el-table-column :label="strConvert('申请人')" align="center" width="90" prop="createBy" />
<!--      <el-table-column :label="strConvert('描述')" align="center" prop="remark" />-->
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('审批意见')" align="center" prop="approvalComments" width="100"/>
      <el-table-column :label="strConvert('审批时间')" align="center" prop="approvalTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('总费用')" align="center" prop="amount" />-->
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div v-if="scope.row.approvalStatus==1">
            <el-button
              size="medium"
              type="info"
              icon="el-icon-info"
              @click="handleDetailUpdate(scope.row)"
              v-hasPermi="['manage:request:edit']"
            >{{ strConvert('查看详情') }}</el-button>
          </div>
          <div v-else>
            <el-button
              size="medium"
              type="warning"
              icon="el-icon-caret-top"
              @click="submitRequest(scope.row)"
              v-hasPermi="['manage:request:edit']"
            >{{ strConvert('提交申请') }}</el-button>
            <el-button
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['manage:request:edit']"
            >{{ strConvert('修改') }}</el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['manage:request:remove']"
            >{{ strConvert('删除') }}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 物件申请详情对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="850px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="10" class="mb8">
          <el-form-item :label="strConvert('标题')" prop="title">
            <el-input v-model="form.title" :placeholder="strConvert('请输入标题')" />
          </el-form-item>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('物件类型')" prop="resType">
              <el-select v-model="form.resType" :placeholder="strConvert('请选择物件类型')">
                <el-option
                  v-for="dict in dict.type.t_res_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.resType === '2'" :label="strConvert('工单号')" prop="serviceId">
              <el-select
                style="width: 180px"
                v-model="form.serviceId"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入工单号')"
                :remote-method="querySearchWorkOrder"
                @change="handleWorkOrder"
                :loading="loading">
                <el-option
                  v-for="item in queryList"
                  :key="item.id"
                  :label="item.id"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('图像集')" prop="images">
              <image-upload
                :enable-image-compress="true"
                :max-image-size="100"
                :image-quality="0.7"
                :max-image-width="800"
                :max-image-height="800"
                v-model="form.images"
              />
            </el-form-item>
            <!--        <el-form-item :label="strConvert('总费用')" prop="amount">
                      <el-input v-model="form.amount" disabled />
                    </el-form-item>-->
          </el-col>
          <el-col :span="12">
            <el-form-item :label="strConvert('描述')" prop="comment">
              <el-input v-model="form.comment" rows="6" type="textarea" :placeholder="strConvert('请输入内容')" />
            </el-form-item>
          </el-col>
          <el-col :span="1.5">
          </el-col>
        </el-row>


        <el-divider content-position="center">物料申请单详情信息</el-divider>
        <el-row  :gutter="10" class="mb8">
          <el-col :span="1.5" >
            <el-button type="primary" icon="el-icon-plus"  @click="handleAddTResRequestDetail">{{ strConvert('添加') }}</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete"  @click="handleDeleteTResRequestDetail">{{ strConvert('删除') }}</el-button>
          </el-col>
        </el-row>
        <el-table :data="tResRequestDetailList" :row-class-name="rowTResRequestDetailIndex" @selection-change="handleTResRequestDetailSelectionChange" ref="TResRequestDetail">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column :label="strConvert('序号')" align="center" prop="index" width="50"/>
          <el-table-column v-if="form.resType === '1'" :label="strConvert('物料编号')" prop="materialId" width="100"/>
          <el-table-column v-if="form.resType === '1'" :label="strConvert('物料名称')" prop="materialName" width="180">
            <template slot-scope="scope">
              <el-select

                v-model="scope.row.materialName"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入物料名称')"
                :remote-method="querySearchMaterial"
                @change="(val) => handleSelectMaterial(val, scope.row)"
                :loading="loading">
                <el-option
                  v-for="item in queryListMaterial"
                  :key="item.id"
                  :style="item.num>0?'':'color: red'"
                  :label="item.value+' 库存'+item.num"
                  :value="item.id">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column v-if="form.resType === '2'" :label="strConvert('配件编号')" prop="partsId" width="100"/>
          <el-table-column v-if="form.resType === '2'" :label="strConvert('配件名称')" prop="partsName" width="180">
            <template slot-scope="scope">
              <el-select

                v-model="scope.row.partsName"
                filterable  remote reserve-keyword
                :placeholder="strConvert('请输入配件名称')"
                :remote-method="querySearchParts"
                @change="(val) => handleSelectParts(val, scope.row)"
                :loading="loading">
                <el-option
                  v-for="item in queryListParts"
                  :key="item.id"
                  :style="item.num>0?'':'color: red'"
                  :label="item.value+' 库存'+item.num"
                  :value="item.id">
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column v-if="form.resType === '2'"   :label="strConvert('规格')" prop="norm" width="130"/>
          <el-table-column  :label="strConvert('数量')" prop="num" width="90">
            <template slot-scope="scope">
              <el-input  v-model="scope.row.num" :placeholder="strConvert('请输入数量')" type="number" min="1" width="100"/>
            </template>
          </el-table-column>
          <el-table-column :label="strConvert('说明')" prop="comment" width="200">
            <template slot-scope="scope">
              <el-input v-model="scope.row.comment" rows="2" type="textarea" :placeholder="strConvert('请输入说明')" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button  type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import {
  listRequest,
  getRequest,
  delRequest,
  addRequest,
  updateRequest,
  batchTResRequestDetail,
  detailDeleteByIds
} from '@/api/manage/request'
import { listMaterial } from "@/api/manage/material"
import { batchAddParts, listParts, listPartsJoinPurchase, updateParts } from '@/api/manage/parts'
import { getWorkorder, listWorkorder } from '@/api/manage/workorder'
import { getPurchase } from '@/api/manage/purchase'
import { formatDate, isValidString } from '@/utils'
import { getInfo } from '@/api/login'


export default {
  name: "Request",
  dicts: ['t_approval_status', 't_res_type','t_parts_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      queryMaterialList: [],
      queryPartsList: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,

      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物件申请表格数据
      requestList: [],
      // 物件申请详情表格数据
      tResRequestDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层
      detailOpen: false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        resRequestId: null,
        title: null,
        resType: null,
        approvalStatus: null,
        serviceId: null,
        approvalComments: null,
        approvalTime: null,
        createBy: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        resType: [
          { required: true, message: "物件类型不能为空", trigger: "change" }
        ],
      },
      queryList:null,
      queryListParts:null,
      queryListMaterial:null,
      resRequestId:null, //父级id
      restParts:{},
      restMaterials:{},
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询物件申请列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      this.queryParams.createBy=this.$store.getters.name;
      this.queryParams.approvalStatus="3";
      listRequest(this.queryParams).then(response => {
        this.requestList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        resRequestId: null,
        title: null,
        resType: '1',
        operatorType: null,
        approvalStatus: null,
        images: null,
        serviceId: null,
        approvalName: null,
        approvalComments: null,
        approvalTime: null,
        amount: 0.00,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        comment: null
      }
      this.tResRequestDetailList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.resRequestId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加物件申请"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.resRequestId = row.resRequestId || this.ids
      getRequest(this.resRequestId).then(response => {
        this.form = response.data
        this.tResRequestDetailList = response.data.tResRequestDetailList
        this.open = true
        this.title = "修改物件申请"
      })
    },
    /** 编辑详情操作 */
    handleDetailUpdate(row) {
      this.resRequestId = row.resRequestId || this.ids
      getRequest(this.resRequestId).then(response => {
        response.data.tResRequestDetailList.forEach(item => {
          item.materialId = item.materialId != null ? String(item.materialId) : "";
          item.partsId = item.partsId != null ? String(item.partsId) : "";
        });
        this.tResRequestDetailList = response.data.tResRequestDetailList
        this.detailOpen=true;
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.tResRequestDetailList = this.tResRequestDetailList
          if (this.form.resRequestId != null) {
            updateRequest(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addRequest(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 提交申请 */
    submitRequest(row) {
      const resRequestIds = row.resRequestId || this.ids
      this.$modal.confirm('是否确认提交编号为"' + resRequestIds + '"的申请单进行审批？').then(() => {
          const rowData=row;
          if (rowData.resRequestId != null) {
            getRequest(rowData.resRequestId).then(response => {
              this.tResRequestDetailList = response.data.tResRequestDetailList
              if (this.tResRequestDetailList != null) {
                rowData.updateBy=this.$store.getters.name;
                rowData.updateTime=formatDate(new Date());
                rowData.approvalStatus="0"
                updateRequest(rowData).then(response => {
                  this.$modal.msgSuccess("修改成功")
                  this.open = false
                  this.getList()
                  this.reset()
                })
              } else {
                this.$modal.msgError("请重新检查申请清单后，确保数据完整后再试")
              }
            });
          }
      }).catch(() => {})
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.resRequestId = row.resRequestId || this.ids
      var that=this;
      this.$modal.confirm('是否确认删除物件申请编号为"' + resRequestIds + '"的数据项？').then(function() {
        return delRequest(that.resRequestId)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 采购单详情序号 */
    rowTResRequestDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 详情添加按钮操作 */
    handleAddTResRequestDetail() {
      let obj = {}
      obj.resType = ""
      obj.materialId = ""
      obj.materialName = ""
      obj.partsId = ""
      obj.partsName = ""
      obj.partsType = ""
      obj.comment = ""
      obj.images = ""
      obj.norm = ""
      obj.num=1
      obj.amount =0.00
      obj.currencyType = "GEL"
      this.tResRequestDetailList.push(obj)
    },
    /** 采购单详情删除按钮操作 */
    handleDeleteTResRequestDetail() {
      if (this.checkedTResRequestDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的采购单详情数据")
      } else {
        const tResRequestDetailList = this.tResRequestDetailList
        const checkedTResRequestDetail = this.checkedTResRequestDetail
        this.tResRequestDetailList = tResRequestDetailList.filter(function(item) {
          return checkedTResRequestDetail.indexOf(item.index) == -1
        })
      }
    },
    /** 复选框选中数据 */
    handleTResRequestDetailSelectionChange(selection) {
      this.checkedTResRequestDetail = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/request/export', {
        ...this.queryParams
      }, `request_${new Date().getTime()}.xlsx`)
    },
    handleWorkOrder(serviceId){
      if(isValidString(serviceId)){
        getWorkorder(serviceId).then(response => {
          this.tResRequestDetailList = response.data.tWorkPartsDetailList
        });
      }
      this.queryList=[]
    },
    /* 查找工单信息 */
    querySearchWorkOrder(queryString) {
      if (queryString === '') {
        this.queryList = []; // 输入为空时清空历史数据
        return;
      }
      if(queryString!==""){
        listWorkorder({"serviceId":queryString }).then(response => {
          if(response.rows.length>0){
            this.queryList = response.rows.map(item=>{
              return {"value":item.title,"id":item.serviceId}
            });
          }
        })
      }
    },
    handleSelectMaterial(value, row) {
      row.materialId=value;
      row.materialName=this.restMaterials[value].materialName;
    },
    /* 查找物料信息 */
    querySearchMaterial(queryString) {
      this.restMaterials={};
      if(isValidString(queryString)) {
        listMaterial({ "materialName": queryString }).then(response => {
          if(response.rows.length>0){
            this.restMaterials={};
            this.queryListMaterial = response.rows.map(item=>{
              this.restMaterials[item.materialId]=item;
              return {
                value: item.materialName,
                id: String(item.materialId),
                num: item.num,
              }
            });
          }
        })
      }else {
        this.queryListMaterial = []; // 输入为空时清空历史数据
      }
    },
    handleSelectParts(value, row) {
      row.partsId=value;
      row.partsName=this.restParts[value].partsName;
      row.norm=this.restParts[value].norm;
      row.price=this.restParts[value].price;
    },
    /* 查找配件信息 */
    querySearchParts(queryString) {
      if(isValidString(queryString)) {
        this.restParts={};
        listPartsJoinPurchase({ "partsName": queryString,"serviceId":this.form.serviceId }).then(response => {
          if (response.rows.length > 0) {
            this.queryListParts = response.rows.map(item => {
              this.restParts[item.partsId]=item;
              return {
                value: item.partsName,
                id: String(item.partsId),
                partsType: item.partsType,
                num: item.num,
              }
            });
          }
        })
      }else {
        this.queryListParts = []; // 输入为空时清空历史数据
      }
    },
    cleanData(data){
      console.log(data)
      this.queryList=[]
    },
    // 校验
    validateData(row){
      if (!row.partsName&&row.resType==='2') {
        this.$message.error('配件名称不能为空');
        return false;
      }else if(!row.materialName&&row.resType==='1') {
        this.$message.error('物料名称不能为空');
        return false;
      }else if (!row.num) {
        this.$message.error('数量不能为空');
        return false;
      }
      return true;
    },
    // 退出编辑状态
    reBackEdit(index){
      this.$set(this.tResRequestDetailList[index], 'isEditing', false);
      delete this.tResRequestDetailList[index].isNew;
      delete this.tResRequestDetailList[index]._backup;
    },
  }
}
</script>
