<template>
  <!-- 静音按钮（可选） -->
  <el-button
    v-if="showMuteButton"
    size="mini"
    @click="toggleMute"
  >
    {{ isMuted ? '🔈 开启提示音' : '🔊 关闭提示音' }}
  </el-button>
</template>

<script>
export default {
  name: "audioNotification",
  data() {
    return {
      audio: null,
      audioFile: require("@/assets/music/message.mp3"),
      isMuted: false,
      showMuteButton: false,
      triedToPlay: false // 记录是否尝试过播放
    };
  },
  mounted() {
    this.initAudio();
    // 静音状态从本地存储读取
    this.isMuted = localStorage.getItem("audioMuted") === "true";
  },
  methods: {
    // 初始化音频（静音模式绕过浏览器限制）
    initAudio() {
      if (!this.audio) {
        this.audio = new Audio(this.audioFile);
        this.audio.volume = this.isMuted ? 0 : 0.5;
        this.audio.load(); // iOS必须预加载
      }
    },

    // 核心播放方法
    async play() {
      if (this.isMuted) return;

      this.triedToPlay = true;
      this.initAudio();

      try {
        this.audio.currentTime = 0;
        await this.audio.play();
        this.showMuteButton = true; // 播放成功后才显示静音按钮
      } catch (error) {
        this.handlePlayError(error);
      }
    },

    // 错误处理（分级策略）
    handlePlayError(error) {
      console.warn("播放失败:", error);

      // 1. 如果是权限问题，引导用户点击
      if (error.name === "NotAllowedError") {
      /*   this.$message.warning("请点击页面任意位置启用声音提示"); */
        this.setupOneTimeInteraction();
        return;
      }

      // 2. 其他错误（如文件丢失）
      this.$message.error("提示音加载失败");
      this.isMuted = true; // 自动静音避免重复报错
    },

    // 设置一次性交互监听（点击后重试播放）
    setupOneTimeInteraction() {
      const retryPlay = () => {
        if (this.triedToPlay && !this.isMuted) {
          this.play(); // 用户交互后再次尝试
        }
        window.removeEventListener("click", retryPlay);
      };

      window.addEventListener("click", retryPlay, { once: true });
    },

    // 静音切换
    toggleMute() {
      this.isMuted = !this.isMuted;
      if (this.audio) {
        this.audio.volume = this.isMuted ? 0 : 0.5;
      }
      localStorage.setItem("audioMuted", this.isMuted);
    }
  }
};
</script>
