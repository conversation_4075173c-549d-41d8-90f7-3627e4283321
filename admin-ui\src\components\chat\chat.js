import { isValidString } from '@/utils'

export default {
  namespaced: true, // 关键！启用命名空间
  state: {
    activeSessionId: null,
    messages: []
  },
  mutations: {
    SET_MESSAGES(state, { sessionId, messages }) {
      if(!isValidString(sessionId)) return;
      if (state.activeSessionId === sessionId) {
        state.messages = messages;
      }

      // 同步到 sessionStorage
      sessionStorage.setItem(
        `chat_${sessionId}`,
        JSON.stringify({
          lastUpdated: new Date().toISOString(),
          messages
        })
      );
    },
    ADD_MESSAGE(state, { sessionId, message }) {
      if(!isValidString(sessionId)) return;
      if (state.activeSessionId === sessionId) {
        state.messages.push(message);
      }
      // 更新 sessionStorage
      const cached = JSON.parse(
        sessionStorage.getItem(`chat_${sessionId}`) || '{"messages": []}'
      );
      cached.messages.push(message);
      sessionStorage.setItem(`chat_${sessionId}`, JSON.stringify(cached));
    }
  },
  actions: {
    // 初始化或切换会话
    async loadChatSession({ commit }, sessionId) {
      // 1. 从 sessionStorage 恢复
      const cached = sessionStorage.getItem(`chat_${sessionId}`);
      if (cached) {
        commit('SET_MESSAGES', {
          sessionId,
          messages: JSON.parse(cached).messages
        });
      }

      // 2. WebSocket 会自动从服务器获取最新消息（见组件逻辑）
    },

    // 发送消息（WebSocket 和本地存储双写）
    async sendMessage({ commit, state }, { sessionId, message }) {
      // 先更新本地
      commit('ADD_MESSAGE', {
        sessionId,
        message: {
          ...message,
          type: 'sent', // 标记为已发送
          time: new Date().toISOString()
        }
      });

      // 更新 Vuex
     /*  commit('ADD_MESSAGE', newMessage);

      // 同步到 sessionStorage
      updateSessionStorage(state.activeSessionId, state.messages); */

      // 发送到服务器（实际项目需加错误处理）

    }
  }
};

// 更新 sessionStorage 的辅助函数
function updateSessionStorage(sessionId, messages) {
  sessionStorage.setItem(
    `chat_${sessionId}`,
    JSON.stringify({
      lastUpdated: new Date().toISOString(),
      messages: messages
    })
  );
}
