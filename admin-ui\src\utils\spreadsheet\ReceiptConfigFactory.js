/**
 * 单据配置工厂
 * 用于创建和管理不同类型单据的配置
 */

import { AcceptReceiptUniversalConverter } from './AcceptReceiptUniversalConverter.js'

/**
 * 单据类型枚举
 */
export const RECEIPT_TYPES = {
  CAR_DAMAGE: 'car_damage',           // 汽车定损单
  MAINTENANCE: 'maintenance',         // 维修单
  INSPECTION: 'inspection',           // 检测单
  INSURANCE: 'insurance',             // 保险单
  PURCHASE: 'purchase',               // 采购单
  SALES: 'sales'                      // 销售单
}

/**
 * 单据配置工厂类
 */
export class ReceiptConfigFactory {
  constructor() {
    this.converters = new Map()
    this.configs = new Map()
    this.converterInstances = new Map() // 缓存转换器实例
    this.initializeDefaultConfigs()
  }

  /**
   * 初始化默认配置
   */
  initializeDefaultConfigs() {
    // 汽车定损单配置
    this.registerReceiptType(RECEIPT_TYPES.CAR_DAMAGE, {
      converter: AcceptReceiptUniversalConverter,
      title: '汽车定损收车明细单',
      georgianTitle: 'მანქანის დაზიანების შეფასება და ქვითრების სია',
      companyName: 'EURO MOTORS',
      defaultConfig: {
        width: '100%',
        height: '800px',
        readonly: true,
        showOperations: true
      },
      exportOptions: {
        pdf: {
          enabled: true,
          orientation: 'landscape',
          format: 'a4'
        },
        excel: {
          enabled: true,
          sheetName: 'CarDamageReport'
        },
        print: {
          enabled: true
        }
      },
      validation: {
        required: ['customerNickName', 'serviceId', 'brand'],
        optional: ['phonenumber', 'color', 'carPlateNumber']
      }
    })

    // 维修单配置（示例）
    this.registerReceiptType(RECEIPT_TYPES.MAINTENANCE, {
      converter: AcceptReceiptUniversalConverter,
      title: '汽车维修单',
      georgianTitle: 'ავტომობილის შეკეთების ორდერი',
      companyName: 'EURO MOTORS',
      defaultConfig: {
        width: '100%',
        height: '700px',
        readonly: true,
        showOperations: true
      },
      exportOptions: {
        pdf: { enabled: true },
        excel: { enabled: true },
        print: { enabled: true }
      }
    })

    // 检测单配置（示例）
    this.registerReceiptType(RECEIPT_TYPES.INSPECTION, {
      converter: AcceptReceiptUniversalConverter,
      title: '汽车检测单',
      georgianTitle: 'ავტომობილის ინსპექციის ორდერი',
      companyName: 'EURO MOTORS',
      defaultConfig: {
        width: '100%',
        height: '600px',
        readonly: true,
        showOperations: true
      },
      exportOptions: {
        pdf: { enabled: true },
        excel: { enabled: true },
        print: { enabled: true }
      }
    })
  }

  /**
   * 注册单据类型
   * @param {string} type 单据类型
   * @param {object} config 配置对象
   */
  registerReceiptType(type, config) {
    if (!config.converter) {
      throw new Error(`单据类型 ${type} 缺少转换器配置`)
    }

    this.configs.set(type, {
      ...config,
      type
    })
  }

  /**
   * 创建转换器实例
   * @param {string} type 单据类型
   * @param {object} customConfig 自定义配置
   * @returns {object} 转换器实例
   */
  createConverter(type, customConfig = {}) {
    const config = this.configs.get(type)
    if (!config) {
      throw new Error(`未找到单据类型: ${type}`)
    }

    const ConverterClass = config.converter
    if (!ConverterClass) {
      throw new Error(`单据类型 ${type} 未配置转换器`)
    }

    // 检查是否已有缓存的实例
    const cacheKey = `${type}_${JSON.stringify(customConfig)}`
    if (this.converterInstances.has(cacheKey)) {
      return this.converterInstances.get(cacheKey)
    }

    // 使用Universal转换器
    const converterConfig = {
      title: config.title,
      georgianTitle: config.georgianTitle,
      companyName: config.companyName,
      ...customConfig
    }

    const instance = new ConverterClass(converterConfig)
    this.converterInstances.set(cacheKey, instance)
    return instance
  }

  /**
   * 获取单据配置
   * @param {string} type 单据类型
   * @returns {object} 配置对象
   */
  getReceiptConfig(type) {
    const config = this.configs.get(type)
    if (!config) {
      throw new Error(`未找到单据类型: ${type}`)
    }

    return { ...config }
  }

  /**
   * 获取默认组件配置
   * @param {string} type 单据类型
   * @returns {object} 组件配置
   */
  getDefaultComponentConfig(type) {
    const config = this.getReceiptConfig(type)
    return {
      ...config.defaultConfig,
      customConfig: {
        exportOptions: config.exportOptions
      }
    }
  }

  /**
   * 验证表单数据
   * @param {string} type 单据类型
   * @param {object} formData 表单数据
   * @returns {object} 验证结果
   */
  validateFormData(type, formData) {
    const config = this.getReceiptConfig(type)
    const validation = config.validation || {}
    const errors = []
    const warnings = []

    // 检查必填字段
    if (validation.required) {
      validation.required.forEach(field => {
        if (!formData[field] || formData[field] === '') {
          errors.push(`字段 ${field} 是必填的`)
        }
      })
    }

    // 检查可选字段（警告）
    if (validation.optional) {
      validation.optional.forEach(field => {
        if (!formData[field] || formData[field] === '') {
          warnings.push(`建议填写字段 ${field}`)
        }
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 获取所有已注册的单据类型
   * @returns {array} 单据类型列表
   */
  getRegisteredTypes() {
    return Array.from(this.configs.keys()).map(type => {
      const config = this.configs.get(type)
      return {
        type,
        title: config.title,
        georgianTitle: config.georgianTitle
      }
    })
  }

  /**
   * 检查单据类型是否存在
   * @param {string} type 单据类型
   * @returns {boolean} 是否存在
   */
  hasReceiptType(type) {
    return this.configs.has(type)
  }

  /**
   * 移除单据类型
   * @param {string} type 单据类型
   */
  removeReceiptType(type) {
    this.configs.delete(type)
    this.converters.delete(type)
  }

  /**
   * 获取导出选项
   * @param {string} type 单据类型
   * @returns {object} 导出选项
   */
  getExportOptions(type) {
    const config = this.getReceiptConfig(type)
    return config.exportOptions || {
      pdf: { enabled: true },
      excel: { enabled: true },
      print: { enabled: true }
    }
  }

  /**
   * 创建完整的单据组件配置
   * @param {string} type 单据类型
   * @param {object} options 选项
   * @returns {object} 完整配置
   */
  createReceiptComponentConfig(type, options = {}) {
    const {
      formData = {},
      orderDetailList = [],
      serviceTypeDict = {},
      customConfig = {},
      componentConfig = {}
    } = options

    const converter = this.createConverter(type, customConfig)
    const defaultConfig = this.getDefaultComponentConfig(type)
    const exportOptions = this.getExportOptions(type)

    return {
      converter,
      formData,
      orderDetailList,
      serviceTypeDict,
      ...defaultConfig,
      ...componentConfig,
      customConfig: {
        ...defaultConfig.customConfig,
        ...componentConfig.customConfig,
        exportOptions
      }
    }
  }
}

// 创建单例实例
export const receiptConfigFactory = new ReceiptConfigFactory()

// 导出便捷方法
export const createReceiptConverter = (type, config) => {
  return receiptConfigFactory.createConverter(type, config)
}

export const getReceiptConfig = (type) => {
  return receiptConfigFactory.getReceiptConfig(type)
}

export const validateReceiptData = (type, formData) => {
  return receiptConfigFactory.validateFormData(type, formData)
}

export const createReceiptComponentConfig = (type, options) => {
  return receiptConfigFactory.createReceiptComponentConfig(type, options)
}

export default receiptConfigFactory
