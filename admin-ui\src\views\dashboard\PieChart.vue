<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'
import { strConvert } from '@/i18n/i18nMix'
import Cookies from 'js-cookie'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '250px'
    },
    labels:{
      type:Array,
      default:[]
    },
    dataList:{
      type:Array,
      default: [
      ]
    }
  },
  data() {
    return {
      chart: null,
      langName:{ '登记中': null,
        '待分派': null,
        '执行中': null,
        '等待配件': null,
        '待结算': null,
        '已完结':null,},
    }
  },
  watch: {
    dataList: {
      handler(newVal) {
        let listData=[];
        if(Cookies.get("language")!=='zh'){
          newVal.forEach(item=>{
            console.log(strConvert(item.name))
            Object.keys(this.langName).forEach(name=>{
              if(item.name===name){
                listData.push({ value: item.value, name: this.langName[name]})
              }
            })
          })

        }else listData=newVal;
        this.initChart(listData);
      },
      deep: true
    }
  },
  created() {
    if(Cookies.get("language")!=='zh'){
      this.$i18n.locale=Cookies.get("language");
      this.$nextTick(() => {
        Object.keys(this.langName).forEach(item=>{
          this.langName[item]=strConvert(item)
        })
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart(this.dataList)
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    strConvert,
    initChart(listData) {
      this.chart = echarts.init(this.$el, 'macarons')
      this.chart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c} ({d}%)'
        },
        legend: {
          left: 'center',
          bottom: '10',
          data: listData.map(item => item.name)
        },
        series: [
          {
            name: 'WEEKLY WRITE ARTICLES',
            type: 'pie',
            roseType: 'radius',
            radius: [15, 95],
            center: ['50%', '38%'],
            data: listData,
            animationEasing: 'cubicInOut',
            animationDuration: 2600
          }
        ]
      })
    }
  }
}
</script>
