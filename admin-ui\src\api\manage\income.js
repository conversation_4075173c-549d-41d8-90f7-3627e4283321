import request from '@/utils/request'

// 查询资金收入登记列表
export function listIncome(query) {
  return request({
    url: '/manage/income/list',
    method: 'get',
    params: query
  })
}

// 查询资金收入登记详细
export function getIncome(incomeId) {
  return request({
    url: '/manage/income/' + incomeId,
    method: 'get'
  })
}

// 新增资金收入登记
export function addIncome(data) {
  return request({
    url: '/manage/income',
    method: 'post',
    data: data
  })
}

// 修改资金收入登记
export function updateIncome(data) {
  return request({
    url: '/manage/income',
    method: 'put',
    data: data
  })
}

// 删除资金收入登记
export function delIncome(incomeId) {
  return request({
    url: '/manage/income/' + incomeId,
    method: 'delete'
  })
}
