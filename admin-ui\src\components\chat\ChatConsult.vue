<template>
  <div class="chat-consult-container" v-if="isVisible">
    <div class="chat-header">
      <h3>在线咨询</h3>
      <button class="close-btn" @click="handleClose">×</button>
    </div>

    <div class="chat-content">
      <!-- 聊天历史记录区域 -->
      <div class="chat-history" ref="chatHistory" >
        <div v-for="(message, index) in displayMessages" :key="index"
             :class="['message', message.messageType]">
          <div class="message-content">
            <template v-if="message.type === 'link'">
              <div class="product-card">
                <img :src="message.image" alt="商品图片">
                <div class="product-info">
                  <h4>{{ message.title }}</h4>
                  <p>{{ message.description }}</p>
                </div>
              </div>
            </template>
            <template v-else>
              {{ message.content }}
            </template>
          </div>
          <span class="message-time">{{ message.time }}</span>
        </div>
      </div>


      <!-- 聊天输入区域 -->
      <div class="chat-input-area">
        <div class="input-container">
          <textarea

            v-model="messageInput"
            placeholder="请输入消息..."
            @keyup.enter="handleSend"
          ></textarea>
        </div>
        <div class="button-group">
          <button class="send-btn" @click="handleSend">发  送</button>
          <button class="cancel-btn" @click="handleClose">取  消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {formatDate, isValidString} from "@/utils";
import { mapActions, mapGetters, mapState } from 'vuex'

export default {
  name: 'ChatConsult',
  data() {
    return {
      messageInput: '',
      sessionId:"",
      systemMsg:{},
      displayMessages: [], // 独立的状态用于渲染
      ws: null,
      userInfo: {
        phone: '',
        email: ''
      },
      // 表单校验
      rules: {
        email: [
          {
            type: "email",
            message: "请输入正确的邮箱地址",
            trigger: ["blur", "change"]
          }
        ],
        phone: [
          {
            required: true,
            pattern: /[0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      },
      isInit: true,  //是否初始化，如果，需要获取历史消息，否则如果缓存已经有，直接发送空消息即可。
      isRegistered: false, // 新增：标记用户是否已注册
      baseUrl: process.env.VUE_APP_BASE_API,
      mainHost: process.env.MAIN_HOST,
      messageType:"server"//用户类型，用于区别是用户端还是服务人员
    }
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false
    },
    querySessionId: {
      type: String,
      default: false
    }
  },
  watch: {
    querySessionId: {
      handler(val) {
        if (val) {
          this.sessionId=val;
          this.displayMessages=[];
          // 从 sessionStorage 加载历史记录
          const cached = sessionStorage.getItem(`chat_${this.sessionId}`);
          if (cached) {
            let list=JSON.parse(cached).messages
            if(list.length>0){
              this.displayMessages=list;
              this.isInit=false
              this.scrollToBottom();
            }
          }
          // 初始化 WebSocket 连接
          this.initWebSocket();
        } else if (this.ws) {
            this.ws.close();
        }
      },
      immediate: true
    },
  },
  created() {
  },
  methods: {
    ...mapActions('chat', ['loadChatSession', 'sendMessage']),

    initWebSocket() {
      if (this.ws) {
        this.ws.close();
        clearInterval(this.pingInterval);
      }



      console.log(this.sessionId)
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      this.ws = new WebSocket(
        `${protocol}//${window.location.host}${this.baseUrl}/chat/SERVICE`
      );


      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        if (data.type === 'pong') return;

        // 处理历史消息（首次连接时服务器返回）
        if (data.type === 'history') {
          let list=JSON.parse(data.content)
          if(Array.isArray(list)){
            this.$store.commit('chat/SET_MESSAGES', {
              sessionId: this.sessionId,
              messages: list.map(msg => ({
                ...msg,
                type: 'received'
              }))
            });
            list.map(item=>{
             this.displayMessages.push(item)
            })
            this.isInit=false;
            return;
          }
        }


        // 处理实时消息
        if(data.type==="error"){
          this.systemMsg={
            type: 'system',
            content: data.content,
          }
          console.log(this.systemMsg)
          return
        }
        this.$store.commit('chat/ADD_MESSAGE', JSON.parse(data.content)[0]);
        console.log(JSON.parse(data.content))
        this.displayMessages.push(JSON.parse(data.content)[0]);
        this.scrollToBottom()
      };




      this.ws.onopen = () => {
        // 心跳检测
        this.pingInterval = setInterval(() => {
          this.ws.send(JSON.stringify({ type: "ping" }));
        }, 30000);

        if(this.isInit){
          // 主动请求历史消息
          this.ws.send(JSON.stringify({
            type: 'get_history',
            to: this.sessionId
          }));
        }else {
          // 主动请求历史消息
          this.ws.send(JSON.stringify({
            type: 'message',
            to: this.sessionId
          }));
        }

      };

      this.ws.onclose = () => {
        clearInterval(this.pingInterval);
      };

      this.scrollToBottom();
    },

    /**
     * 初始化请求用于获取历史记录及正常信息发送
     */
    async handleSend() {
      if (!this.messageInput.trim()) {
        this.$message.error("消息不能为空");
        return;
      }
      const message = {
        type: 'message',
        messageType: this.messageType,
        content: this.messageInput,
        to: this.sessionId,
        time: new Date().toISOString()
      };

      console.log("是不是空的？",message)
      // 1. 更新本地状态
      await this.$store.dispatch('chat/sendMessage', {
        sessionId: this.sessionId,
        message: { ...message, type: 'sent' }
      });

      // 2. 通过 WebSocket 发送
      if (this.ws?.readyState === WebSocket.OPEN) {
        console.log(659165)
        this.ws.send(JSON.stringify(message));
      }

      this.messageInput = '';
      this.scrollToBottom();
    },

    handleClose() {
      if (this.ws) {
        this.ws.close();
        clearInterval(this.pingInterval);
      }
      this.$emit('close');
    },

    // 滚动到底部
    scrollToBottom() {
      setTimeout(() => {
        const chatHistory = this.$refs.chatHistory
        if (chatHistory) {
          chatHistory.scrollTop = chatHistory.scrollHeight
        }
      }, 100)
    }
  },
}
</script>

<style scoped>
.chat-consult-container {
  //position: fixed;
  border-inline: 2px #909399;
  width:510px;
  margin-left: 20px;
  height: 600px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.chat-header {
  padding: 15px;
  background: #1890ff;
  color: white;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
}

.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 15px;

  /* 添加这行以确保 chat-history 可以计算出高度 */
  overflow: hidden;
}



.chat-history {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 15px;

  /* 添加以下行确保可滚动区域不会被输入框挤出容器 */
  min-height: 100px;
  max-height: 100%;
}


.message {
  margin-bottom: 15px;
  max-width: 80%;
}

.message.server {
  margin-left: auto;
}

.message-content {
  padding: 10px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.message.server .message-content {
  background: #1890ff;
  color: white;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  display: block;
}

.chat-input-area {
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.input-container {
  max-width:350px;
  margin-bottom: 10px;
}

textarea {
  width: 100%;
  height: 80px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: none;
}

.button-group {
  display: flex;
  gap: 10px;
}

.send-btn, .cancel-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.send-btn {
  background: #1890ff;
  color: white;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.user-info-form {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.form-group {
  margin-bottom: 10px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.required {
  color: red;
}

.product-card {
  display: flex;
  background: white;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.product-card img {
  width: 80px;
  height: 80px;
  object-fit: cover;
}

.product-info {
  padding: 10px;
}

.product-info h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
}

.product-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}
</style>
