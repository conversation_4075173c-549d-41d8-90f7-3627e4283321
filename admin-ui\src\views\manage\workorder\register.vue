<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="!isMobile"
      v-show="showSearch"
      :label-width="isMobile ? '100%' : '140px'"
      class="mobile-form-adapt"
    >
      <el-form-item :label="strConvert('工单编号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('工单标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入工单标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户编号')" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          :placeholder="strConvert('请输入客户编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户姓名')" prop="customerNickName">
        <el-input
          v-model="queryParams.customerNickName"
          :placeholder="strConvert('请输入客户姓名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('车牌号')" prop="carPlateNumber">
        <el-input
          v-model="queryParams.carPlateNumber"
          :placeholder="strConvert('请输入车牌号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          :style="{width: isMobile ? '100%' : '240px'}"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleQuery">{{strConvert('搜索')}}</el-button>
        <el-button icon="el-icon-refresh" @click="resetQuery">{{strConvert('重置')}}</el-button>
      </el-form-item>
    </el-form>

    <!-- 移动端按钮组 -->
    <div class="mobile-button-group" v-if="isMobile">
      <el-button
        type="danger"
        icon="el-icon-plus"
        size="medium"
        @click="handleFastAdd"
        v-hasPermi="['manage:workorder:add']"
      >{{strConvert('快速登记')}}</el-button>
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="medium"
        @click="handleAdd"
        v-hasPermi="['manage:workorder:add']"
      >{{strConvert('登记')}}</el-button>
      <el-button
        type="info"
        :disabled="single"
        icon="el-icon-s-check"
        @click="goPrint"
      >{{strConvert('受理单打印')}}</el-button>
    </div>

    <!-- 桌面端按钮组 -->
    <el-row :gutter="10" class="mb8" v-else>
      <el-col :span="1.5">
        <el-button
          type="danger"
          icon="el-icon-plus"
          size="medium"
          @click="handleFastAdd"
          v-hasPermi="['manage:workorder:add']"
        >{{strConvert('快速登记')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="medium"
          @click="handleAdd"
          v-hasPermi="['manage:workorder:add']"
        >{{strConvert('登记')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          :disabled="single"
          icon="el-icon-s-check"
          @click="goPrint"
        >{{strConvert('受理单打印')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          :disabled="single"
          icon="el-icon-s-check"
          @click="goPrint2"
        >{{strConvert('受理单打印（新）')}}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格 -->
    <el-table
      ref="singleTable"
      v-loading="loading"
      :data="workorderList"
      @selection-change="handleSelectionChange"
      :style="{width: '100%', height: isMobile ? mobileMaxHeight + 'px' : 'auto'}"
      class="mobile-table-adapt"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('工单编号')" :width="isMobile ? '120' : '155'" align="center" prop="serviceId" />
      <el-table-column :label="strConvert('工单标题')" align="center" prop="title" />
      <el-table-column :label="strConvert('车牌号')" width="90" align="center" prop="carPlateNumber" />
      <el-table-column :label="strConvert('客户编号')" width="110" align="center" prop="customerId" />
      <el-table-column :label="strConvert('客户姓名')" width="100" align="center" prop="customerNickName" />
<!--      <el-table-column :label="strConvert('图片')" align="center" width="180" prop="images">
        <template slot-scope="scope">
          <image-preview :src="scope.row.images" :width="50" :height="50"/>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('创建者')" width="80" align="center" prop="createBy" />
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('问题描述')" align="center"  prop="comment" />
      <el-table-column :label="strConvert('操作')" align="center" :width="isMobile ? '100%' : '200'" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div class="mobile-action-buttons" v-if="isMobile">
            <el-button
              size="small"
              type="warning"
              icon="el-icon-s-promotion"
              @click="handleRelease(scope.row)"
              v-hasPermi="['manage:workorder:edit']"
            >{{strConvert('发布')}}</el-button>
            <el-button
              size="small"
              icon="el-icon-edit"
              v-hasPermi="['manage:workorder:edit']"
              type="text"
              @click="$router.push(`/manage/questionForm/register/${scope.row.serviceId}`)"
            >
              {{strConvert('修改')}}
            </el-button>
            <el-button
              size="small"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['manage:workorder:remove']"
            >{{strConvert('删除')}}</el-button>
          </div>
          <div v-else>
            <el-button
              size="medium"
              type="warning"
              icon="el-icon-s-promotion"
              @click="handleRelease(scope.row)"
              v-hasPermi="['manage:workorder:edit']"
            >{{strConvert('发布工单')}}</el-button>
            <el-button
              size="medium"
              icon="el-icon-edit"
              v-hasPermi="['manage:workorder:edit']"
              type="text"
              @click="$router.push(`/manage/questionForm/register/${scope.row.serviceId}`)"
            >
              {{strConvert('修改')}}
            </el-button>
            <el-button
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['manage:workorder:remove']"
            >{{strConvert('删除')}}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
      :small="isMobile"
    />

    <!-- 添加或修改工单记录对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="fastOpen"
      :width="isMobile ? '90%' : '800px'"
      append-to-body
      class="mobile-dialog-adapt"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <span style="color: red">{{ strConvert('快速登记方便快速添加表单数据，内容的顺序依次是：故障描述 -> 客户姓名 -> 电话号码 -> 预收款(CEL) -> 车牌号') }}</span>
        <el-divider content-position="center">快速录入</el-divider>
        <el-form-item :label="strConvert('录入信息')" prop="fastRemark">
          <el-input v-model="form.fastRemark" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
        <el-divider content-position="center">样例：</el-divider>
        <el-input readonly type="textarea" rows="8" :value="example" />
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFastForm">{{strConvert('确 定')}}</el-button>
        <el-button @click="fastCancel">{{strConvert('取 消')}}</el-button>
      </div>
    </el-dialog>

    <receipt show="accept" :serviceId="serviceId" printType="acceptReceipt" :OpenPrint.sync="printOpen" />
    <receipt2 show="accept" :serviceId="serviceId" printType="acceptReceipt" :OpenPrint.sync="printOpen2" />
  </div>
</template>

<script>
import {
  addWorkorder,
  delWorkorder,
  getWorkorder,
  listWorkorder,
  releaseWorkorder,
  updateWorkorder
} from '@/api/manage/workorder'
import { addCustomer, getInfoByNickName, listCustomer } from '@/api/manage/customer'
import { formatDate } from '@/utils'
import { listParts } from '@/api/manage/parts'
import { getInfo } from '@/api/login'
import Receipt2 from '@/views/receipt/components/indexCloud.vue'
import cache from '@/plugins/cache'
import Receipt from '@/views/receipt/components/index.vue'
import { strConvert } from '@/i18n/i18nMix'
export default {
  name: "Workorder",
  components: { Receipt,Receipt2 },
  data() {
    return {
      isMobile: false,
      mobileMaxHeight: 500,
      mobileButtons: [
        { type: 'danger', icon: 'el-icon-plus', text: '快速登记', handler: this.handleFastAdd, perm: ['manage:workorder:add'] },
        { type: 'primary', icon: 'el-icon-plus', text: '登记', handler: this.handleAdd, perm: ['manage:workorder:add'] },
      ],
      // 遮罩层
      loading: true,
      serviceId:"",
      // 选中数组
      ids: [],
      example:
        "样例1：检车后发现需要喷漆处理，ვანგ მინგი，558411526，500，KYB7673S\n"+
        "样例2：检车后发现需要喷漆处理@ვანგ მინგი@558411526@500@KYB7673S\n"+
        "样例3：检车后发现需要喷漆处理!ვანგ მინგი!558411526!500!KYB7673S",
      // 子表选中数据
      checkedTWorkOrderDetail: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      restCustomer: {},
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单记录表格数据
      workorderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      fastOpen:false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceId: null,
        title: null,
        businessType: null,
        carPlateNumber:null,
        customerId: null,
        customerNickName: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        customerNickName: [
          { required: true, message: "客户姓名不能为空", trigger: "blur" }
        ],
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        comment: [
          { required: true, message: "问题描述不能为空", trigger: "blur" }
        ],
        phonenumber: [
          { required: true, message: "客户电话不能为空", trigger: "blur" }
        ],
        advancePayment: [
          { required: true, message: "预收款不能为空", trigger: "blur" }
        ],
        carPlateNumber: [
          { required: true, message: "车牌号不能为空", trigger: "blur" }
        ]
      },
      queryListParts:null,
      printOpen:false,
      printOpen2:false,
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.checkMobile();
    window.addEventListener('resize', this.checkMobile);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.checkMobile);
  },
  methods: {
    strConvert,
    /** 查询工单记录列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      this.queryParams.businessType="0"
      listWorkorder(this.queryParams).then(response => {
        this.workorderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    fastCancel() {
      this.fastOpen = false
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 调整后的单选处理方法
    handleSelectionChange(selection) {
      // 如果选择了多行，则只保留最后选择的一行
      if (selection.length > 1) {
        this.$refs.singleTable.clearSelection();
        this.$refs.singleTable.toggleRowSelection(selection[selection.length - 1]);
        // 更新为单选后的结果
        this.ids = [selection[selection.length - 1].serviceId];
        this.single = false;
        this.multiple = false;
      } else {
        // 正常单选情况
        this.ids = selection.length ? [selection[0].serviceId] : [];
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push(`/manage/questionForm/register/000`);
    },
    /** 新增按钮操作 */
    handleFastAdd() {
      this.fastOpen = true
      this.title = "快速添加工单"
    },
    /** 发布工单操作 */
    handleRelease(row) {
      this.$modal.confirm(this.strConvert('单号为"') + row.serviceId + this.strConvert('"的工单，确认后将产生入帐流水，是否确认？')).then(() => {
          releaseWorkorder(row).then(response => {
            this.$modal.msgSuccess("发布成功")
            this.getList()
          })
      }).catch(() => {})
    },
    goPrint(){
      this.serviceId = this.ids[0]
      this.printOpen = true
    },
    goPrint2(){
      this.serviceId = this.ids[0]
      this.printOpen2 = true
    },
    submitFastForm(){
      var fastData=[];
      var tempData="";
      if(this.form.fastRemark!==undefined&&this.form.fastRemark!==null){
        tempData=this.form.fastRemark
         fastData=this.splitText(this.form.fastRemark);
      }else {
        return  this.$modal.msgWarning("请输入内容")
      }
      console.log(fastData)
      this.form.comment=fastData[0]
      this.form.advancePayment=fastData[3]
      this.form.carPlateNumber=fastData[4]
      var customer= {};
      if(fastData.length>=5){
        getInfoByNickName(fastData[1]).then(response => {
          if(response.data!=null){
            customer=response.data
            this.form.customerId=customer.customerId;
            this.form.customerNickName=customer.customerNickName;
            this.form.phonenumber=customer.phonenumber;
            this.fastOpen=false;

          }else{
            customer.customerNickName=fastData[1]
            customer.phonenumber=fastData[2]
            addCustomer(customer).then(response => {
              this.form.customerNickName=fastData[1]
              this.form.phonenumber=fastData[2]
              this.fastOpen=false;
            })
          }
          cache.session.setJSON("fastFrom",this.form);
          this.$router.push(`/manage/questionForm/register/000`);
        })
      }else {
        this.form.fastRemark=tempData;
        this.$modal.msgError("数据缺失，请检查后再试")
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const serviceIds = row.serviceId || this.ids
      this.$modal.confirm('是否确认删除工单记录编号为"' + serviceIds + '"的数据项？').then(function() {
        return delWorkorder(serviceIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/workorder/export', {
        ...this.queryParams
      }, `workorder_${new Date().getTime()}.xlsx`)
    },

    /* 过滤内容格式*/
    splitText(text) {
      return text.split(/(?<!\s)[,，@!]+(?!\s)/)  // 只匹配分隔符，且前后不能是空格
        .map(item => item.trim())
        .filter(item => item !== '');
    },
    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
      this.mobileMaxHeight = window.innerHeight - 300; // 根据实际布局调整
    },
  }
}
</script>
