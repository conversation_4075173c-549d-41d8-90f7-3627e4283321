import { isValidString } from '@/utils'

export default {
  methods: {
    //统计主项金额相关信息
    computeMainTotalAmount(){
      let texts=[];
      texts.push(" = "+this.strConvert("折扣金额")+"（"+this.form.discountAmount+"GEL）");
      /*  if(this.FormData.taxRate){
         texts.push(" - 附加税（"+this.FormData.taxRate+"%）");
       } */
      this.T_QUESTION_ORDER_SERVICE_TYPE.forEach(question => {
        if(question.isShowCount!=="+"&&question.amount>0){
          texts.push(" - "+this.strConvert("类目")+question.value+" ( "+question.amount+"GEL ) ")
        }
      })
      this.totalText=texts.join("");
    },
    createWorkerDetailForm(response){
      this.checkboxDetail=[];
      let sumServerCount=false;
      if(response.data.tWorkOrderDetailList!==undefined){
        this.tWorkOrderDetailList=response.data.tWorkOrderDetailList;
        this.totalCountAmount=0.00;
        this.tWorkOrderDetailList.map(item=>{
          this.T_WORK_ORDER_SERVICE_TYPE.map(type=>{
            if(item.questionType===type.value){
              // 初始化存储数据的数组
              if (!Array.isArray(type[type.value])) {
                type[type.value] = [];
              }
              if (type.totalAmount == null) {
                type.totalAmount = 0.00;
              }
              // 计算每个服务类的金额
              if (isValidString(type.isShowCount) && this.form.mainTotalAmount > 0) {
                item.price=0.00;
                item.amount=0.00;
                //判断如果是合并计算的服务，那么只计算一次，其它项为0
                if(type.isShowCount==="+"){
                  if(!sumServerCount){
                    type.totalAmount = this.form.mainTotalAmount * (type.ratio / 100);
                    sumServerCount=true;
                  }
                }else {
                  type.totalAmount = this.form.mainTotalAmount * (type.ratio / 100);
                }
              } else {
                type.totalAmount += parseFloat(item.amount || 0);
              }
              type[type.value].push(item);
              type[type.value][0].amount = type.totalAmount;

              type.num = (type.num || 0) + 1
              type.amount=parseFloat(type.totalAmount).toFixed(2);
              this.checkboxDetail.push(item.questionType)
            }
          })
        });
        this.T_WORK_ORDER_SERVICE_TYPE.forEach(type => {
          this.totalCountAmount += parseFloat(type.totalAmount || 0);
        });
      }
    },
    isRequired(prop) {
      return this.rules[prop]?.some(rule => rule.required) || false;
    }
  }
};
