<script>
import { strConvert } from '@/i18n/i18nMix'

export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    /* if (icon&&title.length <= 5) {
      vnodes.push(<svg-icon icon-class={icon}/>)
    } */

    if (title) {
      let languageTitle=strConvert(title)
      if (languageTitle.length > 5) {
        vnodes.push(<span slot='title' style="font-size:11px" title={(languageTitle)}>{(languageTitle)}</span>)
      } else {
        vnodes.push(<svg-icon icon-class={icon}/>)
        vnodes.push(<span slot='title'>{(languageTitle)}</span>)
      }
    }
    return vnodes
  }
}
</script>
