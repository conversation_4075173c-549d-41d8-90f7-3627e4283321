# Univer 动态边框添加指南

## 概述

本指南介绍如何在 Univer 中为动态生成的数据添加边框。我们提供了多种边框添加方式，从简单的区域边框到复杂的边框管理系统。

## 功能特性

### 1. 智能边框添加
- 根据单元格在区域中的位置智能添加边框
- 支持外边框、内边框、全边框等多种类型
- 自动处理空单元格的创建

### 2. 边框样式预设
- 提供多种预定义边框样式
- 支持自定义边框颜色、宽度、样式
- 针对不同区域类型优化的样式

### 3. 边框管理器
- 统一管理多个边框区域
- 支持优先级控制
- 批量应用边框配置

## 使用方法

### 基础用法

```javascript
// 创建转换器实例
const converter = new AcceptReceiptUniversalConverter();

// 基本边框添加
converter.addDynamicBorders(cellData, {
  startRow: 3,
  endRow: 6,
  startCol: 0,
  endCol: 7,
  borderStyle: {
    color: '#000000',
    width: 1,
    style: 'solid'
  },
  borderTypes: ['all'], // 全边框
  regionType: '基本信息区域'
});
```

### 使用边框样式预设

```javascript
// 使用预定义样式
converter.addDynamicBorders(cellData, {
  startRow: 8,
  endRow: 15,
  startCol: 0,
  endCol: 7,
  borderStyle: converter.getBorderStylePreset('thick'), // 粗边框
  borderTypes: ['top', 'bottom', 'left', 'right'], // 只添加外边框
  regionType: '表格区域'
});
```

### 可用的边框样式预设

- `default`: 默认边框（黑色，1px，实线）
- `thick`: 粗边框（黑色，2px，实线）
- `thin`: 细边框（黑色，0.5px，实线）
- `dashed`: 虚线边框（黑色，1px，虚线）
- `dotted`: 点线边框（黑色，1px，点线）
- `tableHeader`: 表头边框（白色，1px，实线）
- `tableData`: 表格数据边框（灰色，1px，实线）

### 边框类型选项

- `all`: 全边框（外边框 + 内部网格线）
- `top`: 顶部边框
- `bottom`: 底部边框
- `left`: 左侧边框
- `right`: 右侧边框
- `inner`: 内部网格线
- `['top', 'bottom', 'left', 'right']`: 只添加外边框

### 使用边框管理器

```javascript
// 注册多个边框区域
converter.borderManager.registerBorderRegion(
  '基本信息区域',
  3, 6, 0, 7,
  {
    borderStyle: converter.getBorderStylePreset('default'),
    borderTypes: ['all'],
    priority: 1
  }
);

converter.borderManager.registerBorderRegion(
  '表格区域',
  8, 15, 0, 7,
  {
    borderStyle: converter.getBorderStylePreset('tableData'),
    borderTypes: ['all'],
    priority: 2
  }
);

// 批量应用所有注册的边框
converter.borderManager.applyAllBorders(cellData, converter);
```

### 表格专用边框

```javascript
// 为表格添加专业的边框样式
converter.addTableBorders(
  cellData,
  8,    // 表头行
  9,    // 数据开始行
  15,   // 数据结束行
  0,    // 起始列
  7,    // 结束列
  {
    headerBorderStyle: 'tableHeader',
    dataBorderStyle: 'tableData',
    outerBorderStyle: 'thick'
  }
);
```

### 高亮边框

```javascript
// 为选中区域添加高亮边框
converter.addHighlightBorders(
  cellData,
  10, 12, 2, 5, // 区域范围
  '#FF0000'     // 红色高亮
);
```

## 在转换器中使用

### 传统方式

```javascript
const univerData = converter.convertToUniverData(formData, orderDetailList, {
  serviceTypeDict: serviceTypes,
  borderOptions: {
    basicInfoBorderStyle: 'default',
    tableBorderStyle: 'tableData',
    customBorderConfigs: [
      {
        startRow: 20,
        endRow: 25,
        startCol: 0,
        endCol: 7,
        borderStyle: converter.getBorderStylePreset('thick'),
        borderTypes: ['top', 'bottom'],
        regionType: '汇总区域'
      }
    ]
  }
});
```

### 使用边框管理器

```javascript
const univerData = converter.convertToUniverData(formData, orderDetailList, {
  serviceTypeDict: serviceTypes,
  borderOptions: {
    useBorderManager: true
  }
});
```

## 最佳实践

### 1. 性能优化
- 在数据生成完成后统一添加边框
- 避免重复为同一单元格添加边框
- 使用边框管理器处理复杂的边框需求

### 2. 样式一致性
- 使用预定义的边框样式保持一致性
- 为不同类型的区域使用相应的边框样式
- 合理使用边框优先级避免冲突

### 3. 调试和维护
- 使用有意义的 regionType 名称便于调试
- 利用控制台日志跟踪边框添加过程
- 定期清理边框管理器的注册区域

## 注意事项

1. 边框样式会直接修改单元格的 `v` 对象
2. 后添加的边框会覆盖先添加的边框
3. 空单元格会自动创建以支持边框显示
4. 边框管理器支持优先级控制，数字越大优先级越高

## 示例代码

完整的使用示例请参考 `AcceptReceiptUniversalConverter.js` 中的实现。
