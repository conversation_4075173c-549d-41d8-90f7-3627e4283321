<template>

  <div class="dashboard-editor-container">
    <audio-music ref="audioNotification" />
<!--    {{ strConvert('未读消息提醒') }}-->
<!--    <audio controls="controls" autoplay="autoplay" id="re" :src="audioSrc" style="display:none;">
      Your browser does not support the audio element.
    </audio>-->

    <panel-group  :TableData="tableData"  @goTo="goTo"  @handleSetLineChartData="handleSetLineChartData" />

    <el-row style="background:#fff;padding:16px 16px 0;margin-bottom:12px;">
      <line-chart :chart-data="lineChartData" />
    </el-row>

    <el-row :gutter="32">
<!--      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <raddar-chart />
        </div>
      </el-col>-->
      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <pie-chart :labels="businessLabel" :data-list="businessTypeData"/>
        </div>
      </el-col>
<!--      <el-col :xs="24" :sm="24" :lg="8">
        <div class="chart-wrapper">
          <bar-chart />
        </div>
      </el-col>-->
    </el-row>

  </div>
</template>

<script>
import PanelGroup from './dashboard/PanelGroup'
import LineChart from './dashboard/LineChart'
import RaddarChart from './dashboard/RaddarChart'
import PieChart from './dashboard/PieChart'
import BarChart from './dashboard/BarChart'
import { countLast7Days, initData } from '@/api/home'
import { sortChartDataByWeek  } from '@/utils/index'
import { strConvert } from '@/i18n/i18nMix'
import settlementReceipt from '@/views/receipt/settlementReceipt.vue'
import messageMusic from "@/assets/music/message.mp3"
import AudioMusic from '@/components/AudioMusic/index.vue'
import { listParts } from '@/api/manage/parts'
export default {
  name: 'Index',
  components: {
    AudioMusic,
    settlementReceipt,
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart,
  },
  data() {
    return {
      countNum: null,
      tableData: null,
      flowCountData:{
        newVisitis: {},
        messages:  {
          "thu": 0,
          "tue": 0,
          "wed": 0,
          "sat": 0,
          "fri": 0,
          "mon": 0,
          "sun": 0
        },
        purchases:  {},
        shoppings: {},
        parts:{}
      },
      lineChartData: {},
      businessLabel:[strConvert('登记中'),strConvert('待分派'),strConvert('执行中'),strConvert('待结算')],
      businessTypeData:[],
      audioSrc:messageMusic,
      authorizeAudio:false //{{ strConvert('授权播放权限') }}
    }
  },
  created() {
    /* getTrans("ru"); */


    initData().then(response => {
      if(response.code===200){
        this.countNum=response.data.tablesData;
        listParts({ num:5 }).then(response => {
          this.countNum.partsCount = parseInt(response.total)
          this.tableData =this.countNum;
          //{{ strConvert('播放未读消息提醒') }}
          if(Object.keys(this.tableData.noReadMsgCount).length>0){
            // {{ strConvert('需要播放时') }}
            /* setInterval(() => { */
            this.$refs.audioNotification.play();
            /*  }, 5000); */
          }
        })
        this.lineChartData=sortChartDataByWeek(response.data.workOrderData);
        this.flowCountData.shoppings=sortChartDataByWeek(response.data.workOrderData);

        this.businessTypeData = response.data.businessTypeData
          .filter(item => item.orderCount > 0)
          .map(item => ({
            name: this.businessLabel[item.progressStatus] || item.progressStatus,
            value: item.orderCount
          }));



      }
    });

    countLast7Days().then(response => {
      if(response.code===200){
        this.flowCountData.purchases=sortChartDataByWeek(response.data.countFlowLast7Days);
        this.flowCountData.newVisitis=sortChartDataByWeek(response.data.countCustomerLast7Days);
      }
    })
  },
  methods: {
    strConvert,
    handleSetLineChartData(type) {
      this.lineChartData=this.flowCountData[type];
    },
    goTo(type) {
      const obj = { path: "/" }
      switch (type){
        case "newVisitis":  {
          obj.path="/custom/customer"
          break
          //this.$router.push({ path: '/' })
        } case  "messages":  {
          obj.path="/website/message"
          break
        } case  "purchases":  {
          obj.path="/financial/flow";
          break
        } case  "parts":  {
          obj.path="/res/parts";
          break
        } case  "shoppings":
          obj.path="/workorder/wokrorderList"
          break
      }
      this.$router.push(obj)
     /*  this.$tab.closeOpenPage(obj) */
    }
   /*  playMusic() {
      const audio = this.$refs.myMessage;
      audio.play().catch(err => {
        console.error(strConvert('播放失败:'), err);
      });
    },
    pauseMusic() {
      const audio = this.$refs.myMessage;
      audio.pause();
    } */
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 12px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 12px;
  }
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
