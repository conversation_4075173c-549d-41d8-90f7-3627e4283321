<template>
  <div v-if="open" class="cloud-receipt-container">
    <base-receipt-view
      ref="receiptView"
      :receipt-type="receiptType"
      :form-data="FormData"
      :order-detail-list="OrderDetailList"
      :service-type-dict="serviceTypeDict"
      :readonly="!optionPrint"
      :show-form="false"
      :custom-spreadsheet-config="spreadsheetConfig"
      @refresh="handleRefresh"
      @spreadsheet-exported="handleExported"
      @spreadsheet-error="handleError">

      <!-- 转发父组件的header-actions插槽 -->
      <template #header-actions>
        <slot name="header-actions"></slot>
      </template>

    </base-receipt-view>

    <!-- 附件信息 -->
    <div v-if="isValidString(FormData.comment) && isValidString(FormData.images)" class="attachment-section">
      <el-row>
        <div class="attachment-title">
          <h3>附件信息დანართის ინფორმაცია</h3>
        </div>
      </el-row>
      <el-descriptions :column="1" border>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-user"></i>
            დამატებითი ინფორმაცია问题说明:
          </template>
          {{ FormData.comment }}
        </el-descriptions-item>
      </el-descriptions>
      <el-row v-if="optionPrint" class="avoid-break">
        <div class="image-container">
          <ImageCustomPreview
            :src="FormData.images"
            :width="730"
            :height="600"
            pdf-optimized
          />
        </div>
      </el-row>
    </div>
  </div>
</template>

<script>
import { formatDate, formatWesternDate, isValidString } from '@/utils'
import BaseReceiptView from '@/components/common/BaseReceiptView.vue'
import ImageCustomPreview from '@/components/ImageCustomPreview/index.vue'
import { RECEIPT_TYPES } from '@/utils/spreadsheet/ReceiptConfigFactory.js'
import { exportMixin } from '@/mixins/exportMixin.js'
import file_log from '@/assets/logo/fileLog.png'

export default {
  name: 'AcceptReceiptCloud',
  dicts: ['t_work_question_service_type'],
  components: {
    BaseReceiptView,
    ImageCustomPreview
  },
  mixins: [exportMixin],
  props: {
    open: {
      type: Boolean,
      default: false
    },
    OrderDetailList: {
      type: Array,
      default: () => []
    },
    // 表单参数
    FormData: {
      type: Object,
      default: () => ({
        serviceId: null,
        title: null,
        businessType: "0",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber: null,
        phonenumber: null,
        advancePayment: 0, // 预收金额
        discountAmount: 0, // 折后金额
        totalAmount: 0, // 总金额
        balancePayment: 0, // 再付金额
        balance: 0, // 余额
        comment: null,
        images: null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        brand: null,
        color: null,
        expectedTime: null
      })
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      receiptType: RECEIPT_TYPES.CAR_DAMAGE,
      optionPrint: false,
      spreadsheetData: null,
      serviceTypeDict: [],
      fileLog: file_log,
      spreadsheetConfig: {
        width: 810,
        height: 900,
        showToolbar: false,
        showFormulaBar: false
      }
    }
  },

  mounted() {
    console.log('acceptReceiptCloud mounted - props:')
    console.log('- FormData:', this.FormData)
    console.log('- OrderDetailList:', this.OrderDetailList)
    console.log('- open:', this.open)
    this.$nextTick(() => {
      setTimeout(() => {
        this.initServiceTypeDict()
      }, 100)
    })
  },

  watch: {
    FormData: {
      handler(newVal, oldVal) {
        console.log('acceptReceiptCloud watch FormData changed:', newVal)
        // 只刷新半静态区域（基本信息和汇总区域）
        this.handleRefresh({
          forceFullRefresh: false,
          formDataChanged: true,
          orderDetailListChanged: false
        });
        // 移除自动刷新，避免无限循环
        // 只在数据真正变化时记录日志
      },
      deep: true
      // 移除 immediate: true，避免初始化时触发
    },
    OrderDetailList: {
      handler(newVal, oldVal) {
        console.log('acceptReceiptCloud watch OrderDetailList changed:', newVal)
        // 只刷新动态区域（服务项目表格）
        this.handleRefresh({
          forceFullRefresh: false,
          formDataChanged: false,
          orderDetailListChanged: true
        });
        // 移除自动刷新，避免无限循环
        // 只在数据真正变化时记录日志
      },
      deep: true
      // 移除 immediate: true，避免初始化时触发
    }
  },

  methods: {
    // 初始化服务类型字典
    initServiceTypeDict() {
      if (this.dict && this.dict.type && this.dict.type.t_work_question_service_type) {
        if (this.dict.type.t_work_question_service_type.length > 0) {
          this.serviceTypeDict = this.dict.type.t_work_question_service_type.map(item => ({
            value: item.value,
            label: item.label
          }))
        }
      }
    },
    isValidString,
    formatWesternDate,
    formatDate,

    // 处理表格数据更新
    handleDataUpdated(data) {
      console.log("处理表格数据更新",data)
      this.spreadsheetData = data
      this.$emit('data-updated', data)
    },


    /**
     * 打印收据 - 使用当前页面打印功能
     */
    printReceipt() {
      try {
        console.log('1. 开始打印收据')

        if (!this.FormData || !this.FormData.serviceId) {
          this.$message.warning('请先选择有效的服务单')
          return
        }

        // 检查Univer表格组件是否已初始化
        const spreadsheetComponent = this.$refs.receiptView?.$refs?.spreadsheet
        if (!spreadsheetComponent) {
          this.$message.error('表格未加载完成，请稍后再试')
          return
        }

        console.log('2. Univer表格组件已初始化')

        // 获取Univer容器
        const univerContainer = spreadsheetComponent.$el
        if (!univerContainer) {
          this.$message.error('无法找到表格容器')
          return
        }

        console.log('3. 找到Univer容器')

        // 创建打印样式
        const printStyle = document.createElement('style')
        printStyle.id = 'print-style-temp'
        printStyle.innerHTML = `
          @media print {
            body * {
              visibility: hidden;
            }
            .print-area, .print-area * {
              visibility: visible;
            }
            .print-area {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              background: white;
            }
            .univer-toolbar,
            .univer-formula-bar,
            .univer-sheet-bar,
            .univer-context-menu {
              display: none !important;
            }
            .univer-sheet-content table {
              border-collapse: collapse;
            }
            .univer-sheet-content td {
              border: 1px solid #000 !important;
            }
          }
        `
        document.head.appendChild(printStyle)

        // 为容器添加打印类
        const originalClasses = univerContainer.className
        univerContainer.classList.add('print-area')

        // 添加打印标题
        const printTitle = document.createElement('div')
        printTitle.id = 'print-title-temp'
        printTitle.innerHTML = `<h2 style="text-align: center; margin-bottom: 20px; font-family: Arial, sans-serif;">汽车定损收车明细单</h2>`
        printTitle.style.cssText = 'display: none;'

        // 在打印时显示标题
        const titlePrintStyle = document.createElement('style')
        titlePrintStyle.id = 'title-print-style-temp'
        titlePrintStyle.innerHTML = `
          @media print {
            #print-title-temp {
              display: block !important;
              visibility: visible !important;
            }
          }
        `
        document.head.appendChild(titlePrintStyle)

        // 将标题插入到容器前面
        univerContainer.parentNode.insertBefore(printTitle, univerContainer)
        console.log(univerContainer);

        console.log('3. 准备打印，调用浏览器打印功能')

        // 调用浏览器原生打印
        window.print()

        // 清理临时元素和样式
        setTimeout(() => {
          // 移除打印样式
          const tempStyle = document.getElementById('print-style-temp')
          if (tempStyle) {
            tempStyle.remove()
          }

          const tempTitleStyle = document.getElementById('title-print-style-temp')
          if (tempTitleStyle) {
            tempTitleStyle.remove()
          }

          // 移除打印标题
          const tempTitle = document.getElementById('print-title-temp')
          if (tempTitle) {
            tempTitle.remove()
          }

          // 恢复原始类名
          univerContainer.className = originalClasses

          console.log('4. 清理完成')
        }, 1000)

        this.$message.success('打印任务已发送')

      } catch (error) {
        console.error('打印过程中发生错误:', error)
        this.$message.error('打印失败: ' + error.message)

        // 确保清理临时元素
        const tempStyle = document.getElementById('print-style-temp')
        if (tempStyle) tempStyle.remove()

        const tempTitleStyle = document.getElementById('title-print-style-temp')
        if (tempTitleStyle) tempTitleStyle.remove()

        const tempTitle = document.getElementById('print-title-temp')
        if (tempTitle) tempTitle.remove()
      }
    },

    // 导出PDF - 使用混入的方法
    async exportPDF() {
      if (!this.FormData.serviceId) {
        this.$message.warning('请先选择有效的服务单')
        return
      }

      try {
        const element = this.$refs.receiptView?.$refs.spreadsheet?.$el.querySelector('.univer-sheet-content')
        const filename = `汽车定损收车明细单_${this.FormData.serviceId}_${formatDate(new Date(), 'YYYYMMDD')}.pdf`

        // 直接调用混入的exportToPDF方法
        await this.$options.mixins[0].methods.exportToPDF.call(this, {
          element,
          fileName: filename,
          orientation: 'landscape',
          format: 'a4'
        })
      } catch (error) {
        console.error('导出PDF失败:', error)
        this.$message.error('导出PDF失败，请重试')
      }
    },

    // 导出Excel - 使用混入的方法
    async exportExcel() {
      if (!this.FormData.serviceId) {
        this.$message.warning('请先选择有效的服务单')
        return
      }

      try {
        const spreadsheetComponent = this.$refs.receiptView?.$refs?.spreadsheet
        if (spreadsheetComponent) {
          const cellData = spreadsheetComponent.generateSpreadsheetData()
          const filename = `汽车定损收车明细单_${this.FormData.serviceId}_${formatDate(new Date(), 'YYYYMMDD')}.xlsx`

          // 直接调用混入的exportToExcel方法，传递包含样式的原始单元格数据和转换器
          await this.$options.mixins[0].methods.exportToExcel.call(this, {
            cellData: cellData, // 传递原始单元格数据（包含样式）
            fileName: filename,
            sheetName: '汽车定损收车明细单',
            converter: spreadsheetComponent.converter // 传递转换器实例
          })
        } else {
          this.$message.error('表格组件未就绪，无法导出Excel')
        }
      } catch (error) {
        console.error('导出Excel失败:', error)
        this.$message.error('导出Excel失败，请重试')
      }
    },

    // 将单元格数据转换为二维数组
    convertCellDataToArray(cellData) {
      if (!cellData || cellData.length === 0) {
        return []
      }

      const maxRow = Math.max(...cellData.map(cell => cell.r)) + 1
      const maxCol = Math.max(...cellData.map(cell => cell.c)) + 1

      const array = Array(maxRow).fill().map(() => Array(maxCol).fill(''))

      cellData.forEach(cell => {
        if (cell.r >= 0 && cell.r < maxRow && cell.c >= 0 && cell.c < maxCol) {
          // 提取单元格的实际值
          const value = cell.v?.v ?? cell.v ?? ''
          array[cell.r][cell.c] = value
        }
      })

      return array
    },

    // 切换编辑模式
    toggleEditMode() {
      this.optionPrint = !this.optionPrint
      this.$message.info(this.optionPrint ? '已切换到编辑模式' : '已切换到只读模式')
    },

    // 导出为PDF (兼容旧方法)
    exportToPDF(options) {
      // 直接调用混入的exportToPDF方法，避免递归
      return this.$options.mixins[0].methods.exportToPDF.call(this, options)
    },

    // 导出为Excel (兼容旧方法)
    exportToExcel() {
      // 直接调用混入的exportToExcel方法，避免递归
      return this.$options.mixins[0].methods.exportToExcel.call(this, {
        data: null,
        fileName: null,
        sheetName: '汽车定损收车明细单'
      })
    },

    // 处理PDF导出
    handleExportPDF(element) {
      this.exportToPDF()
    },
    // 刷新表格数据
    refreshSpreadsheet(options = { forceFullRefresh: true }) {
      if (this.$refs.receiptView) {
        this.$refs.receiptView.handleRefresh(options)
      }
    },

    // 处理刷新事件
    handleRefresh() {
      this.initServiceTypeDict()
    },

    // 处理导出事件
    handleExported(data) {
      console.log('导出完成:', data)
    },

    // 处理错误事件
    handleError(error) {
      console.error('组件错误:', error)
      this.$message.error('操作失败: ' + error.message)
    },
  },

  // watch监听器已在上方定义，此处移除重复的监听器以避免无限循环
}
</script>

<style lang="scss" scoped>
.cloud-receipt-container {
  width: 100%;
  min-height: 600px;
  background: white;
  padding: 20px;
  font-family: 'DengXian', sans-serif;
}



.attachment-section {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;

  .attachment-title {
    text-align: center;
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
      margin: 0;
    }
  }

  .image-container {
    margin: 15px 0;
    text-align: center;
  }
}

/* 打印样式优化 */
@media print {
  .cloud-receipt-container {
    padding: 0 !important;
    margin: 0 !important;
    box-shadow: none !important;
    border: none !important;
    background: white !important;
  }

  /* 确保页面顶部没有多余空白 */
  body {
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 隐藏可能的页面标题或导航 */
  .app-main, .main-container, .page-header {
    padding: 0 !important;
    margin: 0 !important;
  }

  /* 隐藏布局组件 */
  .app-wrapper .sidebar-container,
  .app-wrapper .navbar,
  .app-wrapper .tags-view,
  .sidebar-container,
  .navbar,
  .tags-view,
  .fixed-header,
  .right-panel {
    display: none !important;
  }

  /* 确保主容器占满整个页面 */
  .app-wrapper,
  .main-container {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    height: auto !important;
  }

  /* 确保header-actions插槽中的所有按钮都被隐藏 */
  .el-button {
    display: none !important;
  }

  /* 隐藏所有操作相关的元素 */
  [class*="action"], [class*="button"], [class*="operation"] {
    display: none !important;
  }

  .attachment-section {
    border: none !important;
    box-shadow: none !important;
    page-break-inside: avoid;
  }

  /* 确保表格在打印时正确显示 */
  .univer-container {
    width: 100% !important;
    height: auto !important;
    border: none !important;
  }

  /* 强制分页 */
  .page-break {
    page-break-before: always;
  }

  /* 避免分页 */
  .avoid-break {
    page-break-inside: avoid;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cloud-receipt-container {
    padding: 10px;
  }
}
</style>
