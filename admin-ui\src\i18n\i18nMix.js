import { listAllLanguageByLocal } from '@/api/manage/language'
import i18n from '@/i18n/i18n'
import { isValidString,mergeAndSwap } from '@/utils'
import store from '@/store'

/**
 * 确保语言包加载完成 (带缓存和请求锁)
 * @param {string} locale 语言代码
 * @param {boolean} forceUpdate 是否强制更新
 */
export async function ensureLocaleLoaded(locale, forceUpdate = false) {
  // 优先从 Vuex 获取状态
  if (!forceUpdate && store.getters['i18nStore/isLoading']) {
    return
  }

  try {
    // 设置加载状态，防止重复请求
    store.commit('i18nStore/setLoading', {  loading: true})
    const response = await listAllLanguageByLocal()
    const res = transformApiResponse(response)

    // 合并语言包
    i18n.mergeLocaleMessage(locale, res[locale])
    let textToIdCache= mergeAndSwap(i18n.messages.zh,res.zh);
    // 更新加载状态
    store.commit('i18nStore/markLocaleLoaded', {
      messages: i18n.messages[locale],
      textToIdCache:textToIdCache,
    })
  } catch (error) {
    console.error('远程翻译加载失败:', error)
    store.commit('i18nStore/setError', { error })
    throw error // 抛出错误以便调用方处理
  }
}

/**
 * 转换API响应数据格式
 * @param {Object} response API响应
 * @returns {Object} 按语言分类的消息对象
 */
function transformApiResponse(response) {
  return response.rows.reduce((acc, item) => {
    acc.zh = acc.zh || {}; acc.zh[item.id] = item.cnName
    acc.en = acc.en || {}; acc.en[item.id] = item.enName
    acc.ru = acc.ru || {}; acc.ru[item.id] = item.ruName
    acc.ka = acc.ka || {}; acc.ka[item.id] = item.geName
    return acc
  }, {})
}

//const textToIdCache = new Map(); // 缓存中文文本对应的 id
/**
 * 通过 id 获取翻译（需确保 id 在翻译文件中存在）
 */
export async function tMix(id) {
  await ensureLocaleLoaded(i18n.locale);
  const translation = i18n.t(id);
  // 如果翻译结果是 id 本身，说明未定义，返回空字符串避免警告
  return translation === id ? '' : translation;
}

/**
 * 通过中文文本获取翻译（自动匹配第一个符合条件的 id）
 */
export function strConvert(zhText) {

  if(!isValidString(zhText)){
    return "";
  }

  return store.getters['i18nStore/getMessage'](zhText);
}


export function strConvertLangeTitle1(){
  if(i18n.locale==="en"){
    return "Note: After editing content, please execute \"Join\" operation first. Data will be categorized into the left service list. Data will not be saved until \"Confirm Submit\""
  }else  if(i18n.locale==="ru"){
    return "Примечание: После редактирования содержимого сначала выполните операцию \"Присоединиться\". Данные будут классифицированы в левый список услуг. Данные не будут сохранены до \"Подтвердить отправку\""
  }else  if(i18n.locale==="ka"){
    return "შენიშვნა: შინაარსის რედაქტირების შემდეგ, ჯერ შეასრულეთ \"შეერთების\" ოპერაცია. მონაცემები კლასიფიცირდება მარცხენა სერვისების სიაში. მონაცემები არ შეინახება \"დადასტურება გაგზავნამდე\""
  }else {
    return "注意：编辑内容后，请先执行\"加入\"操作，数据将被归类至左侧服务列表中，在未\"确认提交\"前，数据将不会被保存";
  }
}


export function strConvertLangeTitle2(){
  if(i18n.locale==="en"){
    return " already exists in the right list, please remove and try again"
  }else  if(i18n.locale==="ru"){
    return " уже существует в правом списке, пожалуйста, удалите и попробуйте снова"
  }else  if(i18n.locale==="ka"){
    return " უკვე არსებობს მარჯვენა სიაში, გთხოვთ ამოიღოთ და სცადოთ ხელახლა"
  }else {
    return " 已存在于右侧列表中 请移除后 重新再式";
  }
}


export function strConvertLangeTitle3(){
  if(i18n.locale==="en"){
    return "Note: \"Completed settlement\" work orders can be viewed in the \"Work Order List\", and settlement document reprinting can also be handled here"
  }else  if(i18n.locale==="ru"){
    return "Примечание: Заявки с \"Завершенным расчетом\" можно просмотреть в \"Списке заявок\", здесь также можно обработать повторную печать расчетных документов"
  }else  if(i18n.locale==="ka"){
    return "შენიშვნა: \"დასრულებული გადახდის\" სამუშაო შეკვეთები შეიძლება ნახოთ \"სამუშაო შეკვეთების სიაში\", აქ ასევე შეიძლება დამუშავდეს გადახდის დოკუმენტების ხელახალი ბეჭდვა"
  }else {
    return "注意：\"完成结算\"  的工单，可以在《工单列表》中查看，结算单据补打操作也可在此处理";
  }
}
