/**
 * 语言样式切换混入
 * 提供全局的语言样式切换功能，减少对现有组件的侵入
 * 使用方式：在组件中引入此混入即可自动获得语言样式切换能力
 */

import Cookies from 'js-cookie'

export default {
  data() {
    return {
      // 当前语言
      currentLanguage: 'zh',
      // 支持的语言列表
      supportedLanguages: ['zh', 'en', 'ar', 'ru']
    }
  },

  computed: {
    /**
     * 获取当前语言对应的CSS类名
     * @returns {string} 语言CSS类名
     */
    languageClass() {
      return `lang-${this.currentLanguage}`
    },

    /**
     * 判断是否为中文
     * @returns {boolean} 是否为中文
     */
    isZhLanguage() {
      return this.currentLanguage === 'zh'
    },

    /**
     * 判断是否为RTL语言（如阿拉伯语）
     * @returns {boolean} 是否为RTL语言
     */
    isRtlLanguage() {
      return this.currentLanguage === 'ar'
    },

    /**
     * 获取适合当前语言的按钮尺寸
     * @returns {string} 按钮尺寸
     */
    adaptiveButtonSize() {
      return this.currentLanguage === 'zh' ? 'medium' : 'small'
    },

    /**
     * 获取适合当前语言的栅格配置
     * @returns {object} 栅格配置
     */
    adaptiveGridConfig() {
      const configs = {
        zh: { xs: 24, sm: 12, md: 8, lg: 8, xl: 8 },
        en: { xs: 24, sm: 12, md: 8, lg: 8, xl: 8 },
        ar: { xs: 24, sm: 12, md: 8, lg: 8, xl: 8 },
        ru: { xs: 24, sm: 12, md: 8, lg: 8, xl: 8 }
      }
      return configs[this.currentLanguage] || configs.zh
    }
  },

  watch: {
    /**
     * 监听语言变化，自动更新body的CSS类
     * @param {string} newLang 新语言
     * @param {string} oldLang 旧语言
     */
    currentLanguage(newLang, oldLang) {
      this.updateBodyLanguageClass(newLang, oldLang)
    }
  },

  created() {
    // 组件创建时初始化语言
    this.initLanguage()
  },

  mounted() {
    // 组件挂载时设置body的语言类
    this.updateBodyLanguageClass(this.currentLanguage)
  },

  methods: {
    /**
     * 初始化语言设置
     */
    initLanguage() {
      const cookieLanguage = Cookies.get('language')
      if (cookieLanguage && this.supportedLanguages.includes(cookieLanguage)) {
        this.currentLanguage = cookieLanguage
      } else {
        // 默认设置为中文
        this.currentLanguage = 'zh'
        Cookies.set('language', 'zh')
      }
    },

    /**
     * 切换语言
     * @param {string} language 目标语言
     */
    switchLanguage(language) {
      if (this.supportedLanguages.includes(language)) {
        this.currentLanguage = language
        Cookies.set('language', language)
        
        // 触发自定义事件，通知其他组件语言已切换
        this.$emit('language-changed', language)
        
        // 如果存在全局事件总线，也可以通过它通知
        if (this.$bus) {
          this.$bus.$emit('language-changed', language)
        }
      }
    },

    /**
     * 更新body元素的语言CSS类
     * @param {string} newLang 新语言
     * @param {string} oldLang 旧语言
     */
    updateBodyLanguageClass(newLang, oldLang) {
      const body = document.body
      
      // 移除旧的语言类
      if (oldLang) {
        body.classList.remove(`lang-${oldLang}`)
      }
      
      // 添加新的语言类
      if (newLang) {
        body.classList.add(`lang-${newLang}`)
      }
      
      // 设置文档方向属性
      if (this.isRtlLanguage) {
        document.documentElement.setAttribute('dir', 'rtl')
      } else {
        document.documentElement.setAttribute('dir', 'ltr')
      }
    },

    /**
     * 获取适合当前语言的样式对象
     * @param {object} styleConfig 样式配置对象
     * @returns {object} 适合当前语言的样式
     */
    getAdaptiveStyle(styleConfig) {
      if (!styleConfig || typeof styleConfig !== 'object') {
        return {}
      }
      
      // 如果配置中有当前语言的专属样式，使用专属样式
      if (styleConfig[this.currentLanguage]) {
        return { ...styleConfig.default, ...styleConfig[this.currentLanguage] }
      }
      
      // 否则使用默认样式
      return styleConfig.default || {}
    },

    /**
     * 获取适合当前语言的CSS类列表
     * @param {object} classConfig 类配置对象
     * @returns {array} CSS类列表
     */
    getAdaptiveClasses(classConfig) {
      if (!classConfig || typeof classConfig !== 'object') {
        return []
      }
      
      const classes = classConfig.default || []
      const languageClasses = classConfig[this.currentLanguage] || []
      
      return [...classes, ...languageClasses]
    }
  }
}