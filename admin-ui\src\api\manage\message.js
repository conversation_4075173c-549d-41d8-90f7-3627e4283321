import request from '@/utils/request'

// 查询网站消息列表
export function listMessage(query) {
  return request({
    url: '/manage/message/list',
    method: 'get',
    params: query
  })
}

// 查询网站消息详细
export function getMessage(sessionId) {
  return request({
    url: '/manage/message/' + sessionId,
    method: 'get'
  })
}


// 清理未读消息，并且获取用户信息
export function removeNoReaderMessage(sessionId) {
  return request({
    url: '/manage/message/removeNoReaderMessage/' + sessionId,
    method: 'get'
  })
}

// 新增网站消息
export function addMessage(data) {
  return request({
    url: '/manage/message',
    method: 'post',
    data: data
  })
}

// 修改网站消息
export function updateMessage(data) {
  return request({
    url: '/manage/message',
    method: 'put',
    data: data
  })
}

// 删除网站消息
export function delMessage(sessionId) {
  return request({
    url: '/manage/message/' + sessionId,
    method: 'delete'
  })
}
