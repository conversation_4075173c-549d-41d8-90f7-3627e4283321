<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('支出单号')" prop="expenditureId">
        <el-input
          v-model="queryParams.expenditureId"
          :placeholder="strConvert('请输入支出单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('帐变类型')" prop="accountType">
        <el-select v-model="queryParams.accountType" :placeholder="strConvert('请选择帐变类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_financial_expenditure_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('关联单号')" prop="orderId">
        <el-input
          v-model="queryParams.orderId"
          :placeholder="strConvert('请输入关联单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
<!--      <el-form-item :label="strConvert('用户名')" prop="userName">
        <el-input
          v-model="queryParams.userName"
          :placeholder="strConvert('请输入用户名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item :label="strConvert('创建者')" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          :placeholder="strConvert('请输入创建者')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"

          icon="el-icon-plus"
          size="medium"
          @click="handleAdd"
          v-hasPermi="['manage:expenditure:add']"
        >{{ strConvert('支出登记') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"

          icon="el-icon-download"
          size="medium"
          @click="handleExport"
          v-hasPermi="['manage:expenditure:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="expenditureList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('支出单号')" align="center" prop="expenditureId" />
      <el-table-column :label="strConvert('帐变类型')" align="center" prop="accountType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_financial_expenditure_type" :value="scope.row.accountType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('金额')" align="center" prop="amount" >
        <template slot-scope="scope">
          <span>{{ scope.row.amount+" GEL" }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('支出说明')" align="center" prop="centext" />
      <el-table-column :label="strConvert('关联单号')" align="center" prop="orderId" />
      <el-table-column :label="strConvert('图像集')" align="center" prop="images" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.images" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('创建者')" align="center" prop="createBy" />
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('备注')" align="center" prop="remark" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改资金支出登记对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('帐变类型')" prop="accountType">
              <el-select v-model="form.accountType" :placeholder="strConvert('请选择帐变类型')">
                <el-option
                  v-for="dict in dict.type.t_financial_expenditure_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="strConvert('金额')" prop="amount">
              <el-input v-model="form.amount" style="width: 220px" :placeholder="strConvert('请输入金额')" /><span> GEL</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('关联单号')" prop="orderId">
              <el-input v-model="form.orderId" :placeholder="strConvert('请输入关联单号')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="strConvert('图像集')" prop="images">
              <image-upload
                :enable-image-compress="true"
                :max-image-size="100"
                :image-quality="0.7"
                :max-image-width="800"
                :max-image-height="800"
                v-model="form.images"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-form-item :label="strConvert('支出说明')" prop="centext">
            <el-input v-model="form.centext" type="textarea" rows="3" :placeholder="strConvert('请输入内容')" />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { listExpenditure, getExpenditure, delExpenditure, addExpenditure, updateExpenditure } from "@/api/manage/expenditure"

export default {
  name: "Expenditure",
  dicts: ['t_order_type', 't_financial_expenditure_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 资金支出登记表格数据
      expenditureList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        expenditureId: null,
        accountType: null,
        orderId: null,
        userName: null,
        createBy: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountType: [
          { required: true, message: "帐变类型不能为空", trigger: "change" }
        ],
        amount: [
          { required: true, message: "金额不能为空", trigger: "blur" }
        ],
        centext: [
          { required: true, message: "支出说明不能为空", trigger: "blur" }
        ],
        orderType: [
          { required: true, message: "单据类型不能为空", trigger: "change" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询资金支出登记列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      listExpenditure(this.queryParams).then(response => {
        this.expenditureList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        expenditureId: null,
        accountType: null,
        amount: null,
        centext: null,
        orderType:'3',
        orderId: null,
        images: null,
        userId: null,
        userName: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.expenditureId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加资金支出登记"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const expenditureId = row.expenditureId || this.ids
      getExpenditure(expenditureId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改资金支出登记"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.expenditureId != null) {
            updateExpenditure(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addExpenditure(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const expenditureIds = row.expenditureId || this.ids
      this.$modal.confirm('是否确认删除资金支出登记编号为"' + expenditureIds + '"的数据项？').then(function() {
        return delExpenditure(expenditureIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/expenditure/export', {
        ...this.queryParams
      }, `expenditure_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
