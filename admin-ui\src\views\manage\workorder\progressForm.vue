<script >
import { isValidString } from '@/utils'
import { listParts } from '@/api/manage/parts'
import { getWorkorder, updateTWorkOrderDetail } from '@/api/manage/workorder'
import CustomizeTable from '@/views/manage/workorder/components/quesitonTable.vue'
import { listQuestion } from '@/api/system/question'
import { parseTime } from '@/utils/usedcar'
import WorkerDetailTable from '@/views/manage/workorder/components/workerDetailTable.vue'
import ImageCustomPreview from '@/components/ImageCustomPreview/index.vue'
import publicCountMixin from '@/mixins/publicCountMixin'
import { strConvert } from '@/i18n/i18nMix'

export default {
  name: "progressForm",
  components: { ImageCustomPreview, WorkerDetailTable, CustomizeTable },
  mixins: [publicCountMixin],
  dicts: ['t_work_order_detail_service_type','t_work_question_service_type'],
  data() {
    return {
      serviceId: null,
      isMobile: false,
      totalCountAmount: 0.00,//工单总计
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      T_WORK_ORDER_SERVICE_TYPE:[],
      checkedTWorkOrderDetail: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      restCustomer: {},
      customerDiscountLevel:'0',//用户折扣
      restQuestion: {},
      // 总条数
      total: 0,
      // 工单详情表格数据
      tWorkOrderDetailList: [],
      tWorkPartsDetailList:[],
      checkRadioQuestion:'1',
      // 手动添加新服务项
      newWorkerOrderDetailList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      checkboxDetail:[],
      // 表单参数
      form: {
      },
      // 表单校验
      // 表单校验
      rules: {
        customerNickName: [
          { required: true, message: this.strConvert('客户姓名不能为空'), trigger: "blur" }
        ],
        title: [
          { required: true, message: this.strConvert('标题不能为空'), trigger: "blur" }
        ],
        comment: [
          { required: true, message: this.strConvert('问题描述不能为空'), trigger: "blur" }
        ],
        phonenumber: [
          { required: true, message: this.strConvert('客户电话不能为空'), trigger: "blur" }
        ],
        advancePayment: [
          { required: true, message: this.strConvert('预收款不能为空'), trigger: "blur" }
        ],
        carPlateNumber: [
          { required: true, message: this.strConvert('车牌号不能为空'), trigger: "blur" }
        ],
      },
      queryListParts:null,
      printOpen:false,
      totalPartsAmount:0.00,
    }
  },
  created() {
    this.initData();
  },
  computed:{
    // 过滤掉不需要显示的行
    filterServiceTableData(){
      return this.T_WORK_ORDER_SERVICE_TYPE.filter(item => item[item.value]!==undefined&&item[item.value].length>0 );
    },
  },
  mounted() {
  },
  watch:{
    'dict.type.t_work_order_detail_service_type': {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          newVal.map(item=>{
            this.T_WORK_ORDER_SERVICE_TYPE.push({
              isShowCount:item.raw.cssClass,
              ratio:parseInt(item.raw.remark),
              value:item.value,
              label:item.label,
              num:0,
              [item.value]:[],
              amount:0
            })
          });
        }
      }
    }
  },
  methods: {
    strConvert,
    isValidString,
    parseTime,
    initData(){
      this.reset();
      this.serviceId = this.$route.params && this.$route.params.serviceId;
      getWorkorder(this.serviceId).then(response => {
        if(response.data!==undefined){
          this.form = response.data;
          console.log(this.form)

          if(response.data.tWorkPartsDetailList!==undefined){
            this.tWorkPartsDetailList=response.data.tWorkPartsDetailList;
            this.tWorkPartsDetailList.map(item=>{
              this.totalPartsAmount=this.totalPartsAmount+parseFloat(item.amount)
            })
          }
          this.createWorkerDetailForm(response);
        }else {
          this.checkboxDetail=["1"];
        }
      })
      this.handleAddTWorkOrderDetail();
    },
    // 取消按钮
    cancel() {
      const obj = { path: "/workorder/progress" }
      this.$tab.closeOpenPage(obj)
    },

    // 表单重置
    reset() {
      this.form = {
        serviceId:  this.serviceId,
        title: null,
        businessType: "0",
        operatorType: "0",
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        phonenumber:null,
        comment:null,
        expectedTime:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        fastRemark:null,
        brand: null,//品牌
        color: null,//颜色
        advancePayment:0,//预收金额
        discountAmount: 0,//折后金额
        totalAmount: 0,//总金额
        balancePayment: 0,//再付金额
        balance:0,//余额
      }
      this.tWorkOrderDetailList=[]
      this.newWorkerOrderDetailList=[]
      this.resetForm("form")
    },
    handleServiceChange(val) {
      this.checkRadioQuestion=val;
      this.newWorkerOrderDetailList=[];
      this.handleAddTWorkOrderDetail();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.checkboxDetail.length===0){
            this.$modal.msgError(this.strConvert('至少应选择一项服务'))
            return;
          }
          this.form.tWorkOrderDetailList = this.tWorkOrderDetailList
          updateTWorkOrderDetail(this.form).then(response => {
            if(response.code===200){
              this.$modal.msgSuccess(this.strConvert('修改成功'))
              this.cancel()
            }else {
              this.$message.error(response.msg);
            }
          })
        }
      })
    },
    handleSelectQuestion(item,row) {
      row.questionId=item.id;
      row.questionName=this.restQuestion[item.id].questionName;
      row.amount=this.restQuestion[item.id].amount;
    },
    updateTotalAmount(totalAmount){
      this.totalCountAmount=this.totalCountAmount+parseFloat(totalAmount);
    },
    /* 查找用户信息 */
    querySearchQuestion(queryString, cb) {
      if(isValidString(queryString)){
        listQuestion({"questionName":queryString,"questionType":this.checkRadioQuestion }).then(response => {
          this.restQuestion = {};
          var results=[];
          if(response.rows.length>0){
            results = response.rows.map(item=>{
              this.restQuestion[item.questionId]=item;
              return {"value":item.questionName,"id":item.questionId}
            })
          }
          cb(results);
        })
      }
    },
    handleSelectParts(currentValue, select) {
      if (isValidString(currentValue)&&this.queryListParts!=null) {
        const foundItem = this.queryListParts.find(item => item.value === currentValue);
        if (foundItem) {
          select.partsId = foundItem.id;
        }
      }
    },
    /* 查找配件信息 */
    querySearchParts(queryString) {
      if (queryString === '') {
        this.queryListParts = []; // 输入为空时清空历史数据
        return;
      }
      if(isValidString(queryString)) {
        listParts({ "partsName": queryString }).then(response => {
          if (response.rows.length > 0) {
            this.queryListParts = response.rows.map(item => {
              return {
                value: item.partsName,
                id: String(item.partsId),
                partsType: item.partsType,
                num: item.num,
              }
            });
          }
        })
      }
    },
    /** 工单详情序号 */
    rowTWorkOrderDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 工单定损项添加按钮操作 */
    handleAddTWorkOrderDetail() {
      let obj = {}
      obj.questionId = ""
      obj.questionType = this.checkRadioQuestion
      obj.questionName = ""
      obj.questionComment = ""
      obj.num=1
      obj.price=1
      obj.amount = 1
      obj.currencyType = "GEL"
      obj.remark = ""
      this.newWorkerOrderDetailList.push(obj)
    },
    /** 工单详情删除按钮操作 */
    handleDeleteTWorkOrderDetail() {
      if (this.checkedTWorkOrderDetail.length === 0) {
        this.$modal.msgError(this.strConvert('请先选择要删除的工单详情数据'))
      } else {
        const newWorkerOrderDetailList = this.newWorkerOrderDetailList
        const checkedTWorkOrderDetail = this.checkedTWorkOrderDetail
        this.newWorkerOrderDetailList = newWorkerOrderDetailList.filter(function(item) {
          return checkedTWorkOrderDetail.indexOf(item.index) === -1
        })
      }
    },
    /** 复选框选中数据 */
    handleTWorkOrderDetailSelectionChange(selection) {
      this.checkedTWorkOrderDetail = selection.map(item => item.index)
    },
    isRequired(prop) {
      return this.rules[prop]?.some(rule => rule.required) || false;
    },
    tableRowClassName({row, rowIndex}) {
      if (row.value ==='1'&&row.num>0) {
        return 'warning-row';
      }else if(row.value ==='2'&&row.num>0) {
        return 'success-row';
      }else if(row.value ==='3'&&row.num>0) {
        return 'active-row';
      }else if(row.value ==='4'&&row.num>0) {
        return 'error-row';
      }else if(row.value ==='5'&&row.num>0) {
        return 'disabled-row';
      }else if(row.value ==='6'&&row.num>0) {
        return 'mark-row';
      }else if(row.value ==='7'&&row.num>0) {
        return 'urgent-row';
      }else if(row.value ==='8'&&row.num>0) {
        return 'new-row';
      }else if(row.value ==='9'&&row.num>0) {
        return 'nine-row';
      }else if(row.value ==='10'&&row.num>0) {
        return 'ten-row';
      }else if(row.value ==='11'&&row.num>0) {
        return 'elen-row';
      }
      return '';
    }
  }
}
</script>

<template>
  <div class="app-container">
    <el-form ref="form"  label-position='right' :model="form" :rules="rules"  size="mini" class="compact-form" >
      <el-row :gutter="10" class="mb8">
        <el-col :span="12">
          <div style="padding: 8px">
            <div style="margin-bottom: 5px">{{strConvert('受理信息')}}：</div>
            <!-- 第一行：工单标题和编号 -->
            <el-descriptions :column="2" size="mini"  border>
              <el-descriptions-item >
                <template slot="label">
                  <span :class="{'is-required': isRequired('title')}">{{strConvert('工单标题')}}：</span>
                </template>
                <div class="no-margin">
                  {{form.title}}
                </div>
              </el-descriptions-item>
              <el-descriptions-item :label="strConvert('工单编号') + '：'">
                <div class="no-margin">
                  {{form.serviceId}}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 第二行：客户信息 -->
            <el-descriptions  :column="3" border size="mini" class="mt-10">
              <el-descriptions-item >
                <template slot="label">
                  <span :class="{'is-required': isRequired('customerNickName')}">{{strConvert('客户姓名')}}：</span>
                </template>
                <div class="no-margin">
                  {{form.customerNickName}}
                </div>
              </el-descriptions-item>
              <el-descriptions-item >
                <template slot="label">
                  <span :class="{'is-required': isRequired('brand')}">{{strConvert('品牌')}}：</span>
                </template>
                <div class="no-margin">
                  {{form.brand}}
                </div>
              </el-descriptions-item>
              <el-descriptions-item >
                <template slot="label">
                  <span :class="{'is-required': isRequired('color')}">{{strConvert('颜色')}}：</span>
                </template>
                <div class="no-margin">
                  {{form.color}}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 第三行：联系方式和车辆信息 -->
            <el-descriptions :column="2" size="mini" border class="mt-10">
              <el-descriptions-item  >
                <template slot="label">
                  <span :class="{'is-required': isRequired('phonenumber')}">{{strConvert('客户电话')}}：</span>
                </template>
                <div class="no-margin">
                  {{form.phonenumber}}
                </div>
              </el-descriptions-item>
              <el-descriptions-item >
                <template slot="label">
                  <span :class="{'is-required': isRequired('carPlateNumber')}">{{strConvert('车牌号')}}：</span>
                </template>
                <div class="no-margin">
                  {{form.carPlateNumber}}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 第四行：日期信息 -->
            <el-descriptions :column="2" border size="mini" class="mt-10">
              <el-descriptions-item>
                <template slot="label">
                  <span :class="{'is-required': isRequired('createTime')}">{{strConvert('进厂日期')}}：</span>
                </template>
                <span>{{ parseTime(form.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </el-descriptions-item>
              <el-descriptions-item >
                <template slot="label">
                  <span :class="{'is-required': isRequired('expectedTime')}">{{strConvert('取车日期')}}：</span>
                </template>
                  <span>{{ parseTime(form.expectedTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </el-descriptions-item>
            </el-descriptions>

            <!-- 第六行：支付信息 -->
<!--            <el-descriptions :column="2" border size="mini" class="mt-10">
              <el-descriptions-item  >
                <template slot="label">
                  <span :class="{'is-required': isRequired('advancePayment')}">{{ strConvert('预收金额：') }}</span>
                </template>
                <div class="no-margin">
                  {{form.advancePayment}}
                </div>
              </el-descriptions-item>
              <el-descriptions-item  >
                <template slot="label">
                  <span :class="{'is-required': isRequired('balancePayment')}">{{ strConvert('折扣后含税金额：') }}</span>
                </template>
                <div class="no-margin">
                  {{form.balancePayment}}
                </div>
              </el-descriptions-item>
            </el-descriptions>

            &lt;!&ndash; 第五行：金额信息 &ndash;&gt;
            <el-descriptions :column="3" border size="mini" class="mt-10">
              <el-descriptions-item :label="strConvert('总额：')">
                <div class="no-margin">
                  {{form.totalAmount}}
                </div>
              </el-descriptions-item>
              <el-descriptions-item :label="strConvert('折后金额：')">
                <div class="no-margin">
                  {{form.discountAmount}}
                </div>
              </el-descriptions-item>
              <el-descriptions-item :label="strConvert('余额：')">
                <div class="no-margin">
                  {{form.balance}}
                </div>
              </el-descriptions-item>
            </el-descriptions>-->

            <!-- 第七行：问题描述和图片 -->
            <el-descriptions :column="1" border size="mini" class="mt-10">
              <el-descriptions-item :label="strConvert('问题描述') + '：'" v-if="isValidString(form.comment)">
                <div class="no-margin">
                  {{form.comment}}
                </div>
              </el-descriptions-item>
            </el-descriptions>
            <el-descriptions :column="1" border size="mini" class="mt-10">
              <el-descriptions-item  v-if="isValidString(form.images)">
                <template slot="label">
                  <div style="width: 80px;text-align: right">
                    <span :class="{'is-required': isRequired('images')}">{{strConvert('图像集')}}：</span>
                  </div>
                </template>
                <el-form-item prop="images"  class="no-margin"   style="width:450px" >
                  <image-custom-preview :src="form.images" :width="100" :height="100"/>
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>

            <el-divider content-position="center" ><span style="font-weight: bold">{{strConvert('维修配件列表')}}</span></el-divider>
            <el-table border :data="tWorkPartsDetailList" :row-class-name="rowTWorkOrderDetailIndex" @selection-change="handleTWorkOrderDetailSelectionChange" ref="tWorkOrderDetail">
  <!--            <el-table-column :label="strConvert('序号')" align="center" prop="index" width="50"/>-->
              <el-table-column :label="strConvert('配件编号')" prop="partsId" width="95"/>
              <el-table-column :label="strConvert('配件名称')" prop="partsName" width="120" />
              <el-table-column :label="strConvert('规格')" prop="norm" width="120"/>
              <el-table-column :label="strConvert('数量')" prop="num" width="80"/>
              <el-table-column :label="strConvert('单价')" prop="price" width="80"/>
              <el-table-column :label="strConvert('金额')" prop="amount" width="80" />
              <el-table-column :label="strConvert('操作描述')" prop="optionComment" width="110"/>
            </el-table>
            <el-row :gutter="10" class="mb8">
              <el-col :span="6" :offset="18">
                    <div style="margin-top: 2px">{{strConvert('配件合计')}}：{{totalPartsAmount.toFixed(2)}}GEL</div>
              </el-col>
            </el-row>

          </div>
        </el-col>


        <el-col :span="12">
          <el-divider content-position="center" ><span style="font-weight: bold">{{strConvert('维修服务列表')}}</span></el-divider>
          <el-table
            size="mini"
            :data="filterServiceTableData"
            row-key="value"
            :row-class-name="tableRowClassName"
            :expand-row-keys="checkboxDetail"
            style="width: 100%">
            <el-table-column type="expand">
              <template slot-scope="scope">
                <div class="tableChild">
                  <worker-detail-table
                    :count-amount="form.mainTotalAmount"
                    :total-amount="scope.row.totalAmount"
                    :isOption="false"
                    :is-summary="!isValidString(scope.row.isShowCount)"
                    :ratio="scope.row.ratio"
                    :table-name="scope.row.label"
                    :save-order-detail-list="scope.row[scope.row.value]" />
                </div>
              </template>
            </el-table-column>
            <el-table-column width="260"
                             :label="strConvert('服务类型')"
                             prop="label">
            </el-table-column>
            <el-table-column
              :label="strConvert('服务项')"
              prop="num">
            </el-table-column>
            <el-table-column
              :label="strConvert('合计')"
              prop="amount">
            </el-table-column>
          </el-table>
          <el-row :gutter="10" class="mb8">
            <el-col :span="8" :offset="16">
              {{strConvert('维修服务总计')}}：<span style="color: #e60000">{{totalCountAmount.toFixed(2)}}</span> GEL
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <div  style="text-align: center;margin: 15px ">
        <el-button  type="warning" @click="cancel">{{strConvert('关闭')}}</el-button>
      </div>
    </el-form>
  </div>
</template>


<style scoped lang="scss">

.compact-form {

  .el-form-item {
    display: flex;           /* 弹性布局 */
    margin-bottom: 18px;     /* 底部间距 */
  }

  .el-form-item__label {
    text-align: center;       /* 标签右对齐 */
    padding: 0 12px 0 0;     /* 右侧间距 */
    flex-shrink: 0;          /* 禁止收缩 */

    display: none;
  }

  ::v-deep .el-form-item__content{
    width: 100%;
    text-align: center;
  }

  /* 隐藏表单自带的必填星号 */
  /* .el-form-item.is-required .el-form-item__label:before {
     display: none;

   }*/

  ::v-deep .el-descriptions-item__cell .el-descriptions-item__label .is-bordered-label{
    text-align: right;
  }

  ::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell{
    width: 90px;
    color: #464545;
  }

  ::v-deep .el-descriptions--mini.is-bordered .el-descriptions-item__cell{
    padding: 5px 10px;
  }

  .el-descriptions-item__label{
    text-align: right;
  }

  /* 在描述项标签上显示星号 */
  .el-descriptions-item__label .is-required:before {
    content: "*";
    width: 90px;
    color: #f56c6c;
    margin-right: 4px;

  }

  .no-margin {
    margin-bottom: 0;
  }

  ::v-deep .el-radio-button--small .el-radio-button__inner{
    padding: 9px 10px;
  }
  .tableChild{
    margin: 0 auto;     /* 水平居中 */
    width: fit-content; /* 宽度自适应内容 */
    text-align: center; /* 内部文本居中（可选） */
  }



  /* 全局优化：确保文字在彩色行上清晰可见 */
  ::v-deep  .el-table [class*="-row"] .cell {
    color: #333 !important;
    font-weight: normal;
  }


  ::v-deep .el-table  {
    /* 1. 警示黄色（待处理/需注意） */
    .warning-row {
      background: #FFF8E6;
    }

    /* 2. 成功绿色（已完成/成功状态） */
    .success-row {
      background: #F0F9EB;
    }

    /* 3. 高亮蓝色（当前选中/活跃行） */
    .active-row {
      background: #E6F7FF;
    }

    /* 4. 错误红色（异常/失败行） */
    .error-row {
      background: #FFECE6;
    }

    /* 5. 中性灰色（禁用/次要行） */
    .disabled-row {
      background: #eafdfb;
    }

    /* 6. 柔和紫色（特殊标记行） */
    .mark-row {
      background: #F9F0FF;
    }

    /* 7. 活力橙色（高优先级行） */
    .urgent-row {
      background: #FFF2E6;
    }

    /* 8. 清新青色（新增/更新行） */
    .new-row {
      background: #E6FFFB;
    }

    /* 9. 清新青色（新增/更新行） */
    .nine-row {
      background: #dbd6fd;
    }
    /* 10. 清新青色（新增/更新行） */
    .ten-row {
      background: #faffe6;
    }
    /* 11. 清新青色（新增/更新行） */
    .elen-row {
      background: #e6e6ff;
    }


    .el-table--mini .el-table__cell {
      padding: 5px 0;
    }

  }



  /* .el-table .success-row {
     background: #f0f9eb;
   }*/
}
</style>
