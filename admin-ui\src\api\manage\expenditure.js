import request from '@/utils/request'

// 查询资金支出登记列表
export function listExpenditure(query) {
  return request({
    url: '/manage/expenditure/list',
    method: 'get',
    params: query
  })
}

// 查询资金支出登记详细
export function getExpenditure(expenditureId) {
  return request({
    url: '/manage/expenditure/' + expenditureId,
    method: 'get'
  })
}

// 新增资金支出登记
export function addExpenditure(data) {
  return request({
    url: '/manage/expenditure',
    method: 'post',
    data: data
  })
}

// 修改资金支出登记
export function updateExpenditure(data) {
  return request({
    url: '/manage/expenditure',
    method: 'put',
    data: data
  })
}

// 删除资金支出登记
export function delExpenditure(expenditureId) {
  return request({
    url: '/manage/expenditure/' + expenditureId,
    method: 'delete'
  })
}
