<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item :label="strConvert('申请单号')" prop="resRequestId">
        <el-input
          v-model="queryParams.resRequestId"
          :placeholder="strConvert('请输入申请单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('物件类型')" prop="resType">
        <el-select v-model="queryParams.resType" :placeholder="strConvert('请选择物件类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_res_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('审批状态')" prop="approvalStatus">
        <el-select v-model="queryParams.approvalStatus" :placeholder="strConvert('请选择审批状态')" clearable>
          <el-option
            v-for="dict in dict.type.t_approval_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('工单号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('审批时间')" prop="approvalTime">
        <el-date-picker clearable
          v-model="queryParams.approvalTime"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="strConvert('请选择审批时间')">
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="strConvert('创建者')" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          :placeholder="strConvert('请输入创建者')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-:placeholder="strConvert('开始日期')"
          end-:placeholder="strConvert('结束日期')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
<!--      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-edit"
          size="medium"
          :disabled="single"
          @click="approval"
          v-hasPermi="['manage:request:edit']"
        >{{ strConvert('审批') }}</el-button>
      </el-col>-->
      <el-col :span="15">
        <span style="color: red">{{ strConvert('注意：审批通过后，将会扣减申请单中相关物件对应的库存') }}</span>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="requestList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('申请单号')" align="center" prop="resRequestId" />
      <el-table-column :label="strConvert('标题')" align="center" prop="title" />
      <el-table-column :label="strConvert('物件类型')" align="center" prop="resType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_res_type" :value="scope.row.resType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('审批状态')" align="center" width="80" prop="approvalStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_approval_status" :value="scope.row.approvalStatus"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('工单号')" align="center" prop="serviceId" />
      <el-table-column :label="strConvert('申请人')" align="center" width="90" prop="createBy" />
<!--      <el-table-column :label="strConvert('描述')" align="center" prop="comment" />-->
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('审批意见')" align="center" prop="approvalComments" width="100"/>
      <el-table-column :label="strConvert('审批时间')" align="center" prop="approvalTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.approvalTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="medium"
            type="warning"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >{{ strConvert('查看并审批') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />



    <!-- 添加或修改物件申请对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="8">
            <el-form-item :label="strConvert('标题')" prop="title">
              <el-input disabled v-model="form.title" :placeholder="strConvert('请输入标题')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="strConvert('物件类型')" prop="resType">
              <el-select disabled v-model="form.resType" :placeholder="strConvert('请选择物件类型')">
                <el-option
                  v-for="dict in dict.type.t_res_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="strConvert('工单号')" prop="serviceId">
              <el-input disabled v-model="form.serviceId" :placeholder="strConvert('请输入工单号')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="12">
            <el-form-item :label="strConvert('图像集')" prop="images">
              <image-preview :src="form.images" :width="100" :height="100"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item  :label="strConvert('描述')" prop="comment">
              <el-input disabled v-model="form.comment" type="textarea" rows="6" :placeholder="strConvert('请输入内容')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">物件申请详情信息</el-divider>
        <el-table :data="tResRequestDetailList" :row-class-name="rowTResRequestDetailIndex" @selection-change="handleTResRequestDetailSelectionChange" ref="tResRequestDetail">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column :label="strConvert('序号')" align="center" prop="index" width="50"/>
          <el-table-column v-if="form.resType === '1'" :label="strConvert('物料编号')" prop="materialId" width="90"/>
          <el-table-column v-if="form.resType === '1'" :label="strConvert('物料名称')" prop="materialName" />
          <el-table-column v-if="form.resType === '2'" :label="strConvert('配件编号')" prop="partsId" width="90"/>
          <el-table-column v-if="form.resType === '2'" :label="strConvert('配件名称')" prop="partsName" />
          <el-table-column v-if="form.resType === '2'"   :label="strConvert('规格')" prop="norm" width="100"/>
          <el-table-column  :label="strConvert('数量')" prop="num" width="100" />
          <el-table-column :label="strConvert('说明')" prop="comment" />
        </el-table>
      </el-form>

      <el-divider content-position="center">物件申请审批</el-divider>
      <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="80px">
        <el-row :gutter="10" class="mb8">
          <el-col :span="15">
            <el-form-item :label="strConvert('审批状态')" prop="approvalStatus">
              <el-select v-model="approvalForm.approvalStatus" :placeholder="strConvert('请选择审批状态')">
                <el-option
                  v-for="dict in filteredDicts"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-form-item :label="strConvert('审批意见')" prop="approvalComments">
            <el-input v-model="approvalForm.approvalComments"  type="textarea" rows="2" :placeholder="strConvert('请输入审批意见')" />
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFormApproval">{{ strConvert('确 定') }}</el-button>
        <el-button @click="approvalCancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import {
  listRequest,
  getRequest,
  delRequest,
  addRequest,
  updateRequest,
  updateRequestApproval
} from '@/api/manage/request'
import { getInfo } from '@/api/login'

export default {
  name: "Request",
  dicts: ['t_approval_status', 't_res_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedTResRequestDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物件申请表格数据
      requestList: [],
      // 物件申请详情表格数据
      tResRequestDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        resRequestId: null,
        title: null,
        resType: null,
        approvalStatus: null,
        serviceId: null,
        approvalComments: null,
        approvalTime: null,
        createBy: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单参数
      approvalForm: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        resType: [
          { required: true, message: "物件类型不能为空", trigger: "change" }
        ],
      },
      approvalRules: {
        approvalComments: [
          { required: true, message: "审批意见不能为空", trigger: "blur" }
        ],
        approvalStatus: [
          { required: true, message: "审批状态不能为空", trigger: "change" }
        ],
      },
      resRequestId:null,
    }
  },
  computed: {
    filteredDicts() {
      return this.dict.type.t_approval_status.filter(item => {
        return item.label!=='待审批'&&item.label!=='未提交';
      });
    }
  },
  created() {
    this.getList();
  },
  methods: {
    strConvert,

    /** 查询物件申请列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      this.queryParams.approvalStatus="0";
      listRequest(this.queryParams).then(response => {
        this.requestList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        resRequestId: null,
        title: null,
        resType: null,
        operatorType: null,
        approvalStatus: null,
        images: null,
        serviceId: null,
        approvalName: null,
        approvalComments: null,
        approvalTime: null,
        amount: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        comment: null
      }
      this.tResRequestDetailList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection)
      if(selection.length>0){
        this.resRequestId=selection[0].resRequestId;
      }
      this.ids = selection.map(item => item.resRequestId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = strConvert('添加物件申请')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const resRequestId = row.resRequestId || this.ids
      this.resRequestId=resRequestId;
      getRequest(resRequestId).then(response => {
        this.form = response.data
        this.tResRequestDetailList = response.data.tResRequestDetailList
        this.open = true
        this.title = strConvert('物件申请详情')
      })
    },
    // 取消按钮
    approvalCancel() {
      this.open = false
      this.approvalForm = {
        resRequestId: null,
        approvalName: null,
        approvalComments: null,
        approvalStatus:null,
        approvalTime: null
      }
      this.resetForm("approvalForm")
    },
    /** 提交按钮 */
    submitFormApproval() {
        this.$refs["approvalForm"].validate(valid => {
          if (valid) {
            if (this.resRequestId != null) {
              this.approvalForm.resRequestId=this.resRequestId
              this.approvalForm.approvalTime=new Date();
              this.approvalForm.approvalName=this.$store.getters.name;
                //审批通过添加提醒
                if( this.approvalForm.approvalStatus==="1"){
                  this.$modal.confirm('是否确认审批通过？通过后，将同步扣减相应库存？').then(() => {
                    updateRequestApproval(this.approvalForm).then(response => {
                      this.$modal.msgSuccess("审批通过成功")
                      this.open = false
                      this.getList()
                    })
                  }).catch(() => {})
                }else {
                    updateRequestApproval(this.approvalForm).then(response => {
                      this.$modal.msgSuccess("审批驳回成功")
                      this.open = false
                      this.getList()
                    })
                }
            }
          }
        })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const resRequestIds = row.resRequestId || this.ids
      this.$modal.confirm('是否确认删除物件申请编号为"' + resRequestIds + '"的数据项？').then(function() {
        return delRequest(resRequestIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
	/** 物件申请详情序号 */
    rowTResRequestDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 复选框选中数据 */
    handleTResRequestDetailSelectionChange(selection) {
      this.checkedTResRequestDetail = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('manage/request/export', {
        ...this.queryParams
      }, `request_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
