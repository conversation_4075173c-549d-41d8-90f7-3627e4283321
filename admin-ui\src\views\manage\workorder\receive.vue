<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item :label="strConvert('工单编号')" prop="serviceId">
        <el-input
          v-model="queryParams.serviceId"
          :placeholder="strConvert('请输入工单编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('工单标题')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="strConvert('请输入工单标题')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户编号')" prop="customerId">
        <el-input
          v-model="queryParams.customerId"
          :placeholder="strConvert('请输入客户编号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('客户姓名')" prop="customerNickName">
        <el-input
          v-model="queryParams.customerNickName"
          :placeholder="strConvert('请输入客户姓名')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('车牌号')" prop="carPlateNumber">
        <el-input
          v-model="queryParams.carPlateNumber"
          :placeholder="strConvert('请输入车牌号')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="strConvert('创建时间')">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          :start-placeholder="strConvert('开始日期')"
          :end-placeholder="strConvert('结束日期')"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search"  @click="handleQuery">{{strConvert('搜索')}}</el-button>
        <el-button icon="el-icon-refresh"  @click="resetQuery">{{strConvert('重置')}}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          size="medium"
          type="primary"
          :disabled="single"
          icon="el-icon-s-flag"
          @click="handleUpdate"
          v-hasPermi="['manage:workorder:receive']"
        > {{strConvert('派工单登记')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          :disabled="single"
          icon="el-icon-s-check"
          @click="goPrint"
        >{{strConvert('派工单打印')}}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="workorderList"
              @expand-change="openExpandRow"
              ref="singleTable"
              row-key="serviceId"
              :expand-row-keys="selectRow"
              @selection-change="handleSelectionChange" class="mobile-table-adapt">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="expand">
        <template slot-scope="scope">
          <div class="tableChild">
            <el-table  ref="childTable"
                       :ref="'childTable' + scope.row.serviceId"
                       v-loading="tableLoading"
                      :data="tWorkOrderDetailObj[scope.row.serviceId]"
                      border
                      :span-method="(params) => objectSpanMethod(params, tWorkOrderDetailObj[scope.row.serviceId])"
                      :style="{width: '1960px', height: isMobile ? mobileMaxHeight + 'px' : 'auto'}"
                      class="mobile-table-adapt">
<!--              <el-table-column :label="strConvert('服务编号')" align="center" prop="questionId" width="80" />-->
              <el-table-column :label="strConvert('维修服务类型')" align="center" prop="questionType" width="280">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.t_work_order_detail_service_type" :value="scope.row.questionType"/>
                </template>
              </el-table-column>
              <el-table-column width="180" :label="strConvert('服务')" prop="questionName" />
              <el-table-column width="80" :label="strConvert('数量')" prop="num" />
              <el-table-column width="90" :label="strConvert('单价')" prop="price" align="right"/>
              <el-table-column width="90" :label="strConvert('金额')" prop="amount" align="right"/>
              <el-table-column :label="strConvert('进度')" prop="operProgress" :width="isMobile ? '120' : '120'" align="center"  >
                <template slot-scope="scope">
                  <el-progress :text-inside="true" :stroke-width="26"
                               status="success"
                               text-color="#ffff"
                               :percentage="isValidString(scope.row.operProgress)?parseInt(scope.row.operProgress):0"></el-progress>
                </template>
              </el-table-column>
              <el-table-column width="80" :label="strConvert('操作人')" prop="operName" />
              <el-table-column :label="strConvert('操作时间')" align="center" prop="createTime" width="180">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.operTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
              <el-table-column width="100" :label="strConvert('备注')" prop="questionComment" />
              <el-table-column width="450" :label="strConvert('操作')"  align="left" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-dropdown  v-if="scope.row.operProgress!=='100'"  @command="(item)=>handleResultProgress(item,scope.row)">
                    <el-button  icon="el-icon-circle-check"
                                size="medium"
                                type="success"
                                v-hasPermi="['manage:workorder:assign']" >
                      {{strConvert('一键完成')}}<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-for="user in userList" style="width: 125px"
                                        :command="user" :key="user.userId"><span style="color: #e60000">{{ user.nickName }}</span> {{strConvert('操作完成')}}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('工单编号')"  width="155"  align="center" prop="serviceId" />
      <el-table-column :label="strConvert('工单标题')" align="center" prop="title" />
      <el-table-column :label="strConvert('进度')" prop="operProgress" width="150" align="center"  >
        <template slot-scope="scope">
          <el-progress :text-inside="true" :stroke-width="26"
                       status="success"
                       text-color="#ffff"
                       :percentage="isValidString(scope.row.operProgress)?parseInt(scope.row.operProgress):0"></el-progress>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('车牌号')" width="90" align="center" prop="carPlateNumber" />
      <el-table-column :label="strConvert('客户编号')" width="110" align="center" prop="customerId" />
      <el-table-column :label="strConvert('客户姓名')" width="100" align="center" prop="customerNickName" />
<!--      <el-table-column :label="strConvert('消耗时间')" align="center" prop="costTime" />-->
<!--      <el-table-column :label="strConvert('图片')" align="center" width="180" prop="images">
        <template slot-scope="scope">
          <image-preview :src="scope.row.images" :width="50" :height="50"/>
        </template>
      </el-table-column>-->
      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('备注')" align="center" width="200" prop="remark" />
      <el-table-column :label="strConvert('操作')" width="300" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-dropdown @command="(item)=>handleReceive(item,scope.row)">
            <el-button  icon="el-icon-thumb"
                           size="medium"
                           type="warning"
                           v-hasPermi="['manage:workorder:assign']" >
              {{strConvert('工单指派')}}<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="user in userList" style="width: 125px"
                              :command="user" :key="user.userId">{{ user.nickName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button
            size="small"
            icon="el-icon-edit"
            v-hasPermi="['manage:workorder:edit']"
            type="text"
            @click="recall(scope.row.serviceId)"
          >
            {{strConvert('回撤')}}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <receipt show="worker" :serviceId="serviceId" printType="workerReceipt" :OpenPrint.sync="printOpen" />

  </div>
</template>

<script>
import {
  listWorkorder,
  getWorkorder,
  delWorkorder,
  addWorkorder,
  updateWorkorder,
  tWorkOrderAssign, tWorkOrderProgress, tWorkResultProgress
} from '@/api/manage/workorder'
import { listCustomer, addCustomer, getInfoByNickName } from '@/api/manage/customer'
import { getInfo } from '@/api/login'
import acceptReceipt from '@/views/receipt/acceptReceipt.vue'
import WorkerReceipt from '@/views/receipt/workerReceipt.vue'
import { listUser } from '@/api/system/user'
import { formatTime, isValidString } from '@/utils'
import Receipt from '@/views/receipt/components/index.vue'
import { parseTime } from '../../../utils/usedcar'
import { strConvert } from '@/i18n/i18nMix'
export default {
  name: "Workorder",
  components: { Receipt, WorkerReceipt, acceptReceipt },
  dicts: ['t_work_order_detail_service_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      isMobile: false,
      userList:[],
      // 选中数组
      ids: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      restCustomer: [],
      mobileMaxHeight: 500,
      tableLoading:true,
      serviceId:"",
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单记录表格数据
      workorderList: [],
      // 工单详情表格数据
      tWorkOrderDetailList: [],
      tWorkOrderDetailObj: {},
      selectRow:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      printOpen:false,
      // 备注时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        serviceId: null,
        title: null,
        businessType: null,
        carPlateNumber:null,
        customerId: null,
        customerNickName: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      // 表单校验
      rules: {
        customerNickName: [
          { required: true, message: this.strConvert('客户姓名不能为空'), trigger: "blur" }
        ],
        title: [
          { required: true, message: this.strConvert('标题不能为空'), trigger: "blur" }
        ],
        comment: [
          { required: true, message: this.strConvert('问题描述不能为空'), trigger: "blur" }
        ],
        phonenumber: [
          { required: true, message: this.strConvert('客户电话不能为空'), trigger: "blur" }
        ],
        carPlateNumber: [
          { required: true, message: this.strConvert('车牌号不能为空'), trigger: "blur" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getUser()
  },
  methods: {
    strConvert,
    parseTime,
    isValidString,
    getUser(){
      this.loading = false
      getInfo().then(res => {
        this.user = res.user;
        if(res.roles.indexOf("common")===-1){
          listUser({ pageNum:1,pageSize:20,deptId:105}).then(response => {
            this.userList = response.rows
          })
        }
      })


    },
    /** 查询工单记录列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0]
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1]
      }
      this.queryParams.businessType="1"
      listWorkorder(this.queryParams).then(response => {
        this.workorderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 表单重置
    reset() {
      this.form = {
        serviceId: null,
        title: null,
        businessType: "1",
        operatorType: null,
        operProgress: 0,
        customerId: null,
        customerNickName: null,
        status: null,
        carPlateNumber:null,
        phonenumber:null,
        expectedTime:null,
        comment:null,
        images:null,
        operTime: null,
        costTime: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null,
        fastRemark:null
      }
      this.form.title= new Date()+this.strConvert('接收车辆');
      this.tWorkOrderDetailList = []
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = []
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    // 调整后的单选处理方法
    handleSelectionChange(selection) {
      // 如果选择了多行，则只保留最后选择的一行
      if (selection.length > 1) {
        this.$refs.singleTable.clearSelection();
        this.$refs.singleTable.toggleRowSelection(selection[selection.length - 1]);
        // 更新为单选后的结果
        this.ids = [selection[selection.length - 1].serviceId];
        this.single = false;
        this.multiple = false;
      } else {
        // 正常单选情况
        this.ids = selection.length ? [selection[0].serviceId] : [];
        this.single = selection.length !== 1;
        this.multiple = !selection.length;
      }
    },
    /** 派工单编辑按钮操作 */
    handleUpdate() {
      const serviceId = this.ids[0]
      this.$router.push(`/manage/workDetailForm/receive/${serviceId}`);
    },
    /** 指派工单操作 */
    handleReceive(user,row) {
      this.$modal.confirm(this.strConvert('单号为"') + row.serviceId + this.strConvert('"的工单，确认后此订单将由') + user.nickName + this.strConvert('来维护，是否确认？')).then(() => {
        row.businessType="2";
        row.operName=user.userName;
        row.operTime=new Date();
        tWorkOrderAssign(row).then(response => {
          this.$modal.msgSuccess(this.strConvert('指派成功'))
          this.getList()
        })
      }).catch(() => {})
    },
    /** 指派工单操作 */
    handleResultProgress(user,row) {
      this.$modal.confirm(this.strConvert('操作后此类目将由') + user.nickName + this.strConvert('立即完成进度，是否确认')).then(() => {
        row.operName=user.userName;
        row.operTime=new Date();
        row.operProgress="100"
        tWorkResultProgress(row).then(response => {
          if(response.code===200){
            this.$modal.msgSuccess(this.strConvert('更新成功'))
            this.progressOpen = false
            this.getList()
          }
        })
      }).catch(() => {})
    },
    openExpandRow(row,expandedRows){
      const serviceId = row.serviceId;
      this.selectRow=expandedRows.map(item=>{
        return item.serviceId;
      });
      if(this.selectRow.indexOf(serviceId)!==-1){
        this.tableLoading=true;
        getWorkorder(serviceId).then(response => {
          this.form = response.data
          if(response.data.tWorkPartsDetailList!==undefined){
            this.tWorkPartsDetailList = response.data.tWorkPartsDetailList
          }
          if(response.data.tWorkOrderDetailList!==undefined){
            // 排序，确保相同类型连续排列
            const sortedList = response.data.tWorkOrderDetailList.sort((a, b) => {
              return String(a.questionType).localeCompare(String(b.questionType));
            });
            // 响应式赋值
            this.$set(this.tWorkOrderDetailObj, serviceId, sortedList);
          }
          this.tableLoading=false;
        })
      }
    },
    // 生成合并策略
    objectSpanMethod({ row, column, rowIndex }, data) {
      if (column.property === 'questionType') {
        const prevRow = data[rowIndex - 1];
        const currentType = row.questionType;

        if (!prevRow || prevRow.questionType !== currentType) {
          let spanCount = 1;
          for (let i = rowIndex + 1; i < data.length; i++) {
            if (data[i].questionType === currentType) {
              spanCount++;
            } else {
              break;
            }
          }
          return { rowspan: spanCount, colspan: 1 };
        } else {
          return { rowspan: 0, colspan: 0 };
        }
      }
    },
    goPrint(){
      this.serviceId =  this.ids[0]
      this.printOpen = true
    },
    recall(serviceId){
      if(serviceId!==undefined){
        this.$modal.confirm(this.strConvert('操作后工单将退回至《受理登记》中，确认是否继续？')).then(() => {
          getWorkorder(serviceId).then(response => {
            let work = response.data
            work.businessType=0;
            updateWorkorder(work).then(response => {
              if(response.code===200){
                this.$modal.msgSuccess(this.strConvert('回撤成功'))
                this.getList();
              }
            });
          })
        }).catch(() => {})
      }
    }
  }
}
</script>
<style scoped lang="scss">
.tableChild{
  box-shadow: inset 0 2px 4px rgb(6, 152, 29);
  padding: 2px;
  margin: 0 auto;     /* 水平居中 */
  width: fit-content; /* 宽度自适应内容 */
  text-align: center; /* 内部文本居中（可选） */
}
</style>
