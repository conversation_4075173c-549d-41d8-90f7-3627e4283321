<template>
  <div>
    <div :id="containerId" class="print-container">
      <slot></slot>
    </div>

    <div v-if="showButtons" class="print-actions">
      <el-button @click="handlePrint" :loading="printing">直接打印</el-button>
      <el-button @click="generatePDF" :loading="generatingPDF">生成PDF</el-button>
    </div>
  </div>
</template>

<script>
import html2pdf from 'html2pdf.js'
import printJS from 'print-js';
export default {
  name: 'PrintComponent',

  props: {
    beforePrint: {
      type: Function,
      default: null
    },
    afterPrint: {
      type: Function,
      default: null
    },
    containerId: {
      type: String,
      default: 'printContainer'
    },
    showButtons: {
      type: Boolean,
      default: true
    },
    printOptions: {
      type: Object,
      default: () => ({
        popTitle: '文档打印',
        extraCss: '',
        extraHead: ''
      })
    },
    pdfOptions: {
      type: Object,
      default: () => ({
        filename: 'document.pdf',
        margin: 10,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: { scale: 2 },
        jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
      })
    },
    // 新增分页配置
    pageBreak: {
      type: Object,
      default: () => ({
        // 是否启用分页
        enabled: true,
        // 分页元素的选择器
        selector: '.page-break',
        // 分页方式: 'auto'|'always'|'avoid'|'left'|'right'
        type: 'always',
        // 分页前保留多少内容不分开
        keepWithNext: false
      })
    }
  },
  data() {
    return {
      printing: false,
      generatingPDF: false
    }
  },
  methods: {
    /**
     * 获取优化的html2canvas配置参数
     * 根据设备像素比动态调整scale值，提升不同设备的渲染质量
     * @returns {Object} 优化后的html2canvas配置
     */
    getOptimalHtml2CanvasOptions() {
      const devicePixelRatio = window.devicePixelRatio || 1;
      // 限制最大scale为3，避免性能问题
      const optimalScale = Math.min(devicePixelRatio, 3);
      
      return {
        ...this.pdfOptions.html2canvas,
        scale: optimalScale,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        // 高DPI设备额外优化
        ...(devicePixelRatio > 1 && {
          removeContainer: true,
          logging: false
        })
      };
    },

    async handlePrint() {
      this.printing = true;
      try {
        if (this.beforePrint) {
          await this.beforePrint();
        }

        await this.$nextTick();

        printJS({
          printable: this.containerId,
          type: 'html',
          targetStyles: ['*'],
          style: this.getPrintStyles()
        });

        if (this.afterPrint) {
          this.afterPrint();
        }
      } finally {
        this.printing = false;
      }
    },

    // 生成PDF方法
    async generatePDF() {
      this.generatingPDF = true;
      try {
        if (this.beforePrint) {
          await this.beforePrint();
        }

        await this.$nextTick();

        const element = document.getElementById(this.containerId);
        if (!element) {
          throw new Error('打印容器未找到');
        }

        const opt = {
          ...this.pdfOptions,
          // 使用优化的html2canvas配置
          html2canvas: this.getOptimalHtml2CanvasOptions(),
          // 确保html2pdf正确处理分页
          pagebreak: {
            mode: ['css', 'legacy'],
            before: '.page-break, .force-page-break',
            avoid: '.avoid-page-break'
          }
        };

        await html2pdf().set(opt).from(element).save();

        if (this.afterPrint) {
          this.afterPrint();
        }
      } catch (error) {
        console.error('PDF生成失败:', error);
        this.$message?.error('PDF生成失败，请重试');
      } finally {
        this.generatingPDF = false;
      }
    },

    // 获取打印样式
    getPrintStyles() {
      return `
        @page {
          size: auto;
          margin: 5mm;
        }
        body {
          font-family: Arial;
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
        .page-break, .force-page-break {
          page-break-after: always !important;
          break-after: page !important;
          display: block;
          height: 0;
          margin: 0;
          padding: 0;
          border: none;
        }
        .avoid-page-break {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }
        table {
          page-break-inside: auto !important;
        }
        tr {
          page-break-inside: avoid !important;
        }
        thead {
          display: table-header-group !important;
        }
        tfoot {
          display: table-footer-group !important;
        }
        .el-form-item__label, .el-input__inner, .el-textarea__inner {
          color: #000 !important;
        }
      `;
    },

    // 暴露给父组件调用的方法
    print() {
      this.handlePrint()
    },

    exportPDF() {
      this.generatePDF()
    }
  }
}
</script>

<style scoped>
.print-container {
  width: 100%;
}

.print-actions {
  margin-top: 20px;
  text-align: center;
}

/* 打印样式 */
@media print {
  body, body * {
    visibility: visible !important;
  }

  .print-container {
    position: relative !important;
    width: 100% !important;
    height: auto !important;
    overflow: visible !important;
  }

  .el-form-item__label, .el-input__inner, .el-textarea__inner {
    color: #000 !important;
    -webkit-print-color-adjust: exact;
  }

  /* 强制分页样式 */
  .page-break {
    page-break-after: always !important;
    break-after: page !important;
  }

  /* 避免表格被分割 */
  table {
    page-break-inside: auto !important;
  }

  tr {
    page-break-inside: avoid !important;
    page-break-after: auto !important;
  }

  thead {
    display: table-header-group !important;
  }

  tfoot {
    display: table-footer-group !important;
  }

  ::v-deep .el-descriptions-item__label.is-bordered-label {
    background: #3c3232;
  }
}
</style>
