import request from '@/utils/request'

// 查询采购申请列表
export function listPurchase(query) {
  return request({
    url: '/manage/purchase/list',
    method: 'get',
    params: query
  })
}
// 查询采购申请详细
export function getPurchase(purchaseId) {
  return request({
    url: '/manage/purchase/' + purchaseId,
    method: 'get'
  })
}

// 新增采购申请
export function addPurchase(data) {
  return request({
    url: '/manage/purchase',
    method: 'post',
    data: data
  })
}

// 修改采购申请
export function updatePurchase(data) {
  return request({
    url: '/manage/purchase',
    method: 'put',
    data: data
  })
}

// 修改同步生成出帐流水
export function updatePurchaseFlow(data) {
  return request({
    url: '/manage/purchase/updatePurchaseFlow',
    method: 'put',
    data: data
  })
}

// 删除采购申请
export function delPurchase(purchaseId) {
  return request({
    url: '/manage/purchase/' + purchaseId,
    method: 'delete'
  })
}
