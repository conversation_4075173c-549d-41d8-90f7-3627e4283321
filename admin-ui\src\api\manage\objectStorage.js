import request from '@/utils/request'

// 查询物件入库列表
export function listObjectStorage(query) {
  return request({
    url: '/manage/objectStorage/list',
    method: 'get',
    params: query
  })
}

// 查询物件入库详细
export function getObjectStorage(objectStorageId) {
  return request({
    url: '/manage/objectStorage/' + objectStorageId,
    method: 'get'
  })
}

// 新增物件入库
export function addObjectStorage(data) {
  return request({
    url: '/manage/objectStorage',
    method: 'post',
    data: data
  })
}

export function addObjectStorageByPurchaseId(PurchaseId) {
  return request({
    url: '/manage/objectStorage/addObjectStorageByPurchaseId/' + PurchaseId,
    method: 'get'
  })
}

// 修改物件入库
export function updateObjectStorage(data) {
  return request({
    url: '/manage/objectStorage',
    method: 'put',
    data: data
  })
}

// 修改同步采购库存
export function updatePurchaseStore(data) {
  return request({
    url: '/manage/objectStorage/updatePurchaseStore',
    method: 'put',
    data: data
  })
}

// 删除物件入库
export function delObjectStorage(objectStorageId) {
  return request({
    url: '/manage/objectStorage/' + objectStorageId,
    method: 'delete'
  })
}
