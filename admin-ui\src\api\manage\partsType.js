import request from '@/utils/request'

// 查询配件类型列表
export function listPartsType(query) {
  return request({
    url: '/manage/partsType/list',
    method: 'get',
    params: query
  })
}

// 查询配件类型详细
export function getPartsTypeByPartsPosition(partsPosition) {
  return request({
    url: '/manage/partsType/getPartsTypeByPartsPosition/' + partsPosition,
    method: 'get'
  })
}

// 查询配件类型详细
export function getPartsType(partsTypeId) {
  return request({
    url: '/manage/partsType/' + partsTypeId,
    method: 'get'
  })
}

// 新增配件类型
export function addPartsType(data) {
  return request({
    url: '/manage/partsType',
    method: 'post',
    data: data
  })
}

// 修改配件类型
export function updatePartsType(data) {
  return request({
    url: '/manage/partsType',
    method: 'put',
    data: data
  })
}

// 删除配件类型
export function delPartsType(partsTypeId) {
  return request({
    url: '/manage/partsType/' + partsTypeId,
    method: 'delete'
  })
}
