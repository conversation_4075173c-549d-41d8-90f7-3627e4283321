<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="98px">
      <el-form-item :label="strConvert('条目类型')" prop="entryType">
        <el-select v-model="queryParams.entryType"  :placeholder="strConvert('请选择条目类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_entry_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="queryParams.entryType==='1'"  :label="strConvert('定损服务类型')" prop="questionType">
        <el-select v-model="queryParams.questionType" :placeholder="strConvert('请选择问题类型')" clearable >
          <el-option
            v-for="dict in dict.type.t_work_question_service_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="queryParams.entryType==='2'" :label="strConvert('维修服务类型')" prop="questionType">
        <el-select v-model="queryParams.questionType" :placeholder="strConvert('请选择问题类型')" clearable>
          <el-option
            v-for="dict in dict.type.t_work_order_detail_service_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="strConvert('问题名称')" prop="questionName">
        <el-input
          v-model="queryParams.questionName"
          :placeholder="strConvert('请输入问题名称')"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">{{ strConvert('搜索') }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{ strConvert('重置') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:question:add']"
        >{{ strConvert('新增') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:question:edit']"
        >{{ strConvert('修改') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:question:remove']"
        >{{ strConvert('删除') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:question:export']"
        >{{ strConvert('导出') }}</el-button>
      </el-col>
      <el-col :span="10"><span style="color:red;">{{ strConvert('注意：分成比不修改的情况下，默认为10%') }}</span></el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="questionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="strConvert('问题编号')" align="center" prop="questionId" />
      <el-table-column :label="strConvert('条目类型')" align="center" prop="entryType" width="80">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.t_entry_type" :value="scope.row.entryType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('定损服务类型')" align="center" prop="questionType" width="280">
        <template slot-scope="scope">
          <dict-tag v-if="scope.row.entryType==='1'" :options="dict.type.t_work_question_service_type" :value="scope.row.questionType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('维修服务类型')" align="center" prop="questionType" width="280">
        <template slot-scope="scope">
          <dict-tag v-if="scope.row.entryType==='2'" :options="dict.type.t_work_order_detail_service_type" :value="scope.row.questionType"/>
        </template>
      </el-table-column>
      <el-table-column :label="strConvert('定损/维修名称')" align="center" prop="questionName" />
      <el-table-column :label="strConvert('描述')" align="center" prop="questionComment" />
      <el-table-column :label="strConvert('费用')" align="center" prop="amount" />
      <el-table-column :label="strConvert('分成比%')" align="center" prop="shareRatio" >
        <template slot-scope="scope">
          <span style="color: #e60000">{{ scope.row.shareRatio }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column :label="strConvert('货币类型(usd,cel)')" align="center" prop="currencyType" />-->
<!--      <el-table-column :label="strConvert('创建时间')" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>-->
<!--      <el-table-column :label="strConvert('备注')" align="center" prop="remark" />-->
      <el-table-column :label="strConvert('操作')" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:question:edit']"
          >{{ strConvert('修改') }}</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:question:remove']"
          >{{ strConvert('删除') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改定损问题信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item :label="strConvert('条目类型')" prop="entryType">
          <el-select v-model="form.entryType" :placeholder="strConvert('请选择条目类型')">
            <el-option
              v-for="dict in dict.type.t_entry_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="strConvert('服务类型')" prop="questionType">
          <el-select v-if="form.entryType==='1'" v-model="form.questionType" style="width: 300px" :placeholder="strConvert('请选择服务类型')">
            <el-option
              v-for="dict in dict.type.t_work_question_service_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
          <el-select v-else-if="form.entryType==='2'" v-model="form.questionType" style="width: 300px" :placeholder="strConvert('请选择服务类型')">
            <el-option
              v-for="dict in dict.type.t_work_order_detail_service_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="strConvert('问题名称')" prop="questionName">
          <el-input v-model="form.questionName" :placeholder="strConvert('请输入问题名称')" />
        </el-form-item>
        <el-form-item :label="strConvert('问题描述')" prop="questionComment">
          <el-input v-model="form.questionComment" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
        <el-form-item :label="strConvert('分成比%')" prop="shareRatio">
          <el-input v-model="form.shareRatio" type="number"  :placeholder="strConvert('请输入分成比%')" />
        </el-form-item>
        <el-form-item :label="strConvert('费用')" prop="amount">
          <el-input v-model="form.amount" :placeholder="strConvert('请输入费用')" />
        </el-form-item>
        <el-form-item :label="strConvert('备注')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="strConvert('请输入内容')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">{{ strConvert('确 定') }}</el-button>
        <el-button @click="cancel">{{ strConvert('取 消') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { strConvert } from '@/i18n/i18nMix'

import { listQuestion, getQuestion, delQuestion, addQuestion, updateQuestion } from "@/api/system/question"

export default {
  name: "Question",
  dicts: ['t_work_question_service_type','t_entry_type','t_work_order_detail_service_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 定损问题信息表格数据
      questionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entryType: null,
        questionType: null,
        questionName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        currencyType: [
          { required: true, message: "货币类型(usd,cel)不能为空", trigger: "change" }
        ],
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    strConvert,

    /** 查询定损问题信息列表 */
    getList() {
      this.loading = true
      listQuestion(this.queryParams).then(response => {
        this.questionList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        questionId: null,
        questionType: null,
        entryType: "1",
        questionName: null,
        questionComment: null,
        amount: null,
        shareRatio: 10,
        currencyType: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.questionId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加定损问题信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const questionId = row.questionId || this.ids
      getQuestion(questionId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改定损问题信息"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.questionId != null) {
            updateQuestion(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addQuestion(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const questionIds = row.questionId || this.ids
      this.$modal.confirm('是否确认删除定损问题信息编号为"' + questionIds + '"的数据项？').then(function() {
        return delQuestion(questionIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/question/export', {
        ...this.queryParams
      }, `question_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
