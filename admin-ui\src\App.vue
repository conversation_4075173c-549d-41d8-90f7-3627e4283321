<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker"

export default {
  name: "App",
  components: { ThemePicker },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  },
  mounted() {
    // 确保在应用启动时设置正确的语言样式类
    this.updateBodyLanguageClass(this.currentLanguage)
  }
}
</script>
<style>
/* 覆盖Element UI输入框的边框颜色 */
.el-input__inner {
  border-color: #6c6f79 !important; /* 使用Element UI的主色并加深 */
 /* background-color: #eaf3fa;*/
}

/* 输入框获得焦点时的边框颜色 */
.el-input__inner:focus {
  border-color: #6c6f79 !important; /* 更深的蓝色 */
  box-shadow: 0 0 0 2px rgba(0, 119, 230, 0.2) !important;
}

/* 为所有输入框添加悬停效果 */
.el-input__inner:hover {
  border-color: #6c6f79 !important;
}

/* 容器样式 */
.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

/* 标题样式 */
.title {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
}

/* 表单样式 */
.demo-form {
  padding: 20px;
  border: 1px solid #6c6f79;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.form-section {
  margin-bottom: 20px;
}

.el-table {
  border: 1px solid #6c6f79;
}

.el-table--border .el-table__cell {
  border-right: 1px solid #6c6f79;
}

.el-table th.el-table__cell.is-leaf, .el-table td.el-table__cell{
  border-bottom: 1px solid #6c6f79;
}

.el-table--group, .el-table--border {
  border: 1px solid #6c6f79;
}

.el-input__inner::placeholder {
  color: #6c6f79;
  opacity: 0.8;
}
.el-textarea__inner::placeholder {
  color: #6c6f79;
  opacity: 0.8;
}

</style>
<style scoped>
#app .theme-picker {
  display: none;
}

</style>
