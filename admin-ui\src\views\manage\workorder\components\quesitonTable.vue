<script >
import { strConvert } from '@/i18n/i18nMix'
export default {
  name: 'questionTable',
  props: {
    saveOrderDetailList: {
      type: Array,
      default: []
    },
    tableName: {
      type: String,
      default:""
    },
    isDelete: {
      type: Boolean,
      default:true
    },
  },
  data() {
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      isMobile: false,
    }
  },
  created() {
  },
  watch: {
  },
  computed: {

  },
  methods: {
    strConvert,
    deleteRow(index, rows,questionName) {
      rows.splice(index, 1);
      this.$emit("resetCountData",questionName)
    },
    /** 工单详情序号 */
    rowTWorkOrderDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1
    },
    /** 复选框选中数据 */
    handleTWorkOrderDetailSelectionChange(selection) {
      this.checkedTWorkOrderDetail = selection.map(item => item.index)
    },
  }
}
</script>

<template>
  <div class="tableChild">
    <el-table v-if="saveOrderDetailList.length>0" :data="saveOrderDetailList"
              :row-class-name="rowTWorkOrderDetailIndex"
              @selection-change="handleTWorkOrderDetailSelectionChange"
              :ref="'detail'+tableName"
              :max-height="isMobile ? '300px' : '400px'"
              border

              size="mini"
              style="width: 100%" >
      <el-table-column :label="strConvert('编号')" align="center" prop="index" width="50" />
      <el-table-column :label="strConvert('问题名称')" prop="questionName" width="180" />
      <el-table-column :label="strConvert('费用')" prop="amount" width="150" />
      <el-table-column :label="strConvert('描述')" prop="questionComment"  width="120"/>
      <el-table-column width="80" v-if="isDelete" :label="strConvert('操作')" align="center"  class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <div class="mobile-action-buttons">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click.native.prevent="deleteRow(scope.$index, saveOrderDetailList,scope.row.questionName)"
              v-hasPermi="['manage:workorder:edit']"
            >{{strConvert('删除')}}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style scoped lang="scss">
.tableChild{
  margin-right: 15px;
}
</style>
