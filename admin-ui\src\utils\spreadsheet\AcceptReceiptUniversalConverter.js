/**
 * 验收单据通用转换器
 * 继承UniversalDataConverter，直接生成Univer格式数据
 * 基于真实业务需求的验收单据模板
 * @version 2.0.0 - 迁移自acceptReceiptConverter.js
 */
import { UniversalDataConverter } from './UniversalDataConverter'
import { SIGNATURE_TEXTS } from '@/constants/signatureConstants'

// 默认对齐配置：车主签字左对齐，接单人签字左对齐，电话号码右对齐
const defaultAlignment = {
  ownerAlign: 0,  // 0=左对齐, 1=居中, 2=右对齐
  staffAlign: 1,  // 0=左对齐, 1=居中, 2=右对齐
  phoneAlign: 2   // 0=左对齐, 1=居中, 2=右对齐
}

/**
 * 边框管理器类
 * 用于管理和应用各种边框配置
 */
class BorderManager {
  constructor() {
    this.borderRegions = [];
  }

  /**
   * 注册边框区域
   * @param {string} regionType 区域类型
   * @param {number} startRow 起始行
   * @param {number} endRow 结束行
   * @param {number} startCol 起始列
   * @param {number} endCol 结束列
   * @param {object} borderConfig 边框配置
   */
  registerBorderRegion(regionType, startRow, endRow, startCol, endCol, borderConfig = {}) {
    this.borderRegions.push({
      type: regionType,
      startRow,
      endRow,
      startCol,
      endCol,
      config: {
        borderStyle: borderConfig.borderStyle || { color: '#000000', width: 1, style: 'solid' },
        borderTypes: borderConfig.borderTypes || ['all'],
        priority: borderConfig.priority || 0 // 优先级，数字越大优先级越高
      }
    });

    console.log(`🔍 [DEBUG] 注册边框区域: ${regionType}, 行${startRow}-${endRow}, 列${startCol}-${endCol}`);
  }

  /**
   * 应用所有注册的边框
   * @param {Array} cellData 单元格数据数组
   * @param {object} converter 转换器实例
   */
  applyAllBorders(cellData, converter) {
    // 按优先级排序，优先级高的后应用（覆盖优先级低的）
    const sortedRegions = this.borderRegions.sort((a, b) => a.config.priority - b.config.priority);

    sortedRegions.forEach(region => {
      converter.addDynamicBorders(cellData, {
        startRow: region.startRow,
        endRow: region.endRow,
        startCol: region.startCol,
        endCol: region.endCol,
        borderStyle: region.config.borderStyle,
        borderTypes: region.config.borderTypes,
        regionType: region.type
      });
    });

    console.log(`✅ [SUCCESS] 边框管理器应用了 ${this.borderRegions.length} 个边框区域`);
  }

  /**
   * 清除所有注册的边框区域
   */
  clearAllRegions() {
    this.borderRegions = [];
    console.log(`🔍 [DEBUG] 清除所有边框区域注册`);
  }

  /**
   * 获取注册的边框区域数量
   * @returns {number} 区域数量
   */
  getRegionCount() {
    return this.borderRegions.length;
  }
}

export class AcceptReceiptUniversalConverter extends UniversalDataConverter {
  constructor(config = {}) {
    super(config)
    this.receiptTitle = '汽车定损收车明细单'
    this.georgianTitle = 'მანქანის დაზიანების შეფასება და ქვითრების სია'
    this.companyName = 'EURO MOTORS'

    // 为每个实例生成唯一标识符，避免缓存冲突
    this.instanceId = `converter_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
    // 转换器实例已创建

    // 初始化边框管理器
    this.borderManager = new BorderManager()

    // 缓存机制已禁用 - 每次都重新生成所有区域
    // this.staticSectionsCache = null // 静态区域缓存（标题、签字、公司信息）
    // this.semiStaticSectionsCache = null // 半静态区域缓存（基本信息、汇总）
    // this.lastFormDataHash = null // 上次formData的哈希值

    // 强制清除所有静态缓存，确保使用最新代码
    AcceptReceiptUniversalConverter.clearAllStaticCaches()

    // 清除当前实例的所有缓存
    this.clearCache()
  }

  /**
   * 清除所有静态缓存（类级别方法）
   */
  static clearAllStaticCaches() {
    // 这里可以添加类级别的缓存清除逻辑
    // 清除所有静态缓存
  }

  /**
   * 重写createTitleCell方法以支持完整样式对象
   * @param {number} row 行号
   * @param {number} col 列号
   * @param {string} value 值
   * @param {object|number} styleOrFontSize 样式对象或字体大小（向后兼容）
   * @returns {object} 标题单元格数据
   */
  createTitleCell(row, col, value, styleOrFontSize = 16) {
    let style
    if (typeof styleOrFontSize === 'object') {
      // 新格式：传入完整样式对象
      style = { ...this.styles.title, ...styleOrFontSize }
    } else {
      // 旧格式：只传入字体大小（向后兼容）
      style = { ...this.styles.title, fs: styleOrFontSize }
    }

    // 创建标题单元格
    const cell = this.createCell(row, col, value, style)

    return cell
  }

  /**
   * 添加动态合并配置
   * @param {string} key 合并键
   * @param {object} config 合并配置
   */
  addDynamicMerge(key, config) {
    if (!this.dynamicMerges) {
      this.dynamicMerges = []
    }

    // 添加动态合并配置
    this.dynamicMerges.push({ key, config })
  }

  /**
   * 生成数据哈希值用于缓存判断
   * @param {object} data 数据对象
   * @returns {string} 哈希值
   */
  generateDataHash(data) {
    return JSON.stringify(data).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0).toString()
  }

  /**
   * 清除所有缓存（已禁用缓存机制）
   */
  clearCache() {
    // 禁用所有缓存机制
    // this.staticSectionsCache = null
    // this.semiStaticSectionsCache = null
    // this.lastFormDataHash = null
    // 清除动态合并配置，防止旧配置残留
    this.dynamicMerges = []

    // 清除边框管理器的所有注册区域
    if (this.borderManager) {
      this.borderManager.clearAllRegions()
    }
  }

  /**
   * 清除半静态区域缓存（已禁用缓存机制）
   */
  clearSemiStaticCache() {
    // 禁用半静态缓存机制
    // this.semiStaticSectionsCache = null
    // this.lastFormDataHash = null
    // 清除动态合并配置，防止旧配置残留
    this.dynamicMerges = []
    // 半静态区域缓存已清除
  }

  /**
   * 生成静态区域数据（标题、签字、公司信息）
   * @returns {object} 包含cellData和rowCount的对象
   */
  generateStaticSections() {
    // 禁用缓存机制，每次都重新生成
    // if (this.staticSectionsCache) {
    //   return this.staticSectionsCache
    // }

    const cellData = []
    let currentRow = 0

    // 创建标题区域
    currentRow = this.createHeaderSection(currentRow, cellData)
    const headerEndRow = currentRow

    // 预留基本信息和服务表格区域的行数（这些在其他方法中计算）
    // 这里只记录静态区域的结束位置

    const result = {
      cellData: cellData,
      headerEndRow: headerEndRow,
      staticRowCount: currentRow
    }

    // 禁用缓存机制，不保存到缓存
    // this.staticSectionsCache = result
    return result
  }

  /**
   * 生成半静态区域数据（仅基本信息区域）
   * @param {object} formData 表单数据
   * @param {number} startRow 起始行
   * @param {number} serviceTableRowCount 服务表格行数
   * @returns {object} 包含cellData和rowCount的对象
   */
  generateSemiStaticSections(formData, startRow, serviceTableRowCount) {
    const formDataHash = this.generateDataHash(formData)
    if (this.semiStaticSectionsCache && this.lastFormDataHash === formDataHash) {
      return this.semiStaticSectionsCache
    }

    const cellData = []
    let currentRow = startRow

    // 创建基本信息区域
    currentRow = this.createBasicInfoSection(currentRow, formData, cellData)
    const basicInfoEndRow = currentRow

    // 跳过服务表格区域（动态生成）
    currentRow += serviceTableRowCount

    const result = {
      cellData: cellData,
      basicInfoEndRow: basicInfoEndRow,
      totalRowCount: currentRow - startRow
    }

    this.semiStaticSectionsCache = result
    this.lastFormDataHash = formDataHash
    return result
  }

  /**
   * 生成动态区域数据（服务项目表格）
   * @param {number} startRow 起始行
   * @param {Array} orderDetailList 订单详情列表
   * @param {Array} serviceTypeDict 服务类型字典
   * @returns {object} 包含cellData和rowCount的对象
   */
  generateDynamicSections(startRow, orderDetailList, serviceTypeDict) {
    const cellData = []
    let currentRow = startRow

    // 创建服务项目表格区域


    return {
      cellData: cellData,
      rowCount: endRow - startRow
    }
  }

  /**
   * 转换为Univer格式数据
   * @param {object} formData 表单数据
   * @param {Array} orderDetailList 订单详情列表
   * @param {object|Array} options 选项对象或服务类型字典数组（向后兼容）
   * @returns {object} Univer格式数据
   */
  convertToUniverData(formData, orderDetailList = [], options = {}) {
    // 清除缓存，确保数据最新
    this.clearCache();

    // 清空动态合并配置，避免累积造成冲突
    this.dynamicMerges = []

    // 处理参数兼容性：支持直接传数组或对象包装
    let serviceTypeDict = []
    if (Array.isArray(options)) {
      // 向后兼容：直接传递数组
      serviceTypeDict = options
    } else if (options && options.serviceTypeDict) {
      // 新格式：从选项对象中提取
      serviceTypeDict = options.serviceTypeDict
    }

    // 确保 serviceTypeDict 是数组
    if (!Array.isArray(serviceTypeDict)) {
      console.warn('⚠️ [WARN] serviceTypeDict 不是数组，使用空数组替代:', serviceTypeDict)
      serviceTypeDict = []
    }

    // 输入数据验证

    try {
      // 清空动态合并配置，防止重复添加
      this.dynamicMerges = []

      const allCellData = []
      let currentRow = 0

      // 1. 生成静态区域（标题）- 禁用缓存，每次重新生成
      const staticSections = this.generateStaticSections()
      allCellData.push(...staticSections.cellData)
      currentRow = staticSections.staticRowCount

      // 2. 生成基本信息区域（半静态）
      currentRow = this.createBasicInfoSection(currentRow, formData, allCellData)
      
      // 边框将在后续统一添加

      // 4. 生成动态区域（服务表格）- 强制重新生成以确保数据实时性
      const serviceTableResult = this.createServiceTableSection(currentRow, orderDetailList, serviceTypeDict, allCellData)
      allCellData.push(...serviceTableResult.cells)
      currentRow += serviceTableResult.rowCount

      // 记录动态表格结束行号，用于边框添加
      const dynamicTableEndRow = currentRow - 1

      // 5. 根据动态表格实际占用行数，计算后续区域起始位置
      // currentRow已经包含了动态表格的行数，直接使用即可
      const summaryStartRow = currentRow // 紧接着动态表格，不留空隙



      // 数据转换完成

      // 确保有足够的行数，至少比实际使用的行数多10行
      const minRows = Math.max(currentRow + 10, 30)  // 最少30行

      // 生成Univer数据结构
      const sheetConfig = this.getSheetConfig(minRows)
      const univerData = {
        sheets: {
          'sheet-01': {
            ...sheetConfig,
            cellData: allCellData
          }
        }
      }

      // Univer数据结构生成完成

      // 6. 创建汇总区域
      const summaryEndRow = this.createSummarySection(summaryStartRow, formData, allCellData)

      // 7. 创建签字区域
      const signatureEndRow = this.createSignatureSection(summaryEndRow, allCellData)

      // 8. 创建公司信息区域
      currentRow = this.createCompanyInfoSection(signatureEndRow, allCellData)



      // 添加边框（支持多种边框添加方式）
      const borderOptions = options.borderOptions || {};
      const useBorderManager = borderOptions.useBorderManager || false;

      if (useBorderManager) {
        // 使用边框管理器添加边框
        this.applyBordersWithManager(allCellData, dynamicTableEndRow);
      } else {
        // 使用传统方式添加边框
        this.addBordersToData(allCellData, dynamicTableEndRow, borderOptions);
      }

      return univerData
    } catch (error) {
      console.error('❌ [ERROR] 数据转换失败:', error);
      // 异常情况下重置状态，避免阻塞后续调用
      this._isConverting = false;
      throw new Error(`验收单据转换失败: ${error.message}`);
    }
  }

  /**
   * 转换为表格数据（兼容BaseConverter）
   * @param {object} formData 表单数据
   * @param {array} detailList 详情列表
   * @param {object} options 额外选项
   * @returns {array} Univer格式的表格数据
   */
  convertToSpreadsheetData(formData, detailList = [], options = {}) {
    try {
      // 调用convertToUniverData方法获取完整的Univer数据
      const univerData = this.convertToUniverData(formData, detailList, options)

      // 返回第一个工作表的cellData
      const firstSheet = Object.values(univerData.sheets)[0]
      return firstSheet ? firstSheet.cellData : []
    } catch (error) {
      console.error('❌ [ERROR] convertToSpreadsheetData失败:', error)
      throw error
    }
  }

  /**
   * 创建标题区域
   * @param {number} startRow 起始行
   * @param {Array} cellData 单元格数据数组
   * @returns {number} 下一行行号
   */
  createHeaderSection(startRow, cellData, alignmentConfig = {}) {
    // 生成表头区域

    const alignment = { ...defaultAlignment, ...alignmentConfig }

    let currentRow = startRow

    //第0行：图片区域
    // 创建图片单元格（预留位置）
    cellData.push(this.createTitleCell(currentRow, 0, '', {
      horizontalAlign: 'center',
      verticalAlign: 'middle'
    }))
    // 图片区域的跨行合并配置已在静态配置中定义，无需重复添加
    currentRow++

    // 第1行：主标题 - 使用基类的标题样式
    // 创建主标题
    cellData.push(this.createTitleCell(currentRow, 0, this.receiptTitle, {
      horizontalAlign: 'center',
      verticalAlign: 'middle',
      fs: 11
    }))
    currentRow++

    // 第2行：副标题 - 使用基类的标题样式
    // 创建副标题
    cellData.push(this.createTitleCell(currentRow, 0, this.georgianTitle, {
      horizontalAlign: alignment.phoneAlign === 0 ? 'left' : alignment.phoneAlign === 1 ? 'center' : 'right',
      verticalAlign: 'middle',
      fs: 11
    }))

    // 副标题的跨行合并配置已在静态配置中定义，无需重复添加
    currentRow++
    // 表头区域生成完成

    return currentRow
  }

  /**
   * 创建基本信息区域
   * @param {number} startRow 起始行
   * @param {object} formData 表单数据
   * @param {Array} cellData 单元格数据数组
   * @returns {number} 下一行行号
   */
  createBasicInfoSection(startRow, formData, cellData) {
    let currentRow = startRow
    // 使用基类预定义样式，确保样式一致性
    const labelStyle = { ...this.styles.header, fs: 10, horizontalAlign: 'center', verticalAlign: 'middle' }
    const valueStyle = { ...this.styles.default, fs: 10, horizontalAlign: 'center', verticalAlign: 'middle' }

    // 第一行：时间和工单编号 - 标签和值都跨两列显示
    cellData.push(this.createCell(currentRow, 0, 'მიღების დრო时间:', labelStyle))
    cellData.push(this.createCell(currentRow, 2, formData.title || '', valueStyle))
    cellData.push(this.createCell(currentRow, 4, '№/工单编号:', labelStyle))
    cellData.push(this.createCell(currentRow, 6, formData.serviceId || '', valueStyle))
    currentRow++

    // 第二行：车主和品牌 - 标签和值都跨两列显示
    cellData.push(this.createCell(currentRow, 0, 'მფლობელი车主:', labelStyle))
    cellData.push(this.createCell(currentRow, 2, formData.customerNickName || '', valueStyle))
    cellData.push(this.createCell(currentRow, 4, 'ა/მ მარკა品牌:', labelStyle))
    cellData.push(this.createCell(currentRow, 6, formData.brand || '', valueStyle))
    currentRow++

    // 第三行：电话（单列），颜色（单列），车牌号（跨两列）
    cellData.push(this.createCell(currentRow, 0, 'ტელეფონი电话:', labelStyle))  // A列
    cellData.push(this.createCell(currentRow, 1, formData.phonenumber || '', valueStyle))  // B列
    cellData.push(this.createCell(currentRow, 2, 'ფერი/ 颜色:', labelStyle))  // C列
    cellData.push(this.createCell(currentRow, 3, formData.color || '', valueStyle))  // D列
    cellData.push(this.createCell(currentRow, 4, 'სახ. №车牌号:', labelStyle))  // E列
    cellData.push(this.createCell(currentRow, 6, formData.carPlateNumber || '', valueStyle))  // G列
    // 添加车牌号标签的合并配置（E列F列）
    this.addDynamicMerge(`${currentRow}_4_${currentRow}_5`, { r: currentRow, c: 4, rs: 1, cs: 2 })
    currentRow++

    // 第四行：进厂日期和取车日期 - 标签和值都跨两列显示
    const dateRow = currentRow
    cellData.push(this.createCell(dateRow, 0, 'მიღ.თარ/进厂日期:', labelStyle))
    cellData.push(this.createCell(dateRow, 2, this.formatDate(formData.createTime), valueStyle))
    cellData.push(this.createCell(dateRow, 4, 'შესრ.თარ/取车日期:', labelStyle))
    cellData.push(this.createCell(dateRow, 6, this.formatDate(formData.expectedTime), valueStyle))

    // 动态添加进厂日期和取车日期的合并配置
    this.addDynamicMerge(`${dateRow}_0_${dateRow}_1`, { r: dateRow, c: 0, rs: 1, cs: 2 })  // 进厂日期标签跨两列
    this.addDynamicMerge(`${dateRow}_2_${dateRow}_3`, { r: dateRow, c: 2, rs: 1, cs: 2 })  // 进厂日期值跨两列
    this.addDynamicMerge(`${dateRow}_4_${dateRow}_5`, { r: dateRow, c: 4, rs: 1, cs: 2 })  // 取车日期标签跨两列
    this.addDynamicMerge(`${dateRow}_6_${dateRow}_7`, { r: dateRow, c: 6, rs: 1, cs: 2 })  // 取车日期值跨两列

    currentRow++

    return currentRow
  }

  /**
   * 格式化日期
   * @param {string|Date} date 日期
   * @returns {string} 格式化后的日期字符串
   */
  formatDate(date) {
    if (!date) return ''
    const d = new Date(date)
    if (isNaN(d.getTime())) return ''
    return d.toLocaleDateString('zh-CN')
  }

  /**
   * 格式化西式日期
   * @param {Date} date 日期对象
   * @returns {string} 格式化后的西式日期字符串
   */
  formatWesternDate(date) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    const month = months[date.getMonth()]
    const day = date.getDate()
    const year = date.getFullYear()
    return `${month} ${day}, ${year}`
  }

  /**
   * 创建服务项目表格区域
   * @param {number} startRow 起始行
   * @param {Array} orderDetailList 订单详情列表
   * @param {Array} serviceTypeDict 服务类型字典
   * @param {Array} cellData 单元格数据数组
   * @returns {number} 下一行行号
   */
  createServiceTableSection(startRow, orderDetailList, serviceTypeDict, cellData) {
    console.log(`🔍 [DEBUG] createServiceTableSection - 开始创建服务表格区域`);
    console.log(`🔍 [DEBUG] createServiceTableSection - 起始行: ${startRow}`);
    console.log(`🔍 [DEBUG] createServiceTableSection - 原始订单详情数量: ${orderDetailList.length}`);
    console.log(`🔍 [DEBUG] createServiceTableSection - 服务类型字典:`, serviceTypeDict);

    let currentRow = startRow
    const tableCells = [] // 定义 tableCells 变量来收集所有单元格数据

    // 去重处理
    const uniqueItems = this.removeDuplicateItems(orderDetailList)
    console.log(`🔍 [DEBUG] createServiceTableSection - 去重后订单详情数量: ${uniqueItems.length}`);
    // 项目去重完成

    // 按服务类型分组
    const groupedData =  this.groupOrderDetailsByType(uniqueItems, serviceTypeDict)
    console.log(`🔍 [DEBUG] createServiceTableSection - 分组结果:`, groupedData);
    console.log(`🔍 [DEBUG] createServiceTableSection - 分组数量: ${groupedData.length}`);

    // 为每个服务类型创建表格，不添加额外空行
    groupedData.forEach((group, index) => {
      console.log(`🔍 [DEBUG] createServiceTableSection - 处理第${index + 1}个分组: "${group.label}", 当前行: ${currentRow}`);
      console.log(`🔍 [DEBUG] createServiceTableSection - 分组项目数量: ${group.items.length}`);

      if (group.items.length > 0) {
        const groupResult = this.createServiceGroupTable(currentRow, group)
        tableCells.push(...groupResult.cells)
        currentRow = groupResult.nextRow

        console.log(`🔍 [DEBUG] createServiceTableSection - 第${index + 1}个分组处理完成，下一行: ${currentRow}`);
        console.log(`🔍 [DEBUG] createServiceTableSection - 该分组生成单元格数量: ${groupResult.cells.length}`);
      }
    })

    const result = {
      cells: tableCells,
      rowCount: currentRow - startRow
    };

    console.log(`🔍 [DEBUG] createServiceTableSection - 服务表格区域创建完成`);
    console.log(`🔍 [DEBUG] createServiceTableSection - 总行数: ${result.rowCount}`);
    console.log(`🔍 [DEBUG] createServiceTableSection - 总单元格数量: ${tableCells.length}`);

    return result;
  }

  /**
   * 创建服务分组表格
   * @param {number} startRow 起始行
   * @param {object} group 分组数据
   * @returns {object} 包含cells和nextRow的对象
   */
  createServiceGroupTable(startRow, group) {
    console.log(`🔍 [DEBUG] createServiceGroupTable - 开始创建服务组表格`);
    console.log(`🔍 [DEBUG] createServiceGroupTable - 起始行: ${startRow}`);
    console.log(`🔍 [DEBUG] createServiceGroupTable - 组信息:`, group);

    const groupCells = []
    let currentRow = startRow

    // 添加分组标题
    console.log(`🔍 [DEBUG] createServiceGroupTable - 步骤1: 添加分组标题，当前行: ${currentRow}`);
    currentRow = this.addGroupTitle(groupCells, currentRow, group.label)
    console.log(`🔍 [DEBUG] createServiceGroupTable - 分组标题添加完成，下一行: ${currentRow}`);

    // 添加表头
    console.log(`🔍 [DEBUG] createServiceGroupTable - 步骤2: 添加表头，当前行: ${currentRow}`);
    currentRow = this.addTableHeaders(groupCells, currentRow)
    console.log(`🔍 [DEBUG] createServiceGroupTable - 表头添加完成，下一行: ${currentRow}`);

    // 添加数据行
    console.log(`🔍 [DEBUG] createServiceGroupTable - 步骤3: 添加数据行，当前行: ${currentRow}`);
    currentRow = this.addDataRows(groupCells, currentRow, group.items)
    console.log(`🔍 [DEBUG] createServiceGroupTable - 数据行添加完成，下一行: ${currentRow}`);

    // 添加小计行
    console.log(`🔍 [DEBUG] createServiceGroupTable - 步骤4: 添加小计行，当前行: ${currentRow}`);
    currentRow = this.addSubtotalRow(groupCells, currentRow, group)
    console.log(`🔍 [DEBUG] createServiceGroupTable - 小计行添加完成，下一行: ${currentRow}`);

    const result = {
      cells: groupCells,
      nextRow: currentRow
    };

    console.log(`🔍 [DEBUG] createServiceGroupTable - 表格创建完成`);
    console.log(`🔍 [DEBUG] createServiceGroupTable - 总行数: ${result.nextRow - startRow}`);
    console.log(`🔍 [DEBUG] createServiceGroupTable - 生成的单元格数量: ${groupCells.length}`);

    return result;
  }

  /**
   * 添加分组标题
   * @param {Array} cellData 单元格数据数组
   * @param {number} currentRow 当前行
   * @param {string} label 标题文本
   * @returns {number} 下一行行号
   */
  addGroupTitle(cellData, currentRow, label) {
    // 使用基类的表头样式作为分组标题样式
    const groupTitleStyle = { ...this.styles.tableHeader, fs: 9, bl: 0 }
    // 分组标题样式
    cellData.push(this.createCell(currentRow, 0, label, groupTitleStyle))

    // 修复：分组标题应该跨整行（8列：0-7）与表格列数一致
    this.addDynamicMerge(`group_title_${label}_${currentRow}_0_7`, { r: currentRow, c: 0, rs: 1, cs: 8 })

    return currentRow + 1
  }

  /**
   * 添加表头行
   * @param {Array} cellData 单元格数据数组
   * @param {number} currentRow 当前行
   * @returns {number} 下一行行号
   */
  addTableHeaders(cellData, currentRow) {
    // 使用基类的表头样式，确保样式一致性
    const headerStyle = { ...this.styles.tableHeader, fs: 9 }
    console.log('🔍 [DEBUG] addTableHeaders - 表头样式:', headerStyle);

    // 修复表头布局：8列布局，指定字段占用两列
    const headers = [
      { col: 0, text: 'NO' },
      { col: 1, text: '服务', span: 2 },  // 占用两列（1-2）
      { col: 3, text: 'სამღებრო სამუშაოები', span: 2 },  // 占用两列（3-4）
      { col: 5, text: 'სამ.ღირებულება费用' },
      { col: 6, text: 'შენიშვნა备注', span: 2 }  // 占用两列（6-7）
    ];

    headers.forEach(header => {
      const cell = this.createCell(currentRow, header.col, header.text, headerStyle);
      cellData.push(cell);
      console.log(`🔍 [DEBUG] addTableHeaders - 添加表头单元格: 行${currentRow}, 列${header.col}, 值"${header.text}", 单元格:`, cell);
      
      // 如果字段需要跨列，添加合并配置
      if (header.span && header.span > 1) {
        this.addDynamicMerge(`header_${header.col}_${currentRow}`, { 
          r: currentRow, 
          c: header.col, 
          rs: 1, 
          cs: header.span 
        });
      }
    });

    return currentRow + 1
  }

  /**
   * 添加数据行
   * @param {Array} cellData 单元格数据数组
   * @param {number} currentRow 当前行
   * @param {Array} items 数据项目
   * @returns {number} 下一行行号
   */
  addDataRows(cellData, currentRow, items) {
    console.log(`🔍 [DEBUG] addDataRows - 开始添加数据行，起始行: ${currentRow}, 数据项目数量: ${items.length}`);
    console.log('🔍 [DEBUG] addDataRows - 数据项目:', items);

    // 修复：使用基类的样式系统，确保样式一致性
    const cellStyle = { ...this.styles.default, fs: 9, horizontalAlign: 'left', verticalAlign: 'middle' }
    const numberStyle = { ...this.styles.default, fs: 9, horizontalAlign: 'center', verticalAlign: 'middle' }
    const priceStyle = { ...this.styles.default, fs: 9, horizontalAlign: 'right', verticalAlign: 'middle' }
    console.log('🔍 [DEBUG] addDataRows - 数据行样式配置完成');

        items.forEach((item, index) => {
          console.log(`🔍 [DEBUG] addDataRows - 处理第${index + 1}项数据，当前行: ${currentRow}`);
          console.log(`🔍 [DEBUG] addDataRows - 项目详情:`, item);

      // 修复数据行布局：8列布局，与表头对应
      const cells = [
        { col: 0, value: index + 1, style: numberStyle, desc: 'NO' },
        { col: 1, value: item.questionName || '', style: cellStyle, desc: '服务', span: 2 },  // 占用两列（1-2）
        { col: 3, value: item.questionComment || '', style: cellStyle, desc: 'სამღებრო სამუშაოები', span: 2 },  // 占用两列（3-4）
        { col: 5, value: item.amount || 0, style: priceStyle, desc: '费用' },
        { col: 6, value: item.remark || '', style: cellStyle, desc: '备注', span: 2 }  // 占用两列（6-7）
      ];

      cells.forEach(cellInfo => {
        const cell = this.createCell(currentRow, cellInfo.col, cellInfo.value, cellInfo.style);
        cellData.push(cell);
        console.log(`🔍 [DEBUG] addDataRows - 添加数据单元格: 行${currentRow}, 列${cellInfo.col}, 值"${cellInfo.value}", 描述"${cellInfo.desc}", 单元格:`, cell);
        
        // 如果字段需要跨列，添加合并配置
        if (cellInfo.span && cellInfo.span > 1) {
          this.addDynamicMerge(`data_${currentRow}_${cellInfo.col}`, { 
            r: currentRow, 
            c: cellInfo.col, 
            rs: 1, 
            cs: cellInfo.span 
          });
          console.log(`🔍 [DEBUG] addDataRows - 添加数据合并: 行${currentRow}, 列${cellInfo.col}, 跨${cellInfo.span}列`);
        }
      });

      // 使用8列布局，部分字段跨列显示
      console.log(`🔍 [DEBUG] addDataRows - 第${index + 1}项数据处理完成，当前行: ${currentRow}`);

      currentRow++
    })

    console.log(`🔍 [DEBUG] addDataRows - 所有数据行添加完成，最终行: ${currentRow}`);
    return currentRow
  }

  /**
   * 添加小计行
   * @param {Array} cellData 单元格数据数组
   * @param {number} currentRow 当前行
   * @param {object} group 分组数据
   * @returns {number} 下一行行号
   */
  addSubtotalRow(cellData, currentRow, group) {
    const subtotal = group.items.reduce((sum, item) =>
      sum + (parseFloat(item.amount) || 0), 0
    )

    // 使用基类的小计样式，确保样式一致性
    const subtotalLabelStyle = {
      ...this.styles.subtotal,
      fs: 9,
      horizontalAlign: 'right'  // 小计标签右对齐
    }
    const subtotalEmptyStyle = {
      ...this.styles.subtotal,
      fs: 9
    }
    const priceStyle = {
      ...this.styles.subtotal,
      fs: 9,
      horizontalAlign: 'right'  // 金额右对齐
    }
    // 小计行样式

    // 修复：小计行使用正确的8列布局，与表头对应
    cellData.push(this.createCell(currentRow, 0, `${group.label}小计：`, subtotalLabelStyle))  // 第0列：小计标签
    cellData.push(this.createCell(currentRow, 1, '', subtotalEmptyStyle))                      // 第1列：空
    cellData.push(this.createCell(currentRow, 2, '', subtotalEmptyStyle))                      // 第2列：空
    cellData.push(this.createCell(currentRow, 3, '', subtotalEmptyStyle))                      // 第3列：空
    cellData.push(this.createCell(currentRow, 4, '', subtotalEmptyStyle))                      // 第4列：空
    cellData.push(this.createCell(currentRow, 5, subtotal.toFixed(2), priceStyle))             // 第5列：费用
    cellData.push(this.createCell(currentRow, 6, '', subtotalEmptyStyle))                      // 第6列：空
    cellData.push(this.createCell(currentRow, 7, '', subtotalEmptyStyle))                      // 第7列：空

    // 添加小计行合并配置：标签列跨前5列（0-4），金额在第5列，备注列跨后2列（6-7）
    this.addDynamicMerge(`subtotal_${group.label}_${currentRow}_0_4`, { r: currentRow, c: 0, rs: 1, cs: 5 })  // 小计标签跨5列（0-4列）
    this.addDynamicMerge(`subtotal_${group.label}_${currentRow}_6_7`, { r: currentRow, c: 6, rs: 1, cs: 2 })  // 备注区域跨2列（6-7列）

    return currentRow + 1
  }

  /**
   * 去重处理订单详情
   * @param {Array} orderDetailList 订单详情列表
   * @returns {Array} 去重后的列表
   */
  removeDuplicateItems(orderDetailList) {
    const seen = new Set()
    return orderDetailList.filter(item => {
      // 适配旧数据结构：使用questionName、amount、questionComment作为去重键
      // 兼容新数据结构：如果存在itemName等字段也支持
      const questionName = item.questionName || item.itemName || ''
      const amount = item.amount || item.unitPrice || 0
      const comment = item.questionComment || item.specification || ''
      const key = `${questionName}_${amount}_${comment}`

      if (seen.has(key)) {
        // 发现重复项目，已过滤
        return false
      }
      seen.add(key)
      return true
    })
  }

  /**
   * 按服务类型分组订单详情
   * @param {Array} orderDetailList 订单详情列表
   * @param {Array} serviceTypeDict 服务类型字典
   * @returns {object} 分组结果
   */
  groupOrderDetailsByType(orderDetailList, serviceTypeDict) {
    const groups = {}  // 修复：使用普通对象而不是Map
    // 开始分组处理

    this.initializeGroups(groups, serviceTypeDict)

    orderDetailList.forEach(item => {
      const typeKey = item.questionType || item.serviceType || 'other'
      this.addItemToGroup(groups, typeKey, item, serviceTypeDict)
    })

    // 分组完成

    return Object.values(groups)  // 修复：使用Object.values而不是Array.from
  }

  /**
   * 初始化分组
   * @param {object} groups 分组对象
   * @param {Array} serviceTypeDict 服务类型字典
   */
  initializeGroups(groups, serviceTypeDict) {
    // 确保 serviceTypeDict 是数组
    if (!Array.isArray(serviceTypeDict)) {
      // serviceTypeDict 不是数组，使用空数组
      serviceTypeDict = []
    }

    if (serviceTypeDict.length === 0) {
      // serviceTypeDict 为空，将动态创建分组
      return
    }

    // 预定义分组初始化
    if (Array.isArray(serviceTypeDict)) {
      serviceTypeDict.forEach(type => {
        groups[type.value] = {  // 修复：使用普通对象赋值而不是Map的set方法
          label: type.label,
          items: [],
          totalAmount: 0
        }
      })
    }
  }

  /**
   * 添加项目到分组
   * @param {object} groups 分组对象
   * @param {string} typeKey 类型键
   * @param {object} item 项目
   * @param {Array} serviceTypeDict 服务类型字典
   */
  addItemToGroup(groups, typeKey, item, serviceTypeDict) {
    if (!groups[typeKey]) {
      let typeInfo = null
      if (Array.isArray(serviceTypeDict) && serviceTypeDict.length > 0) {
        typeInfo = serviceTypeDict.find(t => t && t.key === typeKey)
      }

      groups[typeKey] = {
        label: typeInfo ? typeInfo.label : (typeKey === 'other' ? '其他服务' : `未知类型(${typeKey})`),
        items: [],
        totalAmount: 0
      }
      // 动态创建分组
    }

    if (item) {
      groups[typeKey].items.push(item)
      groups[typeKey].totalAmount += parseFloat(item.amount) || 0
      // 适配旧数据结构：使用questionName字段
      const itemName = item.questionName || item.name || '未知项目'
      // 添加项目到分组
    }
  }

  /**
   * 创建汇总区域
   * @param {number} startRow 起始行
   * @param {object} formData 表单数据
   * @param {Array} cellData 单元格数据数组
   * @returns {number} 下一行行号
   */
  createSummarySection(startRow, formData, cellData) {
    // 将三个金额信息分布在两行中，避免合并冲突
    // 第一行：总金额和折后金额
    cellData.push(this.createCell(startRow, 0, `სულ总额: ${formData.totalAmount || 0}`))
    cellData.push(this.createCell(startRow, 4, `ფასდ/შემდეგ(%) 折后金额: ${formData.discountAmount || 0}`))

    // 第二行：预收金额和余额
    cellData.push(this.createCell(startRow + 1, 0, `ავანსი预付: ${formData.advancePayment || 0}`))
    cellData.push(this.createCell(startRow + 1, 4, `ნაშთი余额: ${formData.balance || 0}`))

    // 第一行合并配置 - 避免重叠
    // 总金额（第0-3列）
    this.addDynamicMerge(`summary_${startRow}_0_3`, { r: startRow, c: 0, rs: 1, cs: 4 })
    // 折后金额（第4-7列）
    this.addDynamicMerge(`summary_${startRow}_4_7`, { r: startRow, c: 4, rs: 1, cs: 4 })

    // 第二行合并配置
    // 预收金额（第0-3列）
    this.addDynamicMerge(`summary_${startRow + 1}_0_3`, { r: startRow + 1, c: 0, rs: 1, cs: 4 })
    // 余额（第4-7列）
    this.addDynamicMerge(`summary_${startRow + 1}_4_7`, { r: startRow + 1, c: 4, rs: 1, cs: 4 })

    return startRow + 2
  }

  /**
   * 创建签字区域
   * @param {number} startRow 起始行
   * @param {Array} cellData 单元格数据数组
   * @param {object} alignmentConfig 对齐配置 {ownerAlign: 0|1|2, staffAlign: 0|1|2, phoneAlign: 0|1|2}
   * @returns {number} 下一行行号
   */
  createSignatureSection(startRow, cellData, alignmentConfig = {}) {
    // 创建签字区域

    const alignment = { ...defaultAlignment, ...alignmentConfig }

    // 车主签字行 - 跨整行显示，可配置对齐方式
    const alignmentMap = ['left', 'center', 'right']
    cellData.push(this.createCell(startRow, 0, SIGNATURE_TEXTS.OWNER_SIGNATURE, {
      horizontalAlign: alignmentMap[alignment.staffAlign] || 'center',
      verticalAlign: 'middle',
      fs: 9
    }))

    // 接单人签字行 - 跨6列显示，可配置对齐方式
    cellData.push(this.createCell(startRow + 1, 0, SIGNATURE_TEXTS.STAFF_SIGNATURE, {
      horizontalAlign: alignmentMap[alignment.staffAlign] || 'center',
      verticalAlign: 'middle',
      fs: 9
    }))

    // 电话号码 - 可配置对齐方式
    cellData.push(this.createCell(startRow + 1, 7, SIGNATURE_TEXTS.PHONE_NUMBER, {
      horizontalAlign: alignmentMap[alignment.phoneAlign] || 'right',
      verticalAlign: 'middle',
      fs: 9
    }))

    // 车主签字行跨整行(8列)
    this.addDynamicMerge(`${startRow}_0_${startRow}_7`, { r: startRow, c: 0, rs: 1, cs: 8 })

    // 接单人签字行跨6列
    this.addDynamicMerge(`${startRow + 1}_0_${startRow + 1}_5`, { r: startRow + 1, c: 0, rs: 1, cs: 6 })

    return startRow + 2
  }

  /**
   * 创建公司信息区域
   * @param {number} startRow 起始行
   * @param {Array} cellData 单元格数据数组
   * @returns {number} 下一行行号
   */
  createCompanyInfoSection(startRow, cellData) {
    // 创建公司信息区域
    // 公司名称和日期
    cellData.push(this.createCell(startRow, 5, 'European Mazda Automobile Trading Co., Ltd.', { horizontalAlign: 'right', verticalAlign: 'middle' }))
    cellData.push(this.createCell(startRow + 1, 5, this.formatWesternDate(new Date()), { horizontalAlign: 'right', verticalAlign: 'middle' }))

    // 公司名称 跨3列 (从第5列开始)
    this.addDynamicMerge(`${startRow}_5_${startRow}_7`, { r: startRow, c: 5, rs: 1, cs: 3 })

    // 日期 跨3列 (从第5列开始)
    this.addDynamicMerge(`${startRow + 1}_5_${startRow + 1}_7`, { r: startRow + 1, c: 5, rs: 1, cs: 3 })

    return startRow + 2
  }

  /**
   * 获取工作表配置
   * @param {number} totalRows 总行数
   * @returns {object} 工作表配置
   */
  getSheetConfig(totalRows = 50) {
    return {
      // Univer工作表基本配置
      id: 'sheet-01',
      name: '验收单据',
      rowCount: totalRows,
      columnCount: 8,
      defaultRowHeight: 25,
      defaultColumnWidth: 100,

      // 行高配置 - Univer格式
      rowData: this.getUniverRowData(totalRows),

      // 列宽配置 - Univer格式
      columnData: this.getUniverColumnData(),

      // 合并单元格配置 - Univer格式
      mergeData: this.getUniverMergeData(),

      // 其他Univer配置
      showGridLines: 1,
      rightToLeft: 0,
      hidden: 0,
      tabColor: '',
      zoomRatio: 1
    }
  }

  /**
   * 获取Univer格式的行高配置
   * @param {number} totalRows 总行数
   * @returns {object} Univer行高配置
   */
  getUniverRowData(totalRows = 50) {
    const rowData = {}

    // 标题行设置较大行高
    rowData[0] = { h: 35, hd: 0 }  // 主标题行
    rowData[1] = { h: 30, hd: 0 }  // 副标题行
    rowData[2] = { h: 25, hd: 0 }  // 中文标题行

    // 其他行使用默认行高
    for (let i = 3; i < totalRows; i++) {
      rowData[i] = { h: 25, hd: 0 }
    }

    return rowData
  }

  /**
   * 获取Univer格式的列宽配置
   * @returns {object} Univer列宽配置
   */
  getUniverColumnData() {
    return {
      0: { w: 50, hd: 0 },   // A列：NO列，较窄
      1: { w: 150, hd: 0 },  // B列：服务列
      2: { w: 180, hd: 0 },  // C列：სამღებრო სამუშაოები列
      3: { w: 120, hd: 0 },  // D列：费用列
      4: { w: 150, hd: 0 },  // E列：备注列
      5: { w: 100, hd: 0 },  // F列：预留
      6: { w: 100, hd: 0 },  // G列：预留
      7: { w: 100, hd: 0 }   // H列：预留
    }
  }

  /**
   * 创建空单元格
   * @param {number} row 行号
   * @param {number} col 列号
   * @returns {object} 空单元格对象
   */
  createEmptyCell(row, col) {
    return {
      r: row,
      c: col,
      v: {
        v: '',
        ct: { fa: 'General', t: 'g' },
        m: ''
      }
    };
  }

  /**
   * 智能添加边框（根据单元格在区域中的位置）
   * @param {object} cell 单元格对象
   * @param {number} row 当前行
   * @param {number} col 当前列
   * @param {number} startRow 区域起始行
   * @param {number} endRow 区域结束行
   * @param {number} startCol 区域起始列
   * @param {number} endCol 区域结束列
   * @param {object} borderStyle 边框样式
   * @param {Array} borderTypes 边框类型数组
   */
  addSmartBorders(cell, row, col, startRow, endRow, startCol, endCol, borderStyle, borderTypes) {
    if (!cell.v) cell.v = {};

    // 顶部边框
    if (borderTypes.includes('top') && row === startRow) {
      cell.v.borderTop = borderStyle;
    }

    // 底部边框
    if (borderTypes.includes('bottom') && row === endRow) {
      cell.v.borderBottom = borderStyle;
    }

    // 左侧边框
    if (borderTypes.includes('left') && col === startCol) {
      cell.v.borderLeft = borderStyle;
    }

    // 右侧边框
    if (borderTypes.includes('right') && col === endCol) {
      cell.v.borderRight = borderStyle;
    }

    // 内部网格线
    if (borderTypes.includes('inner')) {
      if (row > startRow) cell.v.borderTop = borderStyle;
      if (row < endRow) cell.v.borderBottom = borderStyle;
      if (col > startCol) cell.v.borderLeft = borderStyle;
      if (col < endCol) cell.v.borderRight = borderStyle;
    }

    // 全边框（外边框 + 内部网格线）
    if (borderTypes.includes('all')) {
      cell.v.borderTop = borderStyle;
      cell.v.borderBottom = borderStyle;
      cell.v.borderLeft = borderStyle;
      cell.v.borderRight = borderStyle;
    }
  }

  /**
   * 动态添加边框到指定区域（增强版）
   * @param {Array} cellData 单元格数据数组
   * @param {Object} borderConfig 边框配置
   */
  addDynamicBorders(cellData, borderConfig) {
    const {
      startRow,
      endRow,
      startCol,
      endCol,
      borderStyle = {
        color: '#000000',
        width: 1,
        style: 'solid'
      },
      borderTypes = ['all'], // 默认添加全边框
      regionType = 'default' // 区域类型，用于调试
    } = borderConfig;

    console.log(`🔍 [DEBUG] 动态添加边框到${regionType}区域: 行${startRow}-${endRow}, 列${startCol}-${endCol}, 边框类型: ${borderTypes.join(',')}`);

    for (let row = startRow; row <= endRow; row++) {
      for (let col = startCol; col <= endCol; col++) {
        // 查找现有单元格
        let existingCell = cellData.find(cell => cell.r === row && cell.c === col);

        if (!existingCell) {
          // 如果单元格不存在，创建一个空单元格
          existingCell = this.createEmptyCell(row, col);
          cellData.push(existingCell);
        }

        // 确保v对象存在
        if (!existingCell.v) {
          existingCell.v = {
            v: existingCell.v?.v || '',
            ct: existingCell.v?.ct || { fa: 'General', t: 'g' },
            m: existingCell.v?.m || ''
          };
        }

        // 智能添加边框
        this.addSmartBorders(existingCell, row, col, startRow, endRow, startCol, endCol, borderStyle, borderTypes);

        console.log(`🔍 [DEBUG] 为${regionType}区域单元格(${row},${col})添加边框，类型: ${borderTypes.join(',')}`);
      }
    }

    console.log(`✅ [SUCCESS] ${regionType}区域边框添加完成`);
  }

  /**
   * 硬编码添加指定区域的边框（保留向后兼容性）
   * @param {Array} cellData 单元格数据数组
   * @param {number} startRow 起始行
   * @param {number} endRow 结束行
   * @param {number} startCol 起始列
   * @param {number} endCol 结束列
   */
  addBorderToRange(cellData, startRow, endRow, startCol, endCol) {
    // 使用新的动态边框添加方法
    this.addDynamicBorders(cellData, {
      startRow,
      endRow,
      startCol,
      endCol,
      borderTypes: ['all'],
      regionType: 'legacy'
    });
  }

  /**
   * 获取Univer格式的合并单元格配置
   * @returns {Array} Univer合并单元格配置数组
   */
  getUniverMergeData() {
    const mergeData = []

    // 图片合并
    mergeData.push({
      startRow: 0,
      endRow: 0,
      startColumn: 0,
      endColumn: 7
    })
    // 标题行合并
    mergeData.push({
      startRow: 1,
      endRow: 1,
      startColumn: 0,
      endColumn: 7
    })

    // 第2行（副标题）合并
    mergeData.push({
      startRow: 2,
      endRow: 2,
      startColumn: 0,
      endColumn: 7
    })

    // 基本信息区域静态合并配置
    // 第一行：时间标签和值、工单编号标签和值
    mergeData.push({ startRow: 3, endRow: 3, startColumn: 0, endColumn: 1 })  // 时间标签跨两列
    mergeData.push({ startRow: 3, endRow: 3, startColumn: 2, endColumn: 3 })  // 时间值跨两列
    mergeData.push({ startRow: 3, endRow: 3, startColumn: 4, endColumn: 5 })  // 工单编号标签跨两列
    mergeData.push({ startRow: 3, endRow: 3, startColumn: 6, endColumn: 7 })  // 工单编号值跨两列

    // 第二行：车主标签和值、品牌标签和值
    mergeData.push({ startRow: 4, endRow: 4, startColumn: 0, endColumn: 1 })  // 车主标签跨两列
    mergeData.push({ startRow: 4, endRow: 4, startColumn: 2, endColumn: 3 })  // 车主值跨两列
    mergeData.push({ startRow: 4, endRow: 4, startColumn: 4, endColumn: 5 })  // 品牌标签跨两列
    mergeData.push({ startRow: 4, endRow: 4, startColumn: 6, endColumn: 7 })  // 品牌值跨两列

    // 合并配置分析

    // 添加动态合并配置
    if (this.dynamicMerges && Array.isArray(this.dynamicMerges)) {
      console.log('🔍 [DEBUG] 动态合并配置数量:', this.dynamicMerges.length)

      this.dynamicMerges.forEach((mergeItem) => {
        const merge = mergeItem.config
        const mergeConfig = {
          startRow: merge.r,
          endRow: merge.r + merge.rs - 1,
          startColumn: merge.c,
          endColumn: merge.c + merge.cs - 1
        }

        // 检查是否与现有合并配置冲突
        const conflictingMerge = mergeData.find(existing =>
          !(mergeConfig.endRow < existing.startRow ||
            mergeConfig.startRow > existing.endRow ||
            mergeConfig.endColumn < existing.startColumn ||
            mergeConfig.startColumn > existing.endColumn)
        )

        if (conflictingMerge) {
          console.warn('⚠️ [WARNING] 发现合并配置冲突:', {
            key: mergeItem.key,
            new: mergeConfig,
            existing: conflictingMerge
          })
        } else {
          console.log('✅ [DEBUG] 添加合并配置:', mergeItem.key, mergeConfig)
        }

        mergeData.push(mergeConfig)
      })
    }

    // 最终合并配置
    return mergeData
  }

  /**
   * 获取合并单元格配置（保留兼容性）
   * @returns {object} 合并配置
   */
  getMergeConfig() {
    const mergeConfig = {
      // 标题行合并
      '0_0_0_7': { r: 0, c: 0, rs: 1, cs: 8 },
      '1_0_1_7': { r: 1, c: 0, rs: 1, cs: 8 },
      '2_0_2_7': { r: 2, c: 0, rs: 1, cs: 8 },

      // 基本信息区域标签和值合并配置（标签和值都跨两列显示）
      // 第一行：时间标签和值、工单编号标签和值
      '3_0_3_1': { r: 3, c: 0, rs: 1, cs: 2 },  // 时间标签跨两列
      '3_2_3_3': { r: 3, c: 2, rs: 1, cs: 2 },  // 时间值跨两列
      '3_4_3_5': { r: 3, c: 4, rs: 1, cs: 2 },  // 工单编号标签跨两列
      '3_6_3_7': { r: 3, c: 6, rs: 1, cs: 2 },  // 工单编号值跨两列

      // 第二行：车主标签和值、品牌标签和值
      '4_0_4_1': { r: 4, c: 0, rs: 1, cs: 2 },  // 车主标签跨两列
      '4_2_4_3': { r: 4, c: 2, rs: 1, cs: 2 },  // 车主值跨两列
      '4_4_4_5': { r: 4, c: 4, rs: 1, cs: 2 },  // 品牌标签跨两列
      '4_6_4_7': { r: 4, c: 6, rs: 1, cs: 2 },  // 品牌值跨两列

      // 第三行：电话（单列），颜色（单列），车牌号标签（跨两列E7F7），车牌号值（G7单列）

      // 第四行：进厂日期标签和值、取车日期标签和值（已改为动态生成）
      // 注意：进厂日期和取车日期的合并配置现在通过addDynamicMerge方法动态添加
    }

    // 添加动态合并配置
    if (this.dynamicMerges) {
      this.dynamicMerges.forEach(merge => {
        mergeConfig[merge.key] = merge.config
      })
    }

    return mergeConfig
  }

  /**
   * 为单元格添加边框样式
   * @param {object} cellData 单元格数据
   * @param {object} borderStyle 边框样式配置
   * @returns {object} 带边框的单元格数据
   */
  addBorderToCell(cellData, borderStyle = {}) {
    const defaultBorder = {
      style: 2,
      color: '#333333'
    }

    const border = { ...defaultBorder, ...borderStyle }

    if (!cellData.v) cellData.v = {}

    cellData.v.t = border
    cellData.v.l = border
    cellData.v.r = border
    cellData.v.b = border

    return cellData
  }



  /**
   * 获取边框样式预设
   * @param {string} styleType 样式类型
   * @returns {object} 边框样式对象
   */
  getBorderStylePreset(styleType = 'default') {
    const presets = {
      default: {
        color: '#000000',
        width: 1,
        style: 'solid'
      },
      thick: {
        color: '#000000',
        width: 2,
        style: 'solid'
      },
      thin: {
        color: '#000000',
        width: 0.5,
        style: 'solid'
      },
      dashed: {
        color: '#000000',
        width: 1,
        style: 'dashed'
      },
      dotted: {
        color: '#000000',
        width: 1,
        style: 'dotted'
      },
      tableHeader: {
        color: '#FFFFFF',
        width: 1,
        style: 'solid'
      },
      tableData: {
        color: '#CCCCCC',
        width: 1,
        style: 'solid'
      }
    };

    return presets[styleType] || presets.default;
  }

  /**
   * 为数据添加边框（增强版）
   * @param {array} cellData 单元格数据数组
   * @param {number} dynamicTableEndRow 动态表格结束行号
   * @param {object} options 边框选项
   */
  addBordersToData(cellData, dynamicTableEndRow, options = {}) {
    const {
      basicInfoBorderStyle = 'default',
      tableBorderStyle = 'default',
      customBorderConfigs = []
    } = options;

    console.log(`🔍 [DEBUG] 开始添加边框，动态表格结束行: ${dynamicTableEndRow}`);

    // 基本信息区域边框（第3-6行）
    this.addDynamicBorders(cellData, {
      startRow: 3,
      endRow: 6,
      startCol: 0,
      endCol: 7,
      borderStyle: this.getBorderStylePreset(basicInfoBorderStyle),
      borderTypes: ['all'],
      regionType: '基本信息区域'
    });

    // 服务项目表格区域边框（第8行到动态表格结束行）
    if (dynamicTableEndRow >= 8) {
      this.addDynamicBorders(cellData, {
        startRow: 8,
        endRow: dynamicTableEndRow,
        startCol: 0,
        endCol: 7,
        borderStyle: this.getBorderStylePreset(tableBorderStyle),
        borderTypes: ['all'],
        regionType: '服务项目表格区域'
      });
    }

    // 应用自定义边框配置
    customBorderConfigs.forEach((config, index) => {
      console.log(`🔍 [DEBUG] 应用自定义边框配置 ${index + 1}:`, config);
      this.addDynamicBorders(cellData, {
        ...config,
        regionType: config.regionType || `自定义区域${index + 1}`
      });
    });

    console.log(`✅ [SUCCESS] 所有边框添加完成`);
  }

  /**
   * 为表格区域添加特殊边框样式
   * @param {Array} cellData 单元格数据数组
   * @param {number} headerRow 表头行
   * @param {number} startDataRow 数据开始行
   * @param {number} endDataRow 数据结束行
   * @param {number} startCol 起始列
   * @param {number} endCol 结束列
   * @param {object} options 边框选项
   */
  addTableBorders(cellData, headerRow, startDataRow, endDataRow, startCol, endCol, options = {}) {
    const {
      headerBorderStyle = 'tableHeader',
      dataBorderStyle = 'tableData',
      outerBorderStyle = 'thick'
    } = options;

    // 表头边框
    this.addDynamicBorders(cellData, {
      startRow: headerRow,
      endRow: headerRow,
      startCol,
      endCol,
      borderStyle: this.getBorderStylePreset(headerBorderStyle),
      borderTypes: ['all'],
      regionType: '表头区域'
    });

    // 数据区域边框
    if (endDataRow >= startDataRow) {
      this.addDynamicBorders(cellData, {
        startRow: startDataRow,
        endRow: endDataRow,
        startCol,
        endCol,
        borderStyle: this.getBorderStylePreset(dataBorderStyle),
        borderTypes: ['all'],
        regionType: '表格数据区域'
      });
    }

    // 外边框加粗
    this.addDynamicBorders(cellData, {
      startRow: headerRow,
      endRow: endDataRow,
      startCol,
      endCol,
      borderStyle: this.getBorderStylePreset(outerBorderStyle),
      borderTypes: ['top', 'bottom', 'left', 'right'],
      regionType: '表格外边框'
    });
  }

  /**
   * 为选中区域添加高亮边框
   * @param {Array} cellData 单元格数据数组
   * @param {number} startRow 起始行
   * @param {number} endRow 结束行
   * @param {number} startCol 起始列
   * @param {number} endCol 结束列
   * @param {string} highlightColor 高亮颜色
   */
  addHighlightBorders(cellData, startRow, endRow, startCol, endCol, highlightColor = '#FF0000') {
    const highlightBorderStyle = {
      color: highlightColor,
      width: 2,
      style: 'solid'
    };

    this.addDynamicBorders(cellData, {
      startRow,
      endRow,
      startCol,
      endCol,
      borderStyle: highlightBorderStyle,
      borderTypes: ['top', 'bottom', 'left', 'right'],
      regionType: '高亮选中区域'
    });
  }

  /**
   * 使用边框管理器批量应用边框
   * @param {Array} cellData 单元格数据数组
   * @param {number} dynamicTableEndRow 动态表格结束行号
   */
  applyBordersWithManager(cellData, dynamicTableEndRow) {
    // 清除之前的注册
    this.borderManager.clearAllRegions();

    // 注册基本信息区域边框
    this.borderManager.registerBorderRegion(
      '基本信息区域',
      3, 6, 0, 7,
      {
        borderStyle: this.getBorderStylePreset('default'),
        borderTypes: ['all'],
        priority: 1
      }
    );

    // 注册服务项目表格区域边框
    if (dynamicTableEndRow >= 8) {
      this.borderManager.registerBorderRegion(
        '服务项目表格区域',
        8, dynamicTableEndRow, 0, 7,
        {
          borderStyle: this.getBorderStylePreset('default'),
          borderTypes: ['all'],
          priority: 1
        }
      );
    }

    // 应用所有注册的边框
    this.borderManager.applyAllBorders(cellData, this);

    console.log(`✅ [SUCCESS] 边框管理器应用完成，共处理 ${this.borderManager.getRegionCount()} 个区域`);
  }

  /**
   * 获取边框配置
   * @param {number} totalRows 总行数
   * @returns {array} 边框配置数组
   */
  getBorderConfig(totalRows = 50) {
    if (!totalRows || typeof totalRows !== 'number' || totalRows < 1) {
      console.warn('⚠️ [WARNING] totalRows参数无效，使用默认值50:', totalRows)
      totalRows = 50
    }

    const borderInfo = []
    
    // 基本信息区域边框 (第4-7行，A-H列)
    borderInfo.push({
      rangeType: 'range',
      borderType: 'border-all',
      style: 1,
      color: '#000000',
      range: [{
        row: [3, 6],
        column: [0, 7]
      }]
    })
    
    // 服务项目表格区域边框 (第9行到totalRows-1行，A-H列)
    if (totalRows > 8) {
      borderInfo.push({
        rangeType: 'range',
        borderType: 'border-all',
        style: 1,
        color: '#000000',
        range: [{
          row: [8, totalRows - 1],
          column: [0, 7]
        }]
      })
    }
    
    console.log('✅ 边框配置生成完成，配置数量:', borderInfo.length)
    return borderInfo
  }

}

export default AcceptReceiptUniversalConverter
